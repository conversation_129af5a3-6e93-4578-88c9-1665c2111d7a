/**
 * Destilink Framework API Documentation Generator
 * Extracts API information from Spring controllers and generates documentation
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  rootDir: path.resolve(__dirname, '../..'),
  outputDir: path.resolve(__dirname, 'output'),
  controllerPattern: /(@RestController|@Controller).*?class\s+(\w+)/gs,
  endpointPattern: /@(GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping|RequestMapping)(?:\s*\(\s*(?:value\s*=\s*)?["']([^"']*)["'])?/g,
  ignorePatterns: ['node_modules', 'target', '.git', 'bak', 'temp']
};

// Find all Java files
function findJavaFiles(dir) {
  const files = [];
  
  function scan(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        if (config.ignorePatterns.some(pattern => item.includes(pattern))) {
          continue;
        }
        
        const itemPath = path.join(currentDir, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
          scan(itemPath);
        } else if (stats.isFile() && itemPath.endsWith('.java')) {
          files.push(itemPath);
        }
      }
    } catch (error) {
      console.error(`Error scanning ${currentDir}: ${error.message}`);
    }
  }
  
  scan(dir);
  return files;
}

// Extract API information from a Java file
function extractApiInfo(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const packageMatch = content.match(/package\s+([\w.]+);/);
  const packageName = packageMatch ? packageMatch[1] : 'unknown';
  
  const controllers = [];
  let controllerMatch;
  
  // Find all controllers in the file
  const controllerRegex = new RegExp(config.controllerPattern);
  while ((controllerMatch = controllerRegex.exec(content)) !== null) {
    const controllerType = controllerMatch[1];
    const controllerName = controllerMatch[2];
    const controllerContent = controllerMatch[0];
    
    // Find base path from RequestMapping if present
    let basePath = '';
    const basePathMatch = controllerContent.match(/@RequestMapping(?:\s*\(\s*(?:value\s*=\s*)?["']([^"']*)["'])?/);
    if (basePathMatch && basePathMatch[1]) {
      basePath = basePathMatch[1];
    }
    
    // Find all endpoints in this controller
    const endpoints = [];
    const endpointRegex = new RegExp(config.endpointPattern);
    let endpointMatch;
    let controllerContentCopy = controllerContent;
    
    while ((endpointMatch = endpointRegex.exec(controllerContentCopy)) !== null) {
      const httpMethod = endpointMatch[1].replace('Mapping', '').toUpperCase();
      let path = endpointMatch[2] || '';
      
      // Skip the class-level RequestMapping
      if (endpointMatch[0].includes('@RequestMapping') && path === basePath) {
        continue;
      }
      
      // Combine base path and endpoint path
      if (basePath && path) {
        path = `${basePath}${path.startsWith('/') ? path : '/' + path}`;
      } else if (basePath) {
        path = basePath;
      }
      
      // Find method name
      const methodSection = controllerContentCopy.substring(endpointMatch.index);
      const methodMatch = methodSection.match(/\s+(\w+)\s*\(/);
      const methodName = methodMatch ? methodMatch[1] : 'unknown';
      
      endpoints.push({
        method: httpMethod,
        path: path || '/',
        methodName
      });
    }
    
    controllers.push({
      name: controllerName,
      type: controllerType,
      package: packageName,
      basePath,
      endpoints,
      file: filePath.replace(config.rootDir, '')
    });
  }
  
  return controllers;
}

// Generate Markdown documentation
function generateMarkdown(controllers) {
  let markdown = '# Destilink Framework API Documentation\n\n';
  
  // Group controllers by package
  const packageGroups = {};
  for (const controller of controllers) {
    if (!packageGroups[controller.package]) {
      packageGroups[controller.package] = [];
    }
    packageGroups[controller.package].push(controller);
  }
  
  // Generate TOC
  markdown += '## Table of Contents\n\n';
  for (const packageName of Object.keys(packageGroups).sort()) {
    markdown += `- [${packageName}](#${packageName.replace(/\./g, '')})\n`;
    for (const controller of packageGroups[packageName]) {
      markdown += `  - [${controller.name}](#${controller.name.toLowerCase()})\n`;
    }
  }
  
  markdown += '\n';
  
  // Generate content
  for (const packageName of Object.keys(packageGroups).sort()) {
    markdown += `## ${packageName}\n\n`;
    
    for (const controller of packageGroups[packageName]) {
      markdown += `### ${controller.name}\n\n`;
      markdown += `**Type:** ${controller.type.replace('@', '')}\n\n`;
      markdown += `**File:** \`${controller.file}\`\n\n`;
      
      if (controller.basePath) {
        markdown += `**Base Path:** \`${controller.basePath}\`\n\n`;
      }
      
      if (controller.endpoints.length > 0) {
        markdown += '#### Endpoints\n\n';
        
        for (const endpoint of controller.endpoints) {
          markdown += `##### ${endpoint.methodName}\n\n`;
          markdown += `\`${endpoint.method} ${endpoint.path}\`\n\n`;
        }
      }
      
      markdown += '---\n\n';
    }
  }
  
  return markdown;
}

// Generate HTML documentation
function generateHtml(controllers) {
  let html = `<!DOCTYPE html>
<html>
<head>
  <title>Destilink Framework API Documentation</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
    .container { max-width: 1200px; margin: 0 auto; }
    h1 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
    h2 { margin-top: 30px; color: #0066cc; }
    h3 { margin-top: 25px; color: #333; }
    h4 { margin-top: 20px; color: #666; }
    h5 { margin-top: 15px; color: #333; font-size: 1em; }
    .endpoint { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 15px; }
    .method { font-weight: bold; color: #0066cc; }
    .path { font-family: monospace; background-color: #eee; padding: 2px 5px; }
    .file { font-family: monospace; color: #666; }
    .toc { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Destilink Framework API Documentation</h1>
    
    <div class="toc">
      <h2>Table of Contents</h2>
      <ul>`;
  
  // Group controllers by package
  const packageGroups = {};
  for (const controller of controllers) {
    if (!packageGroups[controller.package]) {
      packageGroups[controller.package] = [];
    }
    packageGroups[controller.package].push(controller);
  }
  
  // Generate TOC
  for (const packageName of Object.keys(packageGroups).sort()) {
    const packageId = packageName.replace(/\./g, '');
    html += `
        <li>
          <a href="#${packageId}">${packageName}</a>
          <ul>`;
    
    for (const controller of packageGroups[packageName]) {
      const controllerId = `${packageId}-${controller.name.toLowerCase()}`;
      html += `
            <li><a href="#${controllerId}">${controller.name}</a></li>`;
    }
    
    html += `
          </ul>
        </li>`;
  }
  
  html += `
      </ul>
    </div>`;
  
  // Generate content
  for (const packageName of Object.keys(packageGroups).sort()) {
    const packageId = packageName.replace(/\./g, '');
    html += `
    <h2 id="${packageId}">${packageName}</h2>`;
    
    for (const controller of packageGroups[packageName]) {
      const controllerId = `${packageId}-${controller.name.toLowerCase()}`;
      html += `
    <h3 id="${controllerId}">${controller.name}</h3>
    <p><strong>Type:</strong> ${controller.type.replace('@', '')}</p>
    <p><strong>File:</strong> <span class="file">${controller.file}</span></p>`;
      
      if (controller.basePath) {
        html += `
    <p><strong>Base Path:</strong> <span class="path">${controller.basePath}</span></p>`;
      }
      
      if (controller.endpoints.length > 0) {
        html += `
    <h4>Endpoints</h4>`;
        
        for (const endpoint of controller.endpoints) {
          html += `
    <div class="endpoint">
      <h5>${endpoint.methodName}</h5>
      <p><span class="method">${endpoint.method}</span> <span class="path">${endpoint.path}</span></p>
    </div>`;
        }
      }
      
      html += `
    <hr>`;
    }
  }
  
  html += `
  </div>
</body>
</html>`;
  
  return html;
}

// Main function
function main() {
  console.log('Destilink Framework API Documentation Generator');
  console.log('=============================================');
  
  // Find Java files
  console.log('Scanning for Java files...');
  const javaFiles = findJavaFiles(config.rootDir);
  console.log(`Found ${javaFiles.length} Java files`);
  
  // Extract API information
  console.log('Extracting API information...');
  let allControllers = [];
  for (const file of javaFiles) {
    const controllers = extractApiInfo(file);
    allControllers = allControllers.concat(controllers);
  }
  console.log(`Found ${allControllers.length} controllers`);
  
  if (allControllers.length === 0) {
    console.log('No controllers found. Nothing to document.');
    return;
  }
  
  // Generate documentation
  console.log('Generating documentation...');
  const markdown = generateMarkdown(allControllers);
  const html = generateHtml(allControllers);
  
  // Write output files
  fs.writeFileSync(path.join(config.outputDir, 'api-documentation.md'), markdown);
  fs.writeFileSync(path.join(config.outputDir, 'api-documentation.html'), html);
  
  console.log('Documentation generated:');
  console.log(`- ${path.join(config.outputDir, 'api-documentation.md')}`);
  console.log(`- ${path.join(config.outputDir, 'api-documentation.html')}`);
}

// Run the program
if (require.main === module) {
  main();
}

module.exports = {
  extractApiInfo,
  generateMarkdown,
  generateHtml
};