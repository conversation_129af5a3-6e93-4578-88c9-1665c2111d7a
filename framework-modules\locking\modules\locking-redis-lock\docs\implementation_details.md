# Redis Locking Module: Final Implementation Details

## 1. Introduction

This document provides detailed implementation specifics for key components and mechanisms within the consolidated `locking-redis-lock` module. Understanding these details is crucial for developing, debugging, and effectively utilizing the module. This plan synthesizes the core implementation aspects from previous planning phases, with a strong emphasis on the **Async-First approach**, mandatory use of `redis-core` components, and strict adherence to framework guidelines.

**Crucially, all Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. These classes are currently primarily utilized by the locking module and can be further optimized if specific needs arise.**

## 2. Core Components and Responsibilities

The module is structured around several core components that collaborate to provide distributed locking functionality:

* `RedisLockAutoConfiguration` (Spring `@AutoConfiguration`):
  * The module's entry point for Spring Boot.
  * **Strictly adheres to no `@ComponentScan`** as per `/.amazonq/rules/guidelines.md`. All framework beans are explicitly defined via `@Bean` methods.
  * Conditionally enables the locking module based on the `destilink.fw.locking.redis.enabled` property.
  * Responsible for instantiating and wiring all shared service beans and the `LockBucketRegistry`.
  * Registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`. This aligns with framework guidelines for auto-configuration.

* `LockComponentRegistry` (Spring Bean):
  * A central registry injected into lock builders and other components.
  * Provides access to shared services: `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, and an `ObjectProvider<LockMonitor>`.
  * Simplifies dependency management for lock instances and internal services, promoting constructor injection as per guidelines.

* `RedisLockProperties` (Spring `@ConfigurationProperties`):
  * Binds global configurations from YAML files (e.g., `destilink.fw.locking.redis.*`).
  * Holds global default values for properties like `prefix` (deprecated), `leaseTime`, `retryInterval`, `unlockMessageListenerEnabled`, `stateKeyExpiration`, `uuidCacheTtlSeconds`, `healthIndicatorEnabled`, `lockOwnerSupplierBeanName`, and watchdog settings.
  * Watchdog settings include: `watchdogEnabled`, `watchdogMinLeaseTimeForActivation`, `watchdogScheduleFixedDelay`, and `watchdogMaxTtlForRenewalCheck`.
  * **Does not** define buckets via YAML; bucket configuration is programmatic.
  * Works in conjunction with `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java` to ensure consistent Redis client configuration.

* `LockBucketConfig` (Non-Spring managed POJO):
  * Holds the *resolved* and *effective* configuration for a specific lock bucket.
  * Instantiated by `LockBucketBuilder`, initialized with global defaults from `RedisLockProperties` and potentially from `RedisCoreProperties`.
  * Bucket-level defaults (e.g., default `leaseTime` for the bucket) can be overridden programmatically via `LockBucketBuilder`. Watchdog eligibility for locks within the bucket is determined by the configured `LockOwnerSupplier` and other global/instance conditions, not a direct `useWatchdog` flag on the bucket.

* `LockBucketRegistry` (Spring Bean):
  * The primary factory for initiating the lock creation process.
  * Provides the entry point to the fluent builder API (e.g., `builder(String bucketName)`).
  * Injects the `LockComponentRegistry` and an initialized `LockBucketConfig` (based on global defaults) into the `LockBucketBuilder`.

* Builder Chain (`LockBucketBuilder` -> `LockConfigBuilder` -> `AbstractLockTypeConfigBuilder` & Subclasses):
  * **`LockBucketBuilder`**: Configures bucket-name, scope (application vs. distributed), custom `LockOwnerSupplier`, and bucket-level default overrides for `leaseTime`, `retryInterval`, and `stateKeyExpiration`. The `LockOwnerSupplier` is key to determining watchdog eligibility.
  * **`LockConfigBuilder`**: Allows selection of the lock type (reentrant, state, read-write, etc.).
  * **`AbstractLockTypeConfigBuilder` & Subclasses (e.g., `ReentrantLockConfigBuilder`, `StateLockConfigBuilder`)**: Allow overriding instance-specific parameters (`leaseTime`, `retryInterval`) and type-specific parameters (e.g., `expectedState`). The `.build()` method constructs the concrete lock instance.

* `AbstractRedisLock` (Base Class for Locks):
  * Provides common asynchronous functionality: `lockAsync()`, `tryLockAsync()`, `unlockAsync()`, and `executeScriptAsync()` helper.
  * **The synchronous `lock()`, `tryLock()`, and `unlock()` methods from `java.util.concurrent.locks.Lock` are implemented as blocking wrappers around these asynchronous operations.** This ensures an Async-First approach while maintaining `Lock` interface compatibility.
  * Manages the core lock acquisition/release lifecycle, including interaction with `LockSemaphoreHolder` for waiting and `LockWatchdog` for lease extension.
  * Delegates Redis script execution to `RedisLockOperations` (obtained from `LockComponentRegistry`), which in turn uses `ClusterCommandExecutor`.
  * Defines abstract asynchronous methods like `tryAcquireLockScriptAsync()`, `releaseLockScriptAsync()` to be implemented by concrete lock types.

* Concrete Lock Implementations (e.g., `RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`, `RedisStampedLock`):
  * Extend `AbstractRedisLock` and **MUST implement the `AsyncLock` interface**.
  * Implement abstract asynchronous methods to provide specific Lua script names and arguments for their locking semantics, utilizing `CompletableFuture`s for all operations.
  * **`RedisReentrantLock`**: Manages reentrancy count and owner information within a **Redis Hash** associated with the lock key, ensuring distributed reentrancy. Includes asynchronous `fullyUnlockAsync()` method.
  * **`RedisStateLock`**: Adds state-specific asynchronous methods (`getStateAsync`, `updateStateAsync`), uses Lua for atomic state operations, and may use a response cache for idempotency.
  * **`RedisReadWriteLock`**: Contains inner `ReadLock` and `WriteLock`. These inner locks extend `AbstractRedisLock` and **MUST implement the `AsyncLock` interface**, providing asynchronous read and write lock operations.
  * **`RedisStampedLock`**: Extends `AbstractRedisLock` and **MUST implement the `AsyncLock` interface**, providing asynchronous stamped lock operations (write, read, optimistic read, conversions).

* `ScriptLoader` (Spring Bean):
  * Loads all necessary Lua scripts from the classpath (e.g., `src/main/resources/lua/`) during application startup (`@PostConstruct`).
  * Caches scripts (e.g., as `org.springframework.data.redis.core.script.RedisScript`) for efficient reuse by `RedisLockOperations`.

* `RedisLockOperations` (Spring Bean, typically `RedisLockOperationsImpl`):
  * Abstraction layer for Redis communication specific to lock operations.
  * **CRITICAL: Exclusively uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis command executions and Lua script invocations.** This ensures all Redis interactions are asynchronous and leverage the `redis-core` module as required.
  * **Centralized Idempotency Management**: This component is the single point of responsibility for generating unique `requestUuid` values for each logical lock operation and ensuring they are passed to all mutating Lua scripts. It generates a new `requestUuid` for each public method call (e.g., `tryLock`, `unlock`, `extendLease`) and uses the same UUID for all retry attempts of that single logical operation.
  * **Response Cache Integration**: Constructs response cache keys using the format `"<prefix>:<bucketName>:__resp_cache__:{<lockName>}:<requestUuid>"` and passes both the `requestUuid` and `responseCacheTtl` (from `RedisLockProperties`) to Lua scripts for centralized idempotency handling.
  * **Consistent Behavior**: All mutating Redis operations are protected by the centralized idempotency mechanism, ensuring consistent behavior across all lock types and operations.

* `DefaultLockOwnerSupplier` (Spring Bean):
  * Provides the default mechanism for generating unique owner IDs (e.g., combining application instance ID and thread ID). Can be replaced by a custom implementation.

* `RedisLockErrorHandler` (Spring Bean):
  * Centralizes logic for interpreting exceptions from Redis operations, including those originating from `ClusterCommandExecutor`.
  * Translates low-level exceptions into specific `AbstractRedisLockException` subtypes, populating them with context.
  * Integrates with `ExceptionMarkerProvider` for structured logging, as per framework guidelines.

* `UnlockMessageListenerManager` (Spring Bean):
  * Manages the lifecycle of `UnlockMessageListener` instances, typically creating one listener per configured lock bucket.
  * Handles graceful shutdown (`@PreDestroy`).

* `UnlockMessageListener` (Per Bucket, managed by Manager):
  * Implements `org.springframework.data.redis.connection.MessageListener`. Each instance is dedicated to a single lock bucket and subscribes to a pattern for its bucket-specific Pub/Sub channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`).
  * It maintains an internal map of `lockName` (e.g., `{order123}`) to `LockSemaphoreHolder` instances and potentially `CompletableFuture`s for asynchronous waiters.
  * Upon receiving a message, it derives the specific `lockName` from the *channel name*.
  * It parses the `UnlockType` from the message payload.
  * Based on the `UnlockType`, it intelligently signals the `Semaphore` within the `LockSemaphoreHolder` for synchronous waits, or completes the relevant `CompletableFuture` for asynchronous waits.
  * Message processing should be done on an optimized executor (e.g., a dedicated thread pool or virtual threads) to prevent blocking Redis client message dispatching threads, aligning with performance considerations.

* `LockSemaphoreHolder` (Non-Spring managed, per lock key):
  * An instance is associated with each lock key that has waiting threads, managed *within* the per-bucket `UnlockMessageListener`.
  * Encapsulates a `java.util.concurrent.Semaphore` (typically fair, 0 initial permits) and an `AtomicInteger` for `waitersCount`.
  * Used by `AbstractRedisLock` for synchronous `lock()` methods to block on.
  * For asynchronous `lockAsync()` methods, it facilitates the completion of associated `CompletableFuture`s when an unlock notification is received.
  * The `waitForUnlock(timeout)` method drains stale permits before attempting to acquire.

* `LockWatchdog` (Spring Bean):
  * Background mechanism for automatic lease extension of active, application-instance-bound locks.
  * Registered by `AbstractRedisLock` upon successful lock acquisition if conditions are met.
  * **Periodically executes `extend_lock.lua` script using `RedisLockOperations` (which in turn uses `ClusterCommandExecutor` asynchronously).**

* `LockMonitor` (Spring Bean, Optional):
  * Collects and exposes metrics related to lock operations using Micrometer (e.g., acquisition times, contention rates, number of held locks, waiting threads). Conditionally activated, adhering to framework guidelines.

### 2.A RedisReadWriteLock

The `RedisReadWriteLock` implements the `java.util.concurrent.locks.ReadWriteLock` interface, providing a pair of associated locks: a read lock and a write lock. This allows multiple readers to access a resource concurrently, or a single writer to have exclusive access. It is typically created via the fluent builder API starting from `LockBucketRegistry.builder()`.

**Key Characteristics:**

* **Separate Read and Write Locks:** Accessed via `readLock()` and `writeLock()` methods. These return `Lock` instances which are inner classes (`RedisReadWriteLock.ReadLock` and `RedisReadWriteLock.WriteLock`) that extend `AbstractRedisLock` and **MUST implement `AsyncLock`**.
* **Reentrancy:** Both read and write locks support reentrancy for the same thread. Hold counts are managed using `ThreadLocal<Integer>` variables (`readHoldsCount`, `writeHoldsCount`) within the respective `ReadLock` and `WriteLock` inner classes.
* **Lua Scripts:** All script executions are performed asynchronously via `RedisLockOperations` (using `ClusterCommandExecutor`).
  * Read lock acquisition: `try_read_lock.lua`
  * Read lock release: `unlock_read_lock.lua`
  * Write lock acquisition: `try_write_lock.lua`
  * Write lock release: `unlock_write_lock.lua`
* **Redis Key Structure:** (Refer to [`redis_key_schema.md`](redis_key_schema.md) and [`lua_scripts_2.md`](framework-modules/locking/modules/locking-redis-lock/docs/lua_scripts_2.md) for full details)
  * **Main Lock Hash**: The primary `lockName` (e.g., `myApp:__lock_buckets__:resource:__locks__:{myRWLock}`) points to a Redis Hash. This hash stores:
    * `mode`: Indicates if the lock is in 'read' or 'write' mode.
    * `writerId` field (e.g., actual writer's `getLockName(threadId)`): Stores the reentrant count if in 'write' mode.
    * `<readerId>` fields (e.g., `readerA-uuid`): For each reader, a field named after the reader's unique ID stores its reentrant read count.
  * **Individual Read Lock Timeout Keys**: For each reentrant acquisition of a read lock by a specific reader, a separate Redis String key is created (e.g., `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerId>:<reentrantCount>`). This key has a TTL equal to the `leaseTime` of that specific read acquisition. These keys are used by Lua scripts to cooperatively manage the TTL of the main lock hash.
* **Watchdog Integration:** Acquired read locks and write locks (referring to the main `RedisReadWriteLock` instance's `mainLockKey`) are registered with the `LockWatchdog`. The watchdog renews the lease of the main lock Hash key based on the overall configured `leaseTime` for the `RedisReadWriteLock`. However, the Lua scripts for read lock release will dynamically adjust the main lock Hash's TTL based on the maximum remaining TTL of any active individual read lock timeout keys. If no locks (read or write) remain, the Lua scripts will delete the main lock Hash. Watchdog operations are asynchronous.

**Operational Details (Async-First):**

* `RedisReadWriteLock.ReadLock` (inner class extending `AbstractRedisLock` and implementing `AsyncLock`):
  * `tryAcquireLockAsync(String requestUuid, String readerId, String writerId, long leaseTimeMs)`: (Note: `readerId` and `writerId` are specific to this lock, `lockOwnerId` from `AbstractRedisLock` might be a concatenation or derived).
    * Handles client-side reentrant count (`readHoldsCount`).
    * Calls the `try_read_lock.lua` script asynchronously via `RedisLockOperations`. This script:
      * Checks the main lock Hash for current `mode` and any conflicting write lock (unless `writerId` matches current writer, allowing reentrants read-while-write).
      * If acquirable, increments the reentrant count for `readerId` in the main lock Hash.
      * Creates an individual read lock timeout key (e.g., `timeoutPrefixForReaderInstance + ':' + newReentrantCount`) and sets its TTL to `leaseTimeMs`.
      * Updates the main lock Hash's TTL to `max(current_main_TTL, leaseTimeMs)`.
    * If successful, and conditions met, registers the main `RedisReadWriteLock` instance with the watchdog. Returns a `CompletableFuture` for the operation.
  * `releaseLockAsync(String requestUuid, String readerId)`:
    * Decrements client-side `readHoldsCount`.
    * If `readHoldsCount` becomes 0 (full release by this client-side instance of the read lock), it calls the `unlock_read_lock.lua` script asynchronously via `RedisLockOperations`. This script:
      * Decrements/removes the reentrant counter for `readerId` in the main lock Hash.
      * Deletes the corresponding individual read lock timeout key for that specific reentrant count.
      * Recalculates the main lock Hash's TTL based on the maximum remaining TTL of any other active individual read lock timeout keys (for any reader).
      * If no readers or writer remain active, deletes the main lock Hash.
      * Unregisters from watchdog if this was the last overall hold on the main `RedisReadWriteLock` instance.
    * Returns a `CompletableFuture` for the operation.
* `RedisReadWriteLock.WriteLock` (inner class extending `AbstractRedisLock` and implementing `AsyncLock`):
  * `tryAcquireLockAsync(String requestUuid, String writerId, long leaseTimeMs)`: (Note: `writerId` is specific to this lock).
    * Handles client-side reentrancy (`writeHoldsCount`).
    * Calls the `try_write_lock.lua` script asynchronously. This script:
      * Checks the main lock Hash: if no lock exists, sets mode to 'write', stores `writerId` with count 1, and sets TTL.
      * If mode is 'write' and `writerId` matches existing, increments reentrancy count and extends TTL.
      * If mode is 'read' or write lock held by another, returns PTTL of main lock.
    * If successful, registers with watchdog. Returns a `CompletableFuture`.
  * `releaseLockAsync(String requestUuid, String writerId)`:
    * Decrements client-side `writeHoldsCount`.
    * If `writeHoldsCount` becomes 0, calls `unlock_write_lock.lua` asynchronously. This script:
      * Verifies ownership and decrements reentrancy count for `writerId` in the main lock Hash.
      * If count becomes 0, removes `writerId` field.
      * Checks for remaining active readers. If readers exist, changes mode to 'read' and sets main Hash TTL to max remaining reader TTL. Otherwise, deletes the main Hash.
      * Unregisters from watchdog.
    * Returns a `CompletableFuture` for the operation.

*(Detailed Redis data structures and Lua script interactions are documented in [`redis_key_schema.md`](redis_key_schema.md) and [`lua_scripts_2.md`](framework-modules/locking/modules/locking-redis-lock/docs/lua_scripts_2.md).)*

### 2.B RedisStampedLock

The `RedisStampedLock` provides a sophisticated locking mechanism with exclusive write, non-exclusive read, and optimistic read modes. It is conceptually similar to `java.util.concurrent.locks.StampedLock` but is adapted for a distributed Redis environment and notably uses `String`-based stamps instead of `long` values. It is typically created via the fluent builder API.

**Key Characteristics:**

* **Locking Modes:**
  * **Write Lock:** Exclusive access, acquired via `writeLock()`, `tryWriteLock()`. Returns a "W:" prefixed string stamp (e.g., "W:ownerId:uuid"). These methods are wrappers around asynchronous operations.
  * **Read Lock:** Shared access, acquired via `readLock()`, `tryReadLock()`. Returns an "R:" prefixed string stamp (e.g., "R:versionInfo"). These methods are wrappers around asynchronous operations.
  * **Optimistic Read:** Non-blocking, returns an "O:" prefixed string stamp representing a version (e.g., "O:version"). This stamp is validated later using `validate(String stamp)`.
* **String-based Stamps:** Essential for distributed context, these stamps carry type and version/owner information. They are **not compatible** with the `long` stamps of `java.util.concurrent.locks.StampedLock`.
* **Reentrancy:**
  * The `writeLock()` and `readLock()` methods support reentrancy for the calling thread. Hold counts are managed using `ThreadLocal<Integer>` variables (`writeHoldsCount`, `readHoldsCount`).
* **Lock Conversions (Atomic via Lua):** All conversions are atomic and executed asynchronously via `RedisLockOperations`.
  * `tryConvertToWriteLock(String readStamp)`: Attempts to upgrade a read lock to a write lock.
  * `tryConvertToReadLock(String writeStamp)`: Attempts to downgrade a write lock to a read lock.
* **Lua Scripts:** A suite of Lua scripts handles the atomic operations, executed asynchronously via `RedisLockOperations` (using `ClusterCommandExecutor`).
  * `try_stamped_lock.lua`: Acquires locks in "read", "write", or "optimistic" mode.
  * `unlock_stamped_lock.lua`: Releases "read" or "write" locks.
  * `validate_stamp.lua`: Validates an optimistic read stamp.
  * `convert_to_write_lock.lua`: Converts a read lock to a write lock.
  * `convert_to_read_lock.lua`: Converts a write lock to a read lock.
* **Redis Key Structure:**
  * Primarily uses a Redis Hash associated with the `lockName` (e.g., `prefix:bucket:__locks__:{myStampedLock}:stamped_data`). This Hash stores fields like `version` (Integer/String), `write_owner_id` (String), `write_reentrancy_count` (Integer), and `read_holders` (Set of reader IDs or a reader count field) to manage the lock's versioned state.
* **Watchdog Integration:** Acquired read and write locks (not optimistic reads) are registered with the `LockWatchdog` for lease extension, potentially using mode-specific identifiers (e.g., `lockName + ":write_stamped"`, `lockName + ":read_stamped"`). Watchdog operations are asynchronous.

**Operational Details (Async-First):**

* **Write Lock (`writeLockAsync()`, `tryWriteLockAsync()` methods, and `tryAcquireLockAsync` override):
  * Handles reentrancy locally using `writeHoldsCount`.
  * For initial acquisition, calls `try_stamped_lock.lua` with mode "write" asynchronously.
  * On success, registers with watchdog and returns a `CompletableFuture` that completes with a "W:" prefixed stamp.
* **Read Lock (`readLockAsync()`, `tryReadLockAsync()` methods):
  * Handles reentrancy locally using `readHoldsCount`.
  * For initial acquisition, calls `try_stamped_lock.lua` with mode "read" asynchronously.
  * On success, registers with watchdog and returns a `CompletableFuture` that completes with an "R:" prefixed stamp.
* **Optimistic Read (`tryOptimisticRead()`):
  * Calls `try_stamped_lock.lua` with mode "optimistic" asynchronously to retrieve the current version/stamp.
  * Returns a `CompletableFuture` that completes with an "O:" prefixed stamp. Does not block or acquire a physical lock.
* **Validation (`validate(String stamp)`):
  * Requires an "O:" prefixed stamp.
  * Calls `validate_stamp.lua` asynchronously to compare the stamp's version with the current version in Redis. Returns a `CompletableFuture<Boolean>`.
* **Unlocking (`unlockAsync(String stamp)`):
  * Determines lock type from stamp prefix ("W:" or "R:").
  * Handles reentrancy by decrementing local `writeHoldsCount` or `readHoldsCount`.
  * If count reaches zero, calls `unlock_stamped_lock.lua` asynchronously with the appropriate mode ("write" or "read") and unregisters from watchdog. Returns a `CompletableFuture<Void>`.
  * Optimistic stamps ("O:") result in a no-op.
* **Conversions (Asynchronous):**
  * `tryConvertToWriteLockAsync(String readStamp)`: Checks local read hold. Calls `convert_to_write_lock.lua` asynchronously. Updates local hold counts and watchdog registration on success. Returns a `CompletableFuture<String>`.
  * `tryConvertToReadLockAsync(String writeStamp)`: Checks local write hold. Calls `convert_to_read_lock.lua` asynchronously. Updates local hold counts and watchdog registration on success. Returns a `CompletableFuture<String>`.

*(The precise Redis Hash fields and detailed interactions for stamp validation and conversions are dictated by the corresponding Lua scripts. These details should be fully documented in `lua_scripts_2.md` and `redis_key_schema.md`.)*

### 2.X Asynchronous Lock Operations (`AsyncLock` Interface)

* **Purpose**: The `AsyncLock` interface provides a non-blocking API for acquiring and releasing distributed locks, returning `CompletableFuture`s. This is beneficial for applications using reactive programming models or requiring high throughput without thread blocking.
* **Integration**: Concrete lock implementations (like `RedisReentrantLock`) **MUST implement `AsyncLock`**. The asynchronous methods (e.g., `lockAsync()`, `tryLockAsync()`, `unlockAsync()`) would internally use the same atomic Lua scripts as their synchronous counterparts. The primary difference is in the return type and thread-blocking behavior. All Redis operations are performed via `RedisLockOperations` which uses `ClusterCommandExecutor` asynchronously.
* **Waiting Mechanism**: For asynchronous operations that need to wait (e.g., `lockAsync()` when the lock is not free), the mechanism would still involve the Pub/Sub notification system. Instead of the calling thread blocking on a `LockSemaphoreHolder`, the callback from the `UnlockMessageListener` (or a timeout from the semaphore wait) would be used to complete the `CompletableFuture` associated with the asynchronous lock request.
* **Key Methods**:
  * `CompletableFuture<Void> lockAsync()`: Asynchronously acquires the lock.
  * `CompletableFuture<Void> lockInterruptiblyAsync()`: Asynchronously acquires the lock, aborting if interrupted.
  * `CompletableFuture<Boolean> tryLockAsync()`: Asynchronously attempts to acquire the lock without blocking.
  * `CompletableFuture<Boolean> tryLockAsync(long time, TimeUnit unit)`: Asynchronously attempts to acquire the lock within the given waiting time.
  * `CompletableFuture<Void> unlockAsync()`: Asynchronously releases the lock.
  * `CompletableFuture<Boolean> isLockedAsync()`: Asynchronously checks if this lock is currently held.

## 3. Key Mechanisms Detailed

* **Lock Acquisition and Waiting**: See [Lock Acquisition Mechanism](lock_acquisition.md). The core implementation prioritizes the asynchronous flow.
* **Lock Release and Messaging**: See [Unlock Messaging](messaging.md) and Lua script documentation. All Redis commands are executed asynchronously via `ClusterCommandExecutor`.
* **Lease Extension (Watchdog)**: See [Watchdog Mechanism](watchdog.md). All watchdog Redis operations are executed asynchronously via `ClusterCommandExecutor`.
* **Exception Handling**: See [Exception Handling](exception_handling.md). Exceptions from `ClusterCommandExecutor` will be handled and translated.
* **Centralized Idempotency**: All mutating lock operations are protected by a centralized idempotency mechanism managed by `RedisLockOperations`. This system generates unique `requestUuid` values for each logical operation and uses a Redis-based response cache to prevent duplicate execution of retried operations.
* **Configuration Loading**: `RedisLockProperties` for global, `LockBucketBuilder` chain for programmatic overrides culminating in `LockBucketConfig` and instance-specific settings. Integrates with `RedisCoreProperties` for shared Redis client configuration.

### 3.1 Centralized Idempotency Flow

The centralized idempotency mechanism ensures that all mutating lock operations are executed exactly once, even in the presence of network failures, client retries, or other distributed system challenges.

```mermaid
flowchart TD
    A[Client Initiates Lock Operation] --> B[RedisLockOperationsImpl]
    B --> C[Generate Unique requestUuid for Logical Operation]
    C --> D[Construct Response Cache Key:<br/>&lt;prefix&gt;:&lt;bucketName&gt;:__resp_cache__:{&lt;lockName&gt;}:&lt;requestUuid&gt;]
    D --> E[Call Lua Script with requestUuid and responseCacheTtl]

    subgraph LuaScriptExecution [Lua Script Execution on Redis Server]
        LS1[Receive requestUuid, responseCacheTtl, and operation parameters] --> LS2{Check Response Cache for requestUuid?}
        LS2 -- "Cache Hit: Result Found" --> LS3[Return Cached Result<br/>Operation Already Completed]
        LS2 -- "Cache Miss: No Result Found" --> LS4[Execute Core Lock Operation Logic]
        LS4 --> LS5{Operation Successful?}
        LS5 -- "Success" --> LS6[Store Operation Result in Response Cache<br/>Key: requestUuid, TTL: responseCacheTtl]
        LS6 --> LS7[Return Operation Result]
        LS5 -- "Failure" --> LS8[Return Error Result<br/>Do Not Cache Failures]
    end

    E --> LS1
    LS3 --> F[Return Cached Result to Client<br/>Idempotent Behavior Achieved]
    LS7 --> G[Return Fresh Result to Client<br/>Operation Completed Successfully]
    LS8 --> H[Return Error to Client<br/>Operation Failed, Can Be Retried]

    subgraph RetryScenario [Client Retry Scenario]
        I[Network Failure/Timeout During Initial Request] --> J[Client Retries with Same requestUuid]
        J --> B
        B --> K[Same requestUuid Used for Retry Attempts]
        K --> E
    end
```

**Key Benefits of Centralized Idempotency:**

1. **Consistency Across Lock Types**: All lock types (reentrant, state, read-write, stamped) benefit from the same idempotency mechanism without individual implementation complexity.

2. **Network Resilience**: Operations that appear to fail due to network issues but actually succeeded on the Redis server will not be re-executed when retried.

3. **Single Point of Truth**: `RedisLockOperationsImpl` is the sole component responsible for `requestUuid` generation and management, ensuring consistent behavior.

4. **Configurable Cache Duration**: The `responseCacheTtl` property allows tuning the idempotency cache duration based on expected client retry patterns.

5. **Lua Script Integration**: All idempotency logic is handled atomically within Lua scripts, ensuring consistency even under high concurrency.

**Implementation Details:**

- **UUID Generation**: Each public method call in `RedisLockOperationsImpl` (e.g., `tryLock`, `unlock`, `extendLease`) generates a new `requestUuid` using `UUID.randomUUID()`.
- **Retry Consistency**: The same `requestUuid` is used for all internal retry attempts of a single logical operation, ensuring idempotent behavior across retries.
- **Cache Key Format**: Response cache keys follow the pattern `<prefix>:<bucketName>:__resp_cache__:{<lockName>}:<requestUuid>` to ensure proper Redis Cluster hash slot allocation.
- **TTL Management**: The response cache TTL is configured via `RedisLockProperties.responseCacheTtl` and should be set long enough to cover typical client retry windows.

This detailed component breakdown ensures clarity in responsibilities and interactions within the `locking-redis-lock` module, enforcing the Async-First principle and the mandatory use of `redis-core`