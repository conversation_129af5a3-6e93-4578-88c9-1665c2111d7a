# Plan: Redis Lock - Step 1: Project Cleanup and Configuration

This first step focuses on cleaning up the project structure, removing duplicated and outdated code, and aligning the core configuration files with the final documented architecture. This will create a clean foundation for subsequent implementation changes.

### Step 1.1: Consolidate Codebase by Removing the `redislock` Subpackage

The current structure contains two parallel implementations. The logic within the `com.tui.destilink.framework.locking.redis.redislock` subpackage is more aligned with the documentation's intent (e.g., using `ClusterCommandExecutor`). We will treat it as the source for the correct logic and move/merge its contents into the primary `com.tui.destilink.framework.locking.redis` package, deleting the `redislock` subpackage entirely.

**Action:**
1.  Delete the entire directory: `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redislock/`
2.  Delete the corresponding test directory: `framework-modules/locking/modules/locking-redis-lock/src/test/java/com/tui/destilink/framework/locking/redislock/`
3.  The logic from the deleted classes will be re-implemented in the correct packages in subsequent steps. For now, this removes the ambiguity and duplicate code.

### Step 1.2: Update `pom.xml`

The `pom.xml` for the module should have an accurate name and description.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/pom.xml`

**Current Snippet:**
```xml
    <artifactId>locking-redis-lock</artifactId>
    <name>Destilink Framework - Locking - Redis Lock</name>
    <description>Redis-based distributed locking mechanism for the Destilink Framework.</description>
```
**Required Change:** The current snippet is already adequate. No changes are needed for the name and description. We will verify dependencies in later steps.

### Step 1.3: Refactor `RedisLockProperties.java`

Align the properties class with the documentation. The documentation specifies a programmatic, builder-based approach for bucket configuration, not a YAML-based map. The properties should reflect the global defaults.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockProperties.java`

**Required Change:**
Replace the entire content of the file with the following, which matches the property table in `configuration.md`:
```java
package com.tui.destilink.framework.locking.redis.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;

@Data
@Validated
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
public class RedisLockProperties {

    private boolean enabled = true;

    @NotNull
    private Duration leaseTime = Duration.ofSeconds(60);

    @NotNull
    private Duration retryInterval = Duration.ofMillis(100);

    @NotNull
    private Duration stateKeyExpiration = Duration.ofMinutes(5);

    @NotNull
    private Duration pubSubWaitTimeout = Duration.ofSeconds(5);
    
    @Valid
    @NotNull
    private WatchdogProperties watchdog = new WatchdogProperties();

    @NotBlank
    @Pattern(regexp = "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$")
    private String lockOwnerIdValidationRegex = "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$";

    @Min(1)
    private int maxLockNameLength = 255;
    @Min(1)
    private int maxBucketNameLength = 100;
    @Min(1)
    private int maxScopeLength = 100;

    @Data
    @Validated
    public static class WatchdogProperties {
        private boolean enabled = true;

        @NotNull
        private Duration minLeaseTimeForActivation = Duration.ofSeconds(10);

        @NotNull
        private Duration scheduleFixedDelay = Duration.ofSeconds(5);
    }
}
```

### Step 1.4: Update `1000-locking-redis-lock.application.yml`

The default application properties file should reflect the structure of the updated `RedisLockProperties.java`.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/resources/1000-locking-redis-lock.application.yml`

**Required Change:**
Replace the entire content of the file to match the new properties structure.
```yml
destilink:
  fw:
    locking:
      redis:
        enabled: true
        lease-time: PT60S
        retry-interval: PT0.1S
        state-key-expiration: PT5M
        pub-sub-wait-timeout: PT5S
        watchdog:
          enabled: true
          min-lease-time-for-activation: PT10S
          schedule-fixed-delay: PT5S
        lock-owner-id-validation-regex: "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$"
        max-lock-name-length: 255
        max-bucket-name-length: 100
        max-scope-length: 100
```
**Note:** The `config/1000-locking-redis-lock.application.yml` file is a duplicate and should be deleted.

---
