# Redis Lock Core Architecture

## Overview
Combines best practices from both legacy and modern approaches:
- Plan 1's clear Spring integration
- Plan 2's component registry pattern
- Hybrid listener strategy (per-key and per-bucket)

```mermaid
graph TD
    A[LockManager] --> B[LockComponentRegistry]
    A --> C[RedisTemplate]
    B --> D[LockFactory]
    B --> E[ExceptionTranslator]
    D --> F[RedisLock]
    F --> G[PollingStrategy]
    F --> H[PubSubStrategy]
```

## Components
1. **LockComponentRegistry** (Plan 2)
   - Central configuration management
   - Version-aware component selection
   
2. **Exception Hierarchy** (Combined)
   ```java
   public interface ExceptionMarkerProvider {
       String getErrorCode();
   }
   
   public class LockAcquisitionException 
       extends RuntimeException implements ExceptionMarkerProvider {
       // Plan 1's marker interface + Plan 2's rich context
   }
   ```

3. **LockFactory** (Enhanced)
   - Builder pattern from Plan 1
   - Automatic strategy selection from Plan 2