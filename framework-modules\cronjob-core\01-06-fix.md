### `plan_01_project_cleanup.md`

# Plan: Redis Lock - Step 1: Project Cleanup and Configuration

This first step focuses on cleaning up the project structure, removing duplicated and outdated code, and aligning the core configuration files with the final documented architecture. This will create a clean foundation for subsequent implementation changes.

### Step 1.1: Consolidate Codebase by Removing the `redislock` Subpackage

The current structure contains two parallel implementations. The logic within the `com.tui.destilink.framework.locking.redis.redislock` subpackage is more aligned with the documentation's intent (e.g., using `ClusterCommandExecutor`). We will treat it as the source for the correct logic and move/merge its contents into the primary `com.tui.destilink.framework.locking.redis` package, deleting the `redislock` subpackage entirely.

**Action:**
1.  Delete the entire directory: `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redislock/`
2.  Delete the corresponding test directory: `framework-modules/locking/modules/locking-redis-lock/src/test/java/com/tui/destilink/framework/locking/redislock/`
3.  The logic from the deleted classes will be re-implemented in the correct packages in subsequent steps. For now, this removes the ambiguity and duplicate code.

### Step 1.2: Update `pom.xml`

The `pom.xml` for the module should have an accurate name and description.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/pom.xml`

**Current Snippet:**
```xml
    <artifactId>locking-redis-lock</artifactId>
    <name>Destilink Framework - Locking - Redis Lock</name>
    <description>Redis-based distributed locking mechanism for the Destilink Framework.</description>
```
**Required Change:** The current snippet is already adequate. No changes are needed for the name and description. We will verify dependencies in later steps.

### Step 1.3: Refactor `RedisLockProperties.java`

Align the properties class with the documentation. The documentation specifies a programmatic, builder-based approach for bucket configuration, not a YAML-based map. The properties should reflect the global defaults.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockProperties.java`

**Required Change:**
Replace the entire content of the file with the following, which matches the property table in `configuration.md`:
```java
package com.tui.destilink.framework.locking.redis.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;

@Data
@Validated
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
public class RedisLockProperties {

    private boolean enabled = true;

    @NotNull
    private Duration leaseTime = Duration.ofSeconds(60);

    @NotNull
    private Duration retryInterval = Duration.ofMillis(100);

    @NotNull
    private Duration stateKeyExpiration = Duration.ofMinutes(5);

    @NotNull
    private Duration pubSubWaitTimeout = Duration.ofSeconds(5);
    
    @Valid
    @NotNull
    private WatchdogProperties watchdog = new WatchdogProperties();

    @NotBlank
    @Pattern(regexp = "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$")
    private String lockOwnerIdValidationRegex = "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$";

    @Min(1)
    private int maxLockNameLength = 255;
    @Min(1)
    private int maxBucketNameLength = 100;
    @Min(1)
    private int maxScopeLength = 100;

    @Data
    @Validated
    public static class WatchdogProperties {
        private boolean enabled = true;

        @NotNull
        private Duration minLeaseTimeForActivation = Duration.ofSeconds(10);

        @NotNull
        private Duration scheduleFixedDelay = Duration.ofSeconds(5);
    }
}
```

### Step 1.4: Update `1000-locking-redis-lock.application.yml`

The default application properties file should reflect the structure of the updated `RedisLockProperties.java`.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/resources/1000-locking-redis-lock.application.yml`

**Required Change:**
Replace the entire content of the file to match the new properties structure.
```yml
destilink:
  fw:
    locking:
      redis:
        enabled: true
        lease-time: PT60S
        retry-interval: PT0.1S
        state-key-expiration: PT5M
        pub-sub-wait-timeout: PT5S
        watchdog:
          enabled: true
          min-lease-time-for-activation: PT10S
          schedule-fixed-delay: PT5S
        lock-owner-id-validation-regex: "^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$"
        max-lock-name-length: 255
        max-bucket-name-length: 100
        max-scope-length: 100
```
**Note:** The `config/1000-locking-redis-lock.application.yml` file is a duplicate and should be deleted.

---
### `plan_02_auto_configuration.md`

# Plan: Redis Lock - Step 2: Auto-Configuration and Component Registry

This step focuses on creating a single, clean auto-configuration class and a central component registry, strictly following framework guidelines.

### Step 2.1: Consolidate into a Single `RedisLockAutoConfiguration`

The existing code has two auto-configuration classes. We will merge them into one, which will define all shared beans.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockAutoConfiguration.java`

**Action:**
1.  Delete the file `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/config/RedisReentrantLockAutoConfiguration.java`.
2.  Replace the content of `RedisLockAutoConfiguration.java` with the structure below. This new structure will define all necessary beans conditionally.

```java
package com.tui.destilink.framework.locking.redis.config;

import com.tui.destilink.framework.locking.redis.service.*;
import com.tui.destilink.framework.locking.redis.service.impl.*;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.Executor;

@AutoConfiguration
@EnableConfigurationProperties(RedisLockProperties.class)
@ConditionalOnClass(ClusterCommandExecutor.class)
@ConditionalOnProperty(prefix = "destilink.fw.locking.redis", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RedisLockAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ScriptLoader scriptLoader() {
        return new ScriptLoaderImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisLockOperations redisLockOperations(ClusterCommandExecutor executor, ScriptLoader loader) {
        return new RedisLockOperationsImpl(executor, loader);
    }

    @Bean
    @ConditionalOnMissingBean
    public DefaultLockOwnerSupplier defaultLockOwnerSupplier() {
        return new DefaultLockOwnerSupplier();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisLockErrorHandler redisLockErrorHandler() {
        return new RedisLockErrorHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public LockMonitor lockMonitor(ObjectProvider<MeterRegistry> meterRegistry) {
        return new DefaultLockMonitor(meterRegistry.getIfAvailable());
    }
    
    @Bean(name = "redisLockMessageExecutor")
    @ConditionalOnMissingBean(name = "redisLockMessageExecutor")
    public Executor redisLockMessageExecutor() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(Runtime.getRuntime().availableProcessors());
        scheduler.setThreadNamePrefix("redis-lock-msg-");
        scheduler.setDaemon(true);
        scheduler.initialize();
        return scheduler;
    }

    @Bean
    @ConditionalOnMissingBean
    public UnlockMessageListenerManager unlockMessageListenerManager(RedisConnectionFactory connectionFactory, Executor redisLockMessageExecutor) {
        return new UnlockMessageListenerManager(connectionFactory, redisLockMessageExecutor);
    }

    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "destilink.fw.locking.redis.watchdog", name = "enabled", havingValue = "true", matchIfMissing = true)
    public LockWatchdog lockWatchdog(RedisLockOperations ops, RedisLockErrorHandler handler, LockMonitor monitor, RedisLockProperties props) {
        return new LockWatchdog(ops, handler, monitor, props);
    }

    @Bean
    @ConditionalOnMissingBean
    public LockComponentRegistry lockComponentRegistry(
            ScriptLoader scriptLoader,
            UnlockMessageListenerManager unlockMessageListenerManager,
            ObjectProvider<LockWatchdog> lockWatchdog,
            RedisLockOperations redisLockOperations,
            DefaultLockOwnerSupplier defaultLockOwnerSupplier,
            RedisLockErrorHandler redisLockErrorHandler,
            ObjectProvider<LockMonitor> lockMonitorProvider) {
        return new LockComponentRegistry(scriptLoader, unlockMessageListenerManager, lockWatchdog.getIfAvailable(),
                redisLockOperations, defaultLockOwnerSupplier, redisLockErrorHandler, lockMonitorProvider);
    }

    @Bean
    @ConditionalOnMissingBean
    public LockBucketRegistry lockBucketRegistry(LockComponentRegistry componentRegistry, RedisLockProperties globalProperties) {
        return new LockBucketRegistry(componentRegistry, globalProperties);
    }
}
```

### Step 2.2: Implement `LockComponentRegistry.java`

This class acts as a central holder for shared services.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockComponentRegistry.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;

@RequiredArgsConstructor
@Getter
public class LockComponentRegistry {
    private final ScriptLoader scriptLoader;
    private final UnlockMessageListenerManager unlockMessageListenerManager;
    private final LockWatchdog lockWatchdog;
    private final RedisLockOperations redisLockOperations;
    private final DefaultLockOwnerSupplier defaultLockOwnerSupplier;
    private final RedisLockErrorHandler redisLockErrorHandler;
    private final ObjectProvider<LockMonitor> lockMonitorProvider;
}
```

---
### `plan_03_builder_api.md`

# Plan: Redis Lock - Step 3: Builder API and Lock Instantiation

This step implements the fluent builder API for creating lock instances, as described in the documentation. This provides a user-friendly and configurable way to obtain locks.

### Step 3.1: Implement `LockBucketRegistry.java`

This bean is the main entry point for applications to create locks.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockBucketRegistry.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class LockBucketRegistry {
    private final LockComponentRegistry componentRegistry;
    private final RedisLockProperties globalProperties;

    public LockBucketBuilder builder(String bucketName) {
        LockBucketConfig defaultConfig = LockBucketConfig.builder()
                .leaseTime(globalProperties.getLeaseTime())
                .retryInterval(globalProperties.getRetryInterval())
                .pubSubWaitTimeout(globalProperties.getPubSubWaitTimeout())
                .stateKeyExpiration(globalProperties.getStateKeyExpiration())
                .build();
        return new LockBucketBuilder(componentRegistry, bucketName, defaultConfig);
    }
}
```

### Step 3.2: Implement `LockBucketBuilder.java`

This class allows configuring bucket-level properties.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockBucketBuilder.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

@RequiredArgsConstructor
public class LockBucketBuilder {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private LockBucketConfig bucketConfig;

    public LockBucketBuilder withDefaultLeaseTime(Duration leaseTime) {
        this.bucketConfig = this.bucketConfig.toBuilder().leaseTime(leaseTime).build();
        return this;
    }

    public LockBucketBuilder withDefaultRetryInterval(Duration retryInterval) {
        this.bucketConfig = this.bucketConfig.toBuilder().retryInterval(retryInterval).build();
        return this;
    }
    
    // Add other with... methods for bucket config properties

    public LockConfigBuilder lock() {
        return new LockConfigBuilder(componentRegistry, bucketName, bucketConfig);
    }
}
```

### Step 3.3: Implement `LockConfigBuilder.java`

This class is for selecting the lock type.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockConfigBuilder.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.impl.RedisReentrantLock;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class LockConfigBuilder {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final LockBucketConfig bucketConfig;

    public ReentrantLockConfigBuilder reentrant(String lockName) {
        // This is a simplified example. A full implementation would have builders for all lock types.
        return new ReentrantLockConfigBuilder(componentRegistry, bucketName, lockName, bucketConfig);
    }
    
    // Add methods for other lock types: readWrite(name), stamped(name), state(name, expectedState)
}
```

### Step 3.4: Implement `AbstractLockTypeConfigBuilder` and Subclasses

These builders handle instance-specific configuration.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/AbstractLockTypeConfigBuilder.java`
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

@RequiredArgsConstructor
@Getter(AccessLevel.PROTECTED)
public abstract class AbstractLockTypeConfigBuilder<B extends AbstractLockTypeConfigBuilder<B, L>, L extends AbstractRedisLock> {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final String lockName;
    private final LockBucketConfig bucketConfig;

    private Duration instanceLeaseTime;
    private Duration instanceRetryInterval;
    
    @SuppressWarnings("unchecked")
    public B withLeaseTime(Duration leaseTime) {
        this.instanceLeaseTime = leaseTime;
        return (B) this;
    }
    
    @SuppressWarnings("unchecked")
    public B withRetryInterval(Duration retryInterval) {
        this.instanceRetryInterval = retryInterval;
        return (B) this;
    }

    protected Duration getEffectiveLeaseTime() {
        return instanceLeaseTime != null ? instanceLeaseTime : bucketConfig.leaseTime();
    }
    
    protected Duration getEffectiveRetryInterval() {
        return instanceRetryInterval != null ? instanceRetryInterval : bucketConfig.retryInterval();
    }

    public abstract L build();
}
```

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/ReentrantLockConfigBuilder.java`
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.impl.RedisReentrantLock;

public class ReentrantLockConfigBuilder extends AbstractLockTypeConfigBuilder<ReentrantLockConfigBuilder, RedisReentrantLock> {

    public ReentrantLockConfigBuilder(LockComponentRegistry componentRegistry, String bucketName, String lockName, LockBucketConfig bucketConfig) {
        super(componentRegistry, bucketName, lockName, bucketConfig);
    }

    @Override
    public RedisReentrantLock build() {
        return new RedisReentrantLock(
            getComponentRegistry(),
            getBucketConfig(),
            getLockName(),
            getEffectiveLeaseTime().toMillis(),
            getEffectiveRetryInterval().toMillis()
        );
    }
}
```

### Step 3.5: Update Lock Constructors

The constructors for lock implementations should be made package-private to enforce creation via the builder API.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/impl/RedisReentrantLock.java` (and others)

**Change:**
```java
// Change constructor from public to package-private
RedisReentrantLock(
        LockComponentRegistry componentRegistry,
        LockBucketConfig config,
        String lockName,
        long lockTtlMillis,
        long retryIntervalMillis) {
    // ... constructor logic
}
```

---
### `plan_04_core_logic.md`

# Plan: Redis Lock - Step 4: Core Asynchronous Logic and Redis Operations

This step is critical for aligning the module with its core architectural principles: Async-First design and mandatory usage of `redis-core`.

### Step 4.1: Refactor `RedisLockOperationsImpl`

The implementation must use `ClusterCommandExecutor` for all Redis interactions.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/impl/RedisLockOperationsImpl.java`

**Action:**
Replace the current `StringRedisTemplate`-based implementation with one that uses `ClusterCommandExecutor`. This class will be responsible for preparing keys and arguments and calling the executor.

```java
package com.tui.destilink.framework.locking.redis.service.impl;

import com.tui.destilink.framework.locking.redis.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.service.ScriptLoader;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.script.RedisScript;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RequiredArgsConstructor
public class RedisLockOperationsImpl implements RedisLockOperations {
    private final ClusterCommandExecutor commandExecutor;
    private final ScriptLoader scriptLoader;

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration leaseTime) {
        RedisScript<Long> script = scriptLoader.getTryLockScript();
        List<String> keys = Collections.singletonList(lockKey);
        return commandExecutor.executeScript(
                script, 
                keys, 
                ownerId, 
                String.valueOf(leaseTime.toMillis())
        ).thenApply(result -> Long.valueOf(1L).equals(result));
    }

    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String ownerId) {
        RedisScript<Long> script = scriptLoader.getReleaseLockScript();
        List<String> keys = Collections.singletonList(lockKey);
        return commandExecutor.executeScript(
            script,
            keys,
            ownerId
        ).thenApply(result -> Long.valueOf(1L).equals(result));
    }

    // ... implement all other methods from RedisLockOperations interface using commandExecutor
}
```

### Step 4.2: Refactor `AbstractRedisLock`

This base class will contain the core async-first logic and synchronous wrappers.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/AbstractRedisLock.java`

**Action:**
Ensure the class implements `AsyncLock` and that its synchronous methods call the asynchronous ones and block for the result.

```java
package com.tui.destilink.framework.locking.redis;

// ... other imports
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;

public abstract class AbstractRedisLock implements AsyncLock {
    
    // ... fields and constructor ...

    // --- Synchronous Wrappers ---

    @Override
    public void lock() {
        lockAsync().join();
    }

    @Override
    public void lockInterruptibly() throws InterruptedException {
        try {
            lockAsync().get();
        } catch (Exception e) {
            if (e.getCause() instanceof InterruptedException) {
                throw (InterruptedException) e.getCause();
            }
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean tryLock() {
        return tryLockAsync().join();
    }
    
    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        try {
            return tryLockAsync(time, unit).get(time, unit);
        } catch (Exception e) {
             if (e.getCause() instanceof InterruptedException) {
                throw (InterruptedException) e.getCause();
            }
            throw new RuntimeException(e);
        }
    }

    @Override
    public void unlock() {
        unlockAsync().join();
    }

    @Override
    public Condition newCondition() {
        throw new UnsupportedOperationException("Conditions are not supported by Redis locks.");
    }

    // --- Asynchronous API (to be implemented by subclasses) ---

    @Override
    public abstract CompletableFuture<Void> lockAsync();
    
    // ... other abstract async methods ...
}
```

### Step 4.3: Align Concrete Lock Implementations

Review each lock implementation (e.g., `RedisReentrantLock`) to ensure it correctly overrides the abstract async methods from `AbstractRedisLock`.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/impl/RedisReentrantLock.java`

**Action:**
The implementation should contain the logic for reentrancy (checking local thread-local, then calling Redis) and must be fully asynchronous.

```java
package com.tui.destilink.framework.locking.redis.impl;

// ... other imports
import java.util.concurrent.CompletableFuture;

public class RedisReentrantLock extends AbstractRedisLock {

    // ... constructor and fields ...

    @Override
    public CompletableFuture<Void> lockAsync() {
        // Implement reentrant lock logic asynchronously
        // 1. Check thread-local for existing hold.
        // 2. If held, increment and return completed future.
        // 3. If not held, call redisLockOperations.tryLock(...)
        // 4. If acquired, update thread-local and complete future.
        // 5. If not acquired, use UnlockMessageListener/LockSemaphoreHolder to wait asynchronously.
        return new CompletableFuture<>(); // Placeholder
    }
    
    // ... implement other abstract async methods ...
}
```

---
### `plan_05_unlock_notification.md`

# Plan: Redis Lock - Step 5: Unlock Notification Mechanism

This step refactors the Pub/Sub mechanism to be robust and efficient, following the documented design of per-bucket listeners and per-key semaphores.

### Step 5.1: Implement `UnlockMessageListenerManager.java`

This bean manages the lifecycle of listeners.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/UnlockMessageListenerManager.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
public class UnlockMessageListenerManager {
    private final RedisConnectionFactory connectionFactory;
    private final Executor redisLockMessageExecutor;
    private final Map<String, UnlockMessageListener> listeners = new ConcurrentHashMap<>();

    public UnlockMessageListener getListener(String bucketName) {
        return listeners.computeIfAbsent(bucketName, this::createListener);
    }

    private UnlockMessageListener createListener(String bucketName) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setTaskExecutor(redisLockMessageExecutor);
        container.afterPropertiesSet();
        container.start();
        
        String channelPattern = buildChannelPattern(bucketName); // e.g., "prefix:bucket:__unlock_channels__:*"
        UnlockMessageListener listener = new UnlockMessageListener(channelPattern);
        container.addMessageListener(listener, new PatternTopic(channelPattern));
        return listener;
    }
    
    private String buildChannelPattern(String bucketName) {
        // Logic to build the channel pattern
        return "lock:" + bucketName + ":unlocks:*"; // Simplified example
    }
}
```

### Step 5.2: Implement `UnlockMessageListener.java`

This class listens to a pattern and signals the correct semaphore.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/UnlockMessageListener.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.model.UnlockType;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@RequiredArgsConstructor
public class UnlockMessageListener implements MessageListener {
    private final String channelPattern;
    private final Map<String, LockSemaphoreHolder> semaphoreHolders = new ConcurrentHashMap<>();

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel(), StandardCharsets.UTF_8);
        String lockName = extractLockNameFromChannel(channel);
        String unlockTypeStr = new String(message.getBody(), StandardCharsets.UTF_8);
        UnlockType unlockType = UnlockType.valueOf(unlockTypeStr);
        
        LockSemaphoreHolder holder = semaphoreHolders.get(lockName);
        if (holder != null) {
            holder.signal(unlockType);
        }
    }
    
    private String extractLockNameFromChannel(String channel) {
        // Logic to extract {lockName} from channel, e.g. "prefix:bucket:__unlock_channels__:{lock123}"
        return channel; // Placeholder
    }
    
    // Methods to add/remove/get semaphore holders
}
```

### Step 5.3: Implement `LockSemaphoreHolder.java`

This class manages the waiting logic.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockSemaphoreHolder.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.model.UnlockType;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

public class LockSemaphoreHolder {
    private final Semaphore semaphore = new Semaphore(0, true);
    private final AtomicInteger waiters = new AtomicInteger(0);
    // Could also hold a map of requestUuid -> CompletableFuture for async waiters

    public void signal(UnlockType unlockType) {
        // Logic to release one or more permits based on unlockType and waiters
        if (waiters.get() > 0) {
            semaphore.release();
        }
    }
    
    // Other methods: await, addWaiter, removeWaiter etc.
}
```

---
### `plan_06_finalizing.md`

# Plan: Redis Lock - Step 6: Watchdog, Exceptions, and Final Touches

This final step ensures the remaining components are aligned with the documentation, providing a complete and robust implementation.

### Step 6.1: Refactor `LockWatchdog.java`

The watchdog needs to be a scheduled bean that interacts asynchronously with Redis.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockWatchdog.java`

**Action:**
Implement the watchdog as a scheduled service. It should maintain a map of monitored locks and periodically call `redisLockOperations.extendLock(...)`.

```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;

@RequiredArgsConstructor
public class LockWatchdog {
    private final RedisLockOperations redisLockOperations;
    private final RedisLockErrorHandler errorHandler;
    private final LockMonitor lockMonitor;
    private final RedisLockProperties properties;

    // Map to store monitored locks...
    
    @Scheduled(fixedDelayString = "${destilink.fw.locking.redis.watchdog.schedule-fixed-delay}")
    public void extendLeases() {
        // Iterate through monitored locks
        // For each lock, call redisLockOperations.extendLock(...)
        // Handle success/failure, update monitor, and remove from map if failed
    }

    public void registerLock(...) { ... }
    public void unregisterLock(...) { ... }
    public void shutdown() { ... }
}
```

### Step 6.2: Align Exception Classes

Ensure all custom exceptions provide contextual markers for structured logging.

**File(s) to Modify:** All classes in `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/exception/`

**Action:**
Review each exception class.
1.  Ensure it extends `AbstractRedisLockException`.
2.  Ensure its constructor correctly passes all required context (`lockName`, `lockType`, etc.) to the super constructor.
3.  Implement the `populateSpecificMarkers` method to add exception-specific context to the log marker.

**Example for `LockTimeoutException.java`:**
```java
public class LockTimeoutException extends AbstractRedisLockException {
    // ... fields: timeoutMillis, attempts
    
    // ... constructor ...

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.timeoutMillis", this.timeoutMillis);
        contextMap.put("lock.attempts", this.attempts);
    }
}
```

### Step 6.3: Final Code Review for Guideline Adherence

Perform a final pass over the entire `locking-redis-lock` module to ensure:
1.  **No `@ComponentScan`**: The module relies solely on `RedisLockAutoConfiguration`.
2.  **Constructor Injection**: All dependencies are injected via constructors.
3.  **`redis-core` Usage**: All Redis interactions are through `ClusterCommandExecutor`. All key construction uses `RedisKeyPrefix` and `RedisKey`.
4.  **Asynchronous by Default**: Core logic uses `CompletableFuture`, and synchronous methods are simple wrappers.
5.  **Logging**: SLF4J with parameterized messages is used. MDC context is set and cleared correctly.

This completes the plan to refactor the module. Executing these steps will result in a clean, robust, and maintainable implementation that is fully aligned with the provided documentation and framework guidelines.