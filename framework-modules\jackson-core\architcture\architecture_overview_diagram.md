# Redis Locking Module: Architecture Overview Diagram

This diagram illustrates the high-level architecture of the `locking-redis-lock` module, showing the key components and their relationships.

```mermaid
classDiagram
    %% Core Interfaces
    class Lock {
        <<interface>>
        +lock(): void
        +lockInterruptibly(): void
        +tryLock(): boolean
        +tryLock(time, unit): boolean
        +unlock(): void
    }
    
    class AsyncLock {
        <<interface>>
        +lockAsync(): CompletableFuture~Void~
        +lockInterruptiblyAsync(): CompletableFuture~Void~
        +tryLockAsync(): CompletableFuture~Boolean~
        +tryLockAsync(time, unit): CompletableFuture~Boolean~
        +unlockAsync(): CompletableFuture~Void~
        +isLockedAsync(): CompletableFuture~Boolean~
    }
    
    %% Abstract Base Class
    class AbstractRedisLock {
        <<abstract>>
        -redisLockOperations: RedisLockOperations
        -lockOwnerSupplier: LockOwnerSupplier
        -properties: RedisLockProperties
        #lockKey: String
        +lock(): void
        +lockInterruptibly(): void
        +tryLock(): boolean
        +tryLock(time, unit): boolean
        +unlock(): void
        #doTryLock(leaseTime): boolean
        #doUnlock(): void
        #doIsLocked(): boolean
        #acquireLockWithRetry(leaseTime): boolean
        #acquireLockWithRetryInterruptibly(leaseTime): boolean
    }
    
    %% Concrete Lock Implementations
    class RedisReentrantLock {
        -threadLocalHolds: ThreadLocal~Integer~
        +lock(): void
        +unlock(): void
        +isLocked(): boolean
        +isHeldByCurrentThread(): boolean
    }
    
    class RedisReadWriteLock {
        +readLock(): Lock
        +writeLock(): Lock
    }
    
    class RedisStampedLock {
        +readLock(): Lock
        +writeLock(): Lock
        +tryOptimisticRead(): String
        +validate(stamp): boolean
        +tryConvertToWriteLock(stamp): String
        +tryConvertToReadLock(stamp): String
    }
    
    class RedisStateLock {
        +getState(): T
        +updateState(newState): boolean
        +updateStateIfEquals(expected, newState): boolean
    }
    
    %% Core Services
    class RedisLockOperations {
        -scriptLoader: ScriptLoader
        -clusterCommandExecutor: ClusterCommandExecutor
        +tryLock(lockKey, ownerId, leaseTime): CompletableFuture~Boolean~
        +unlock(lockKey, ownerId): CompletableFuture~Boolean~
        +isLocked(lockKey): CompletableFuture~Boolean~
        +tryReadLock(lockKey, readerId, leaseTime): CompletableFuture~Boolean~
        +unlockReadLock(lockKey, readerId): CompletableFuture~Boolean~
        +tryWriteLock(lockKey, writerId, leaseTime): CompletableFuture~Boolean~
        +unlockWriteLock(lockKey, writerId): CompletableFuture~Boolean~
        +tryStampedLock(lockKey, ownerId, mode, leaseTime): CompletableFuture~String~
        +unlockStampedLock(lockKey, ownerId, stamp): CompletableFuture~Boolean~
        +validateStamp(lockKey, stamp): CompletableFuture~Boolean~
        +convertToWriteLock(lockKey, ownerId, stamp, leaseTime): CompletableFuture~String~
        +convertToReadLock(lockKey, ownerId, stamp, leaseTime): CompletableFuture~String~
        +tryStateLock(lockKey, ownerId, initialState, leaseTime): CompletableFuture~Boolean~
        +getState(lockKey): CompletableFuture~T~
        +updateState(lockKey, ownerId, newState): CompletableFuture~Boolean~
        +updateStateIfEquals(lockKey, ownerId, expected, newState): CompletableFuture~Boolean~
        +extendLock(lockKey, ownerId, leaseTime): CompletableFuture~Boolean~
    }
    
    class ScriptLoader {
        -scripts: Map~String, String~
        +loadScripts(): void
        +getScript(name): String
    }
    
    class LockWatchdog {
        -monitoredLocks: Map
        -scheduler: ScheduledExecutorService
        +scheduleLeaseExtension(lockKey, ownerId, leaseTime): void
        +cancelLeaseExtension(lockKey, ownerId): void
    }
    
    class UnlockMessageListener {
        -semaphoreHolders: Map
        +onMessage(channel, message): void
    }
    
    class LockSemaphoreHolder {
        -permits: Semaphore
        -waiters: AtomicInteger
        +acquirePermit(timeout): boolean
        +releasePermit(): void
        +signal(UnlockType): void
    }
    
    class LockComponentRegistry {
        +getLock(name): Lock
        +getReentrantLock(name): RedisReentrantLock
        +getReadWriteLock(name): RedisReadWriteLock
        +getStampedLock(name): RedisStampedLock
        +getStateLock(name, initialState): RedisStateLock
    }
    
    %% Configuration
    class RedisLockProperties {
        +leaseTime: long
        +retryInterval: long
        +unlockMessageListenerEnabled: boolean
        +watchdogEnabled: boolean
        +watchdogMinLeaseTimeForActivation: long
        +watchdogScheduleFixedDelay: long
        +watchdogMaxTtlForRenewalCheck: long
        +pubSubWaitTimeout: long
        +stateKeyExpiration: long
        +lockOwnerIdValidationRegex: String
        +maxLockNameLength: int
        +maxBucketNameLength: int
        +maxScopeLength: int
    }
    
    class LockBucketConfig {
        +bucketName: String
        +scope: String
        +leaseTime: long
        +retryInterval: long
        +unlockMessageListenerEnabled: boolean
        +watchdogEnabled: boolean
    }
    
    class RedisLockAutoConfiguration {
        <<@AutoConfiguration>>
    }
    
    %% Exception Hierarchy
    class AbstractRedisLockException {
        +lockKey: String
        +lockType: String
        +lockOwnerId: String
        +requestUuid: UUID
        +stamp: String
        +details: Map
    }
    
    %% Redis Integration
    class ClusterCommandExecutor {
        +executeAsync(command): CompletableFuture
        +executeScriptAsync(script, keys, args): CompletableFuture
    }
    
    %% Relationships
    
    %% Interface Implementations
    Lock <|.. AsyncLock : extends
    AsyncLock <|.. AbstractRedisLock : implements
    AbstractRedisLock <|-- RedisReentrantLock : extends
    AbstractRedisLock <|-- RedisReadWriteLock : extends
    AbstractRedisLock <|-- RedisStampedLock : extends
    AbstractRedisLock <|-- RedisStateLock : extends
    
    %% Core Service Dependencies
    AbstractRedisLock --> RedisLockOperations : uses
    AbstractRedisLock --> LockOwnerSupplier : uses
    AbstractRedisLock --> LockSemaphoreHolder : uses for waiting
    RedisLockOperations --> ScriptLoader : loads scripts
    RedisLockOperations --> ClusterCommandExecutor : executes Redis commands
    RedisLockOperations --> RedisLockErrorHandler : translates errors
    
    %% Configuration Dependencies
    AbstractRedisLock --> RedisLockProperties : configured by
    RedisLockAutoConfiguration --> RedisLockProperties : enables
    RedisLockAutoConfiguration --> LockComponentRegistry : creates
    RedisLockAutoConfiguration --> RedisLockOperations : creates
    RedisLockAutoConfiguration --> ScriptLoader : creates
    RedisLockAutoConfiguration --> LockWatchdog : creates
    RedisLockAutoConfiguration --> UnlockMessageListener : creates
    
    %% Messaging Flow
    UnlockMessageListener --> LockSemaphoreHolder : signals
    LockWatchdog --> RedisLockOperations : extends leases
    
    %% Registry Access
    LockComponentRegistry --> RedisReentrantLock : creates
    LockComponentRegistry --> RedisReadWriteLock : creates
    LockComponentRegistry --> RedisStampedLock : creates
    LockComponentRegistry --> RedisStateLock : creates
    
    %% Error Handling
    RedisLockOperations --> AbstractRedisLockException : throws
    
    %% Notes
    note for AsyncLock "Extends java.util.concurrent.Lock with async methods"
    note for AbstractRedisLock "Base class for all Redis lock implementations"
    note for RedisLockOperations "Core service for Redis lock operations"
    note for LockSemaphoreHolder "Manages non-polling wait mechanism"
    note for UnlockMessageListener "Receives Redis Pub/Sub messages"
    note for LockWatchdog "Extends lock leases automatically"
    note for LockComponentRegistry "Central access point for lock instances"
```

## Key Component Relationships

1. **Client Interaction Flow**:
   - Applications interact with the `LockComponentRegistry` to obtain lock instances
   - Lock instances delegate to `RedisLockOperations` for Redis interactions
   - `RedisLockOperations` uses `ScriptLoader` to execute Lua scripts via `ClusterCommandExecutor`

2. **Non-Polling Wait Mechanism**:
   - When a lock is unavailable, `AbstractRedisLock` uses `LockSemaphoreHolder` to wait
   - `UnlockMessageListener` receives Redis Pub/Sub messages when locks are released
   - `UnlockMessageListener` signals the appropriate `LockSemaphoreHolder` to wake waiting threads

3. **Lease Extension**:
   - `LockWatchdog` automatically extends leases for long-running locks
   - It schedules extension tasks based on the lock's lease time
   - Extension is performed via `RedisLockOperations.extendLock()`

4. **Configuration Hierarchy**:
   - Global settings from `RedisLockProperties`
   - Bucket-specific overrides via `LockBucketConfig`
   - Instance-level parameters during lock acquisition

5. **Error Handling**:
   - Redis errors are translated to specific `AbstractRedisLockException` subtypes
   - Exceptions include context like lock key, owner ID, and request UUID