# Redis Lock Implementation Diagrams

This directory contains activity diagrams for the Redis-based distributed lock implementations in the Destilink Framework.

## Available Diagrams

1. [RedisStateLock](redis-state-lock-diagram.md) - Conditional locking based on a state value stored in Redis
2. [RedisStampedLock](redis-stamped-lock-diagram.md) - Supports write (exclusive), read (shared), and optimistic read modes
3. [RedisReentrantLock](redis-reentrant-lock-diagram.md) - Standard reentrant mutex lock implementation
4. [RedisReadWriteLock](redis-read-write-lock-diagram.md) - Composed of separate ReadLock and WriteLock implementations

## Overview

All lock implementations:
- Use Redis Lua scripts for atomic operations
- Leverage a LockWatchdog service to automatically extend lock leases while held
- Support reentrancy within the same thread
- Implement appropriate error handling and timeout mechanisms