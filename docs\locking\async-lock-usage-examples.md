# AsyncLock Usage Examples

This document provides usage examples for the `AsyncLock` interface using the `RedisLock` implementation.

## lockAsync()

```java
AsyncLock lock = new RedisLock("lock-key");
CompletableFuture<Void> future = lock.lockAsync();
future.thenRun(() -> {
    // Critical section
}).exceptionally(ex -> {
    // Handle exception
    return null;
});
```

## lockInterruptiblyAsync()

```java
AsyncLock lock = new RedisLock("lock-key");
CompletableFuture<Void> future = lock.lockInterruptiblyAsync();
future.thenRun(() -> {
    // Critical section
}).exceptionally(ex -> {
    // Handle exception
    return null;
});
```

## tryLockAsync()

```java
AsyncLock lock = new RedisLock("lock-key");
CompletableFuture<Boolean> future = lock.tryLockAsync();
future.thenAccept(acquired -> {
    if (acquired) {
        // Critical section
        lock.unlockAsync();
    } else {
        // Handle failure to acquire lock
    }
}).exceptionally(ex -> {
    // Handle exception
    return null;
});
```

## tryLockAsync(long time, TimeUnit unit)

```java
AsyncLock lock = new RedisLock("lock-key");
CompletableFuture<Boolean> future = lock.tryLockAsync(10, TimeUnit.SECONDS);
future.thenAccept(acquired -> {
    if (acquired) {
        // Critical section
        lock.unlockAsync();
    } else {
        // Handle failure to acquire lock
    }
}).exceptionally(ex -> {
    // Handle exception
    return null;
});
```

## unlockAsync()

```java
AsyncLock lock = new RedisLock("lock-key");
lock.lockAsync().thenRun(() -> {
    // Critical section
    lock.unlockAsync().exceptionally(ex -> {
        // Handle exception
        return null;
    });
});
```

## isLockedAsync()

```java
AsyncLock lock = new RedisLock("lock-key");
CompletableFuture<Boolean> future = lock.isLockedAsync();
future.thenAccept(locked -> {
    if (locked) {
        // Lock is held
    } else {
        // Lock is not held
    }
}).exceptionally(ex -> {
    // Handle exception
    return null;
});