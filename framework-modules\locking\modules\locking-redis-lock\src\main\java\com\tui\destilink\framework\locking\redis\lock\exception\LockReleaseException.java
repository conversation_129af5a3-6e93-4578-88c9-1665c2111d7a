package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import io.lettuce.core.RedisCommandExecutionException;

/**
 * Exception thrown when a lock release operation fails.
 * <p>
 * This exception is thrown when an attempt to release a lock fails,
 * typically because the lock is owned by a different owner or has expired.
 * </p>
 */
public class LockReleaseException extends AbstractRedisLockException {

    private final String attemptedOwnerId;
    private final String actualOwnerId;
    private final String redisError; // Added to store potential Redis errors

    /**
     * Constructs a new LockReleaseException with the specified details.
     *
     * @param lockName         The full Redis key of the lock involved
     * @param lockType         The specific type of lock (e.g.,
     *                         "RedisReentrantLock")
     * @param lockOwnerId      The ID of the owner attempting the operation (can be
     *                         null)
     * @param attemptedOwnerId The ID of the owner attempting to release the lock
     * @param actualOwnerId    The ID of the actual owner of the lock (if known, can
     *                         be null)
     * @param message          Descriptive error message
     */
    public LockReleaseException(String lockName, String lockType, String lockOwnerId,
            String attemptedOwnerId, String actualOwnerId, String message) {
        super(lockName, lockType, lockOwnerId, null, message); // requestUuid is null
        this.attemptedOwnerId = attemptedOwnerId;
        this.actualOwnerId = actualOwnerId;
        this.redisError = null;
    }

    /**
     * Constructs a new LockReleaseException with the specified details and cause.
     *
     * @param lockName         The full Redis key of the lock involved
     * @param lockType         The specific type of lock (e.g.,
     *                         "RedisReentrantLock")
     * @param lockOwnerId      The ID of the owner attempting the operation (can be
     *                         null)
     * @param attemptedOwnerId The ID of the owner attempting to release the lock
     * @param actualOwnerId    The ID of the actual owner of the lock (if known, can
     *                         be null)
     * @param message          Descriptive error message
     * @param cause            The underlying cause of this exception
     */
    public LockReleaseException(String lockName, String lockType, String lockOwnerId,
            String attemptedOwnerId, String actualOwnerId, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, null, message, cause); // requestUuid is null
        this.attemptedOwnerId = attemptedOwnerId;
        this.actualOwnerId = actualOwnerId;
        this.redisError = (cause instanceof RedisCommandExecutionException) ? cause.getMessage() : null;
    }

    /**
     * Constructs a new LockReleaseException with a message and cause.
     *
     * @param lockName    The full Redis key of the lock involved.
     * @param lockType    The specific type of lock.
     * @param lockOwnerId The ID of the owner attempting the operation.
     * @param message     Descriptive error message.
     * @param cause       The underlying cause of this exception.
     */
    public LockReleaseException(String lockName, String lockType, String lockOwnerId, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, null, message, cause); // requestUuid is null
        this.attemptedOwnerId = lockOwnerId; // Assuming the lockOwnerId is the one who attempted
        this.actualOwnerId = null; // Actual owner might not be known in this context
        this.redisError = (cause instanceof RedisCommandExecutionException) ? cause.getMessage() : null;
    }

    /**
     * Constructs a new LockReleaseException with just a message.
     *
     * @param lockName    The full Redis key of the lock involved.
     * @param lockType    The specific type of lock.
     * @param lockOwnerId The ID of the owner attempting the operation.
     * @param message     Descriptive error message.
     */
    public LockReleaseException(String lockName, String lockType, String lockOwnerId, String message) {
        super(lockName, lockType, lockOwnerId, null, message); // requestUuid is null
        this.attemptedOwnerId = lockOwnerId;
        this.actualOwnerId = null;
        this.redisError = null;
    }

    /**
     * Returns the ID of the owner who attempted to release the lock.
     *
     * @return The ID of the attempting owner.
     */
    public String getAttemptedOwnerId() {
        return attemptedOwnerId;
    }

    /**
     * Returns the ID of the actual owner of the lock, if known.
     *
     * @return The ID of the actual owner, or null if not known or not applicable.
     */
    public String getActualOwnerId() {
        return actualOwnerId;
    }

    /**
     * Returns the specific Redis error message or Lua script error, if available.
     *
     * @return The Redis error message, or null if not applicable.
     */
    public String getRedisError() {
        return redisError;
    }

    public Map<String, Object> getAdditionalContext() {
        Map<String, Object> context = new HashMap<>();
        context.put("lockName", getLockName());
        context.put("lockType", getLockType());
        if (getLockOwnerId() != null) {
            context.put("lockOwnerId", getLockOwnerId());
        }
        if (getRequestUuid() != null) {
            context.put("requestUuid", getRequestUuid());
        }
        context.put("attemptedOwnerId", Objects.toString(attemptedOwnerId, "null"));
        if (actualOwnerId != null) {
            context.put("actualOwnerId", actualOwnerId);
        }
        if (redisError != null) {
            context.put("redisError", redisError);
        }
        return context;
    }
}