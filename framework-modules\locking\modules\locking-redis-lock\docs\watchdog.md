# Redis Locking Module: Final Watchdog Mechanism

> **📋 REFACTORING NOTICE**
>
> Watchdog configuration changes are part of an ongoing refactoring effort to streamline the Redis locking module. Some configuration properties may be in transition during this process. For the most current property status and any temporary inconsistencies, please refer to [`configuration.md`](./configuration.md).

## 1. Introduction

The `LockWatchdog` is a critical component in the `locking-redis-lock` module. Its primary responsibility is to prevent distributed locks from expiring prematurely while they are still actively and legitimately held by an application instance. This mechanism is essential for long-running operations that might exceed the initial Time-To-Live (TTL) set on a Redis lock key. This document details the consolidated design of the watchdog.

## 2. Purpose and Design Principles

*   **Lease Extension Pattern**: The watchdog implements the lease extension pattern. Locks are acquired with an intended `leaseTime`. If this `leaseTime` is sufficiently long (implicitly, in relation to the watchdog's operational interval), the watchdog manages the lock's TTL in Redis.
*   **Prevent Premature Expiration**: It periodically renews the lock's TTL in Redis as long as the acquiring application instance is alive and the lock is registered with the watchdog.
*   **Resilience**: If the application instance crashes or the lock is explicitly released, the watchdog stops renewing, allowing the lock to expire naturally based on its last set TTL, thus preventing orphaned locks.
*   **Short Physical TTLs**: This allows for shorter actual TTLs on Redis keys, improving system recovery if an instance fails, while still supporting logically long lock durations.

## 3. Core Components and Configuration

*   **`LockWatchdog.java` (Bean)**:
    *   A Spring-managed singleton bean instantiated by `RedisLockAutoConfiguration`.
    *   Dependencies: `RedisLockOperations` (for Lua script execution) and `ScriptLoader` (for the `extend_lock.lua` script).
    *   Maintains an internal `ConcurrentHashMap<String, LockInfo>` (`monitoredLocks`) to track active locks.
*   **`LockWatchdog.LockInfo` (Inner Class)**:
    *   Stores `lockOwnerId`, the original `leaseTimeMillis` of the lock, the calculated `extensionIntervalMillis`, and the `nextExtensionTime`.
*   **Global Configuration (`RedisLockProperties.java`)**:
    *   The watchdog is active if the main Redis locking mechanism is enabled (`destilink.fw.locking.redis.enabled=true`) and the watchdog properties are configured.
    *   **Watchdog Specific Properties (under `destilink.fw.locking.redis.watchdog.*`)**:
        *   `interval` (Duration, property: `destilink.fw.locking.redis.watchdog.interval`): The fixed delay (interval) for the watchdog's scheduled renewal task. This dictates how frequently the watchdog checks and potentially renews locks.
        *   `operationMaxRetries` (int, property: `destilink.fw.locking.redis.watchdog.operation-max-retries`): Maximum number of retries for a single watchdog lease extension operation if it fails.
        *   `operationTimeout` (Duration, property: `destilink.fw.locking.redis.watchdog.operation-timeout`): Timeout for a single watchdog lease extension command sent to Redis.
        *   `corePoolSize` (int, property: `destilink.fw.locking.redis.watchdog.core-pool-size`): Core pool size for the watchdog's scheduled executor service.
        *   `threadNamePrefix` (String, property: `destilink.fw.locking.redis.watchdog.thread-name-prefix`): Thread name prefix for watchdog executor threads.
        *   `shutdownAwaitTermination` (Duration, property: `destilink.fw.locking.redis.watchdog.shutdown-await-termination`): Timeout for waiting for the watchdog scheduler to terminate during shutdown.
*   **Instance-Level Activation Conditions**: The watchdog becomes active for a specific lock instance if **all** the following are true:
    1.  Global Redis locking is enabled (`destilink.fw.locking.redis.enabled` is `true`).
    2.  The `LockOwnerSupplier` (configured at the bucket level via `LockBucketBuilder.withLockOwnerSupplier(LockOwnerSupplier)`) permits watchdog use for the specific lock instance (e.g., via a method like `boolean canUseWatchdog(String lockIdentifier, String ownerId)` on the supplier).
    3.  The lock is "application-instance-bound": The `LockOwnerSupplier` returns a non-empty, non-null owner ID for the current application instance.
    4.  The lock's effective `leaseTime` (as resolved from global defaults, bucket defaults, and instance overrides) is sufficiently long. Typically, this means the `leaseTime` should be significantly longer than the watchdog's `interval` to allow for meaningful extensions.

## 4. Operational Flow

### 4.1. Lock Registration

1.  When a lock is successfully acquired (e.g., by `AbstractRedisLock`) and all watchdog activation conditions (see section 3) are met.
2.  `AbstractRedisLock` calls `lockWatchdog.registerLock(lockKey, lockOwnerId, effectiveLeaseTimeMillis)`.
3.  The `LockWatchdog` then:
    *   Calculates an appropriate `extensionIntervalMillis` (e.g., based on the configured `watchdog.interval` or a fraction of the `effectiveLeaseTimeMillis`, ensuring it's less than the effective lease time).
    *   Calculates `nextExtensionTime = System.currentTimeMillis() + extensionIntervalMillis`.
    *   Stores a new `LockInfo(lockOwnerId, effectiveLeaseTimeMillis, extensionIntervalMillis, nextExtensionTime)` in `monitoredLocks`.

### 4.2. Scheduled Lease Extension (`extendLocks()` method)

1.  The `extendLocks()` method is annotated with `@Scheduled(fixedDelayString = "${destilink.fw.locking.redis.watchdog.interval}")`.
2.  It iterates through `monitoredLocks`.
3.  For each `LockInfo`, if `System.currentTimeMillis() >= lockInfo.getNextExtensionTime()`:
    *   It retrieves the `extend_lock.lua` script.
    *   Executes the script via `redisLockOperations.executeScriptAsync(...)`, passing:
        *   `KEYS[1]`: `lockKey`
        *   `KEYS[2]`: `responseCacheKey` (the fully constructed key for the response cache, passed by the client, if the script supports it)
        *   `ARGV[1]`: `requestUuid` (a new UUID for this extension attempt)
        *   `ARGV[2]`: The new lease time in milliseconds for this extension (e.g., a duration derived from the watchdog's `interval` or a significant portion of the original `leaseTime`, ensuring it's a sensible duration for renewal, often the original `leaseTime` or `watchdog.interval * N`).
        *   `ARGV[3]`: `lockInfo.getLockOwnerId()`.
    *   **Atomic Ownership Check**: The `extend_lock.lua` script **MUST** atomically check if the `lockKey` in Redis is still held by `lockInfo.getLockOwnerId()` before extending its TTL.
    *   **Result Handling**:
        *   **Success** (script returns `1`): `lockInfo.setNextExtensionTime(System.currentTimeMillis() + lockInfo.getExtensionIntervalMillis())`. Log success at DEBUG. The TTL of the key in Redis is updated.
        *   **Failure** (script returns `0` or error): Lock not found, owner mismatch, or error. Remove `lockKey` from `monitoredLocks`. Log a WARNING.
    *   Exceptions during script execution are caught, logged (respecting `operationMaxRetries`), and may result in removing the lock from monitoring if retries are exhausted.

### 4.3. Lock Unregistration

1.  When `AbstractRedisLock.unlock()` is called.
2.  `unlock()` calls `lockWatchdog.unregisterLock(lockKey, lockOwnerId)` unconditionally (the watchdog will ignore if not registered).
3.  The `LockWatchdog`:
    *   Retrieves `LockInfo` for `lockKey`.
    *   If found and `lockOwnerId` matches, removes the entry from `monitoredLocks`, stopping further extensions.

## 5. Key Considerations

*   **Atomicity of `extend_lock.lua`**: This script is critical. It must perform the ownership check and the `PEXPIRE` command atomically.
*   **Configuration Alignment**: The watchdog's `interval` and the TTL set upon renewal must be carefully configured. The actual TTL set by the watchdog during renewal should be a duration that provides a safe buffer until the next check (e.g., the original `leaseTime` or a multiple of the `watchdog.interval`).
*   **Clock Skew**: The buffer provided by renewing locks before their TTL expires helps mitigate minor clock skews.
*   **Network Latency**: The chosen renewal intervals should account for expected network latencies and the configured `operationTimeout`.
*   **Application Shutdown**: The `LockWatchdog` should implement `DisposableBean` or use `@PreDestroy` to gracefully stop its scheduled task, respecting `shutdownAwaitTermination`.
*   **`RedisReadWriteLock` TTL Interaction**: When the watchdog manages a `RedisReadWriteLock`, it renews the main Hash key. The effective TTL of this Hash key can also be maintained by active individual read lock timeout keys.

The `LockWatchdog` ensures the liveness and safety of distributed locks held for extended periods, driven by the `LockOwnerSupplier` and lease duration rather than a direct boolean flag.