# Redis Locking Module: Exception Handling Diagram

This diagram illustrates the exception hierarchy and error handling flow in the `locking-redis-lock` module.

```mermaid
classDiagram
    %% Base Exception Classes
    class Throwable {
        <<Java Core>>
        +getMessage(): String
        +getCause(): Throwable
        +getStackTrace(): StackTraceElement[]
    }
    
    class Exception {
        <<Java Core>>
    }
    
    class RuntimeException {
        <<Java Core>>
    }
    
    class MarkerNestedException {
        <<Destilink Core>>
        +getMarker(): Marker
    }
    
    class MarkerNestedRuntimeException {
        <<Destilink Core>>
        +getMarker(): Marker
    }
    
    class ExceptionMarkerProvider {
        <<interface>>
        +getMarker(): Marker
    }
    
    %% Module Base Exception
    class AbstractRedisLockException {
        +lockKey: String
        +lockType: String
        +lockOwnerId: String
        +requestUuid: UUID
        +stamp: String
        +details: Map~String, Object~
        +getMarker(): Marker
    }
    
    %% Specific Exception Types
    class LockAcquisitionException {
        +reason: String
        +attemptCount: int
        +elapsedTimeMs: long
    }
    
    class LockTimeoutException {
        +timeoutMs: long
        +attemptCount: int
    }
    
    class LockReleaseException {
        +reason: String
    }
    
    class LeaseExtensionException {
        +reason: String
        +leaseTimeMs: long
    }
    
    class LockInterruptedException {
        +interruptedWhile: String
    }
    
    class LockCommandException {
        +command: String
        +args: Object[]
    }
    
    class LockConnectionException {
        +connectionDetails: String
    }
    
    class LockNotOwnedException {
        +actualOwnerId: String
    }
    
    class LockNotFoundException {
        +reason: String
    }
    
    class StateLockException {
        +stateKey: String
        +currentState: Object
        +requestedState: Object
    }
    
    class LockConfigurationException {
        +parameter: String
        +value: Object
        +validationMessage: String
    }
    
    class LockSerializationException {
        +objectType: String
        +serializationFormat: String
    }
    
    class LockInternalException {
        +component: String
        +operation: String
    }
    
    class LockConversionException {
        +fromStamp: String
        +toStampType: String
        +reason: String
    }
    
    class StampValidationException {
        +stamp: String
        +reason: String
    }
    
    %% Error Handler
    class RedisLockErrorHandler {
        +translateException(ex: Throwable, lockKey: String, ownerId: String): AbstractRedisLockException
        +translateScriptException(ex: Throwable, scriptName: String, keys: List, args: List): AbstractRedisLockException
        +enrichException(ex: AbstractRedisLockException, details: Map): AbstractRedisLockException
    }
    
    %% Inheritance Relationships
    Throwable <|-- Exception
    Exception <|-- RuntimeException
    Exception <|-- MarkerNestedException
    RuntimeException <|-- MarkerNestedRuntimeException
    MarkerNestedRuntimeException <|-- AbstractRedisLockException
    ExceptionMarkerProvider <|.. MarkerNestedException
    ExceptionMarkerProvider <|.. MarkerNestedRuntimeException
    
    %% Specific Exception Inheritance
    AbstractRedisLockException <|-- LockAcquisitionException
    AbstractRedisLockException <|-- LockTimeoutException
    AbstractRedisLockException <|-- LockReleaseException
    AbstractRedisLockException <|-- LeaseExtensionException
    AbstractRedisLockException <|-- LockInterruptedException
    AbstractRedisLockException <|-- LockCommandException
    AbstractRedisLockException <|-- LockConnectionException
    AbstractRedisLockException <|-- LockNotOwnedException
    AbstractRedisLockException <|-- LockNotFoundException
    AbstractRedisLockException <|-- StateLockException
    AbstractRedisLockException <|-- LockConfigurationException
    AbstractRedisLockException <|-- LockSerializationException
    AbstractRedisLockException <|-- LockInternalException
    AbstractRedisLockException <|-- LockConversionException
    AbstractRedisLockException <|-- StampValidationException
    
    %% Error Handler Relationships
    RedisLockErrorHandler --> AbstractRedisLockException : creates
    RedisLockOperations --> RedisLockErrorHandler : uses
    
    %% Notes
    note for AbstractRedisLockException "Base exception with context-rich fields for structured logging"
    note for RedisLockErrorHandler "Translates Redis errors to specific exception types"
    note for ExceptionMarkerProvider "Provides Logstash markers for structured logging"
```

## Exception Flow

The exception handling in the Redis Locking module follows a structured approach:

1. **Error Source**: Redis operations may fail due to various reasons:
   - Connection issues
   - Command execution failures
   - Script execution errors
   - Timeout conditions
   - Validation failures
   - Ownership violations

2. **Error Translation**: `RedisLockErrorHandler` translates these errors into specific exception types:
   ```
   Redis Error → RedisLockErrorHandler.translateException() → Specific AbstractRedisLockException subtype
   ```

3. **Context Enrichment**: Exceptions are enriched with contextual information:
   - Lock key
   - Lock owner ID
   - Request UUID
   - Operation details
   - Timestamp
   - Additional metadata

4. **Structured Logging**: Exceptions implement `ExceptionMarkerProvider` to provide Logstash markers for structured logging:
   ```
   Exception → getMarker() → Structured JSON log entry with all context
   ```

5. **Client Handling**: Client code can catch specific exception types to handle different error scenarios:
   ```java
   try {
       lock.lock();
   } catch (LockTimeoutException e) {
       // Handle timeout specifically
   } catch (LockAcquisitionException e) {
       // Handle other acquisition failures
   } catch (AbstractRedisLockException e) {
       // Handle any lock-related exception
   }
   ```

## Exception Categories

1. **Acquisition Failures**:
   - `LockAcquisitionException`: General acquisition failure
   - `LockTimeoutException`: Timeout during acquisition
   - `LockInterruptedException`: Thread interrupted during acquisition

2. **Release Failures**:
   - `LockReleaseException`: Error during unlock operation
   - `LockNotOwnedException`: Attempt to unlock a lock owned by another client

3. **State Management Failures**:
   - `StateLockException`: Error during state lock operations
   - `LockConversionException`: Error converting between lock types
   - `StampValidationException`: Invalid or expired stamp

4. **Infrastructure Failures**:
   - `LockCommandException`: Redis command execution error
   - `LockConnectionException`: Redis connection issue
   - `LeaseExtensionException`: Error extending lock lease
   - `LockSerializationException`: Error serializing/deserializing data

5. **Configuration Failures**:
   - `LockConfigurationException`: Invalid configuration parameter
   - `LockInternalException`: Internal component failure