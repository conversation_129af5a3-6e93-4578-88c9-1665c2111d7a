# Destilink Framework Module Visualizer

A tool to visualize module dependencies in the Destilink Framework.

## Features

- Scans all framework modules and their dependencies
- Generates a visual dependency graph using Mermaid.js
- Creates JSON output for further processing
- Provides an interactive HTML visualization

## Usage

1. Make sure Node.js is installed
2. Run the visualizer:

```bash
cd utils/module-visualizer
node module_visualizer.js
```

3. Open the generated HTML file in your browser:
   `output/module-visualization.html`

## Output Files

- `output/module-dependencies.mmd` - Mermaid diagram source
- `output/module-dependencies.json` - JSON representation of modules
- `output/module-visualization.html` - Interactive HTML visualization

## Integration with Documentation

The generated Mermaid diagram can be included in the project's MkDocs documentation:

```markdown
# Module Dependencies

```mermaid
{{content_of_module-dependencies.mmd}}
```
```