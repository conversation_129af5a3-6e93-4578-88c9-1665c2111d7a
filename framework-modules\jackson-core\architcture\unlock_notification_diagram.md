# Redis Locking Module: Unlock Notification

This diagram illustrates the Unlock Notification mechanism in the `locking-redis-lock` module.

```mermaid
classDiagram
    %% Core Classes
    class UnlockMessageListener {
        -RedisMessageListenerContainer listenerContainer
        -Map~String, LockSemaphoreHolder~ lockSemaphores
        -String channelName
        -ObjectMapper objectMapper
        +start(): void
        +stop(): void
        +registerLock(lockKey: String): LockSemaphoreHolder
        +unregisterLock(lockKey: String): void
        +onMessage(message: Message, pattern: byte[]): void
        -handleUnlockMessage(unlockMessage: UnlockMessage): void
        -notifySemaphore(lockKey: String): void
    }
    
    class LockSemaphoreHolder {
        -Semaphore semaphore
        -AtomicInteger waitingThreads
        +acquire(): void
        +release(): void
        +incrementWaitingThreads(): int
        +decrementWaitingThreads(): int
        +getWaitingThreads(): int
        +hasWaitingThreads(): boolean
    }
    
    class UnlockMessage {
        -String lockKey
        -UnlockType unlockType
        -String ownerId
        +getLockKey(): String
        +getUnlockType(): UnlockType
        +getOwnerId(): String
    }
    
    class UnlockType {
        <<Enum>>
        +EXPLICIT
        +EXPIRED
        +FORCE
        +isExplicit(): boolean
        +isExpired(): boolean
        +isForced(): boolean
    }
    
    class UnlockMessageListenerManager {
        -Map~String, UnlockMessageListener~ listeners
        -RedisConnectionFactory connectionFactory
        -ObjectMapper objectMapper
        -RedisLockProperties properties
        +getOrCreateListener(bucket: String): UnlockMessageListener
        +removeListener(bucket: String): void
        -createListener(bucket: String): UnlockMessageListener
    }
    
    class RedisLockOperations {
        -RedisTemplate redisTemplate
        -ScriptLoader scriptLoader
        -ObjectMapper objectMapper
        +unlock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
        +unlockReadLock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
        +unlockWriteLock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
        +unlockStampedLock(lockKey: String, ownerId: String, stamp: String): CompletableFuture~Boolean~
        -publishUnlockMessage(lockKey: String, unlockType: UnlockType, ownerId: String): void
    }
    
    class RedisLockProperties {
        +getUnlockChannelPrefix(): String
        +getUnlockNotificationEnabled(): boolean
    }
    
    %% Lock Implementations
    class AbstractRedisLock {
        #waitForUnlock(lockKey: String, timeoutMs: long): boolean
        #registerForUnlockNotification(lockKey: String): LockSemaphoreHolder
        #unregisterFromUnlockNotification(lockKey: String): void
    }
    
    class RedisReentrantLock {
        -lockKey: String
        +tryLock(timeoutMs: long): boolean
        +unlock(): void
    }
    
    class RedisReadWriteLock {
        -lockKey: String
        +readLock(): ReadLock
        +writeLock(): WriteLock
    }
    
    class RedisStampedLock {
        -lockKey: String
        +tryReadLock(timeoutMs: long): long
        +tryWriteLock(timeoutMs: long): long
        +unlock(stamp: long): void
    }
    
    %% Configuration
    class RedisLockAutoConfiguration {
        +unlockMessageListenerManager(connectionFactory: RedisConnectionFactory, objectMapper: ObjectMapper, properties: RedisLockProperties): UnlockMessageListenerManager
    }
    
    %% Lifecycle Management
    class LockComponentRegistry {
        -UnlockMessageListenerManager unlockMessageListenerManager
        +registerUnlockMessageListenerManager(manager: UnlockMessageListenerManager): void
        +getUnlockMessageListenerManager(): UnlockMessageListenerManager
    }
    
    class ApplicationLifecycle {
        <<Spring>>
        +onApplicationEvent(event: ContextRefreshedEvent): void
        +onApplicationEvent(event: ContextClosedEvent): void
    }
    
    %% Redis Components
    class RedisMessageListenerContainer {
        <<Spring Data Redis>>
        +addMessageListener(listener: MessageListener, topic: Topic): void
        +removeMessageListener(listener: MessageListener): void
        +start(): void
        +stop(): void
    }
    
    class RedisTemplate {
        <<Spring Data Redis>>
        +convertAndSend(channel: String, message: Object): void
    }
    
    %% Relationships
    UnlockMessageListener *-- LockSemaphoreHolder : contains
    UnlockMessageListener --> UnlockMessage : processes
    UnlockMessageListener --> RedisMessageListenerContainer : uses
    
    UnlockMessage --> UnlockType : contains
    
    UnlockMessageListenerManager *-- UnlockMessageListener : manages
    UnlockMessageListenerManager --> RedisConnectionFactory : uses
    
    RedisLockOperations --> RedisTemplate : uses for publishing
    RedisLockOperations --> UnlockMessage : creates
    
    AbstractRedisLock --> UnlockMessageListenerManager : uses
    AbstractRedisLock --> LockSemaphoreHolder : uses
    RedisReentrantLock --|> AbstractRedisLock : extends
    RedisReadWriteLock --|> AbstractRedisLock : extends
    RedisStampedLock --|> AbstractRedisLock : extends
    
    RedisLockAutoConfiguration --> UnlockMessageListenerManager : creates
    LockComponentRegistry --> UnlockMessageListenerManager : manages
    ApplicationLifecycle --> UnlockMessageListener : controls lifecycle
    
    %% Notes
    note for UnlockMessageListener "Listens for unlock messages\non Redis Pub/Sub channels"
    note for LockSemaphoreHolder "Manages waiting threads\nfor a specific lock"
    note for UnlockType "Categorizes unlock operations\nby their cause"
```

## Unlock Notification Overview

The Unlock Notification mechanism is a critical component of the Redis Locking module that provides a non-polling way for waiting threads to be notified when a lock becomes available. This is implemented using Redis Pub/Sub (Publish/Subscribe) messaging, which allows for efficient notification across distributed systems without constant polling.

### Key Responsibilities

1. **Non-Polling Notification**: Notifies waiting threads when a lock is released without requiring polling
2. **Channel Management**: Creates and manages Redis Pub/Sub channels for each lock bucket
3. **Message Processing**: Deserializes and processes unlock messages
4. **Thread Coordination**: Manages waiting threads using semaphores
5. **Bucket Isolation**: Maintains separate listeners for each lock bucket

## Core Components

### UnlockMessageListener

The `UnlockMessageListener` class is responsible for listening to unlock messages on a Redis Pub/Sub channel:

```java
@Slf4j
public class UnlockMessageListener implements MessageListener {
    private final RedisMessageListenerContainer listenerContainer;
    private final Map<String, LockSemaphoreHolder> lockSemaphores = new ConcurrentHashMap<>();
    private final String channelName;
    private final ObjectMapper objectMapper;
    private volatile boolean running = false;
    
    public UnlockMessageListener(RedisConnectionFactory connectionFactory, 
                                String channelName,
                                ObjectMapper objectMapper) {
        this.listenerContainer = new RedisMessageListenerContainer();
        this.listenerContainer.setConnectionFactory(connectionFactory);
        this.listenerContainer.afterPropertiesSet();
        this.channelName = channelName;
        this.objectMapper = objectMapper;
    }
    
    public synchronized void start() {
        if (running) {
            return;
        }
        
        running = true;
        listenerContainer.addMessageListener(this, new ChannelTopic(channelName));
        listenerContainer.start();
        log.info("Started unlock message listener for channel: {}", channelName);
    }
    
    public synchronized void stop() {
        if (!running) {
            return;
        }
        
        running = false;
        listenerContainer.removeMessageListener(this);
        listenerContainer.stop();
        lockSemaphores.clear();
        log.info("Stopped unlock message listener for channel: {}", channelName);
    }
    
    public LockSemaphoreHolder registerLock(String lockKey) {
        return lockSemaphores.computeIfAbsent(lockKey, k -> new LockSemaphoreHolder());
    }
    
    public void unregisterLock(String lockKey) {
        lockSemaphores.remove(lockKey);
    }
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        if (!running) {
            return;
        }
        
        try {
            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
            UnlockMessage unlockMessage = objectMapper.readValue(messageBody, UnlockMessage.class);
            handleUnlockMessage(unlockMessage);
        } catch (Exception e) {
            log.error("Error processing unlock message", e);
        }
    }
    
    private void handleUnlockMessage(UnlockMessage unlockMessage) {
        String lockKey = unlockMessage.getLockKey();
        UnlockType unlockType = unlockMessage.getUnlockType();
        
        log.debug("Received unlock message for lock: {}, type: {}", lockKey, unlockType);
        
        notifySemaphore(lockKey);
    }
    
    private void notifySemaphore(String lockKey) {
        LockSemaphoreHolder semaphoreHolder = lockSemaphores.get(lockKey);
        if (semaphoreHolder != null && semaphoreHolder.hasWaitingThreads()) {
            semaphoreHolder.release();
            log.debug("Released semaphore for lock: {}", lockKey);
        }
    }
}
```

### LockSemaphoreHolder

The `LockSemaphoreHolder` class manages waiting threads for a specific lock:

```java
public class LockSemaphoreHolder {
    private final Semaphore semaphore = new Semaphore(0);
    private final AtomicInteger waitingThreads = new AtomicInteger(0);
    
    public void acquire() throws InterruptedException {
        semaphore.acquire();
    }
    
    public void release() {
        semaphore.release();
    }
    
    public int incrementWaitingThreads() {
        return waitingThreads.incrementAndGet();
    }
    
    public int decrementWaitingThreads() {
        return waitingThreads.decrementAndGet();
    }
    
    public int getWaitingThreads() {
        return waitingThreads.get();
    }
    
    public boolean hasWaitingThreads() {
        return waitingThreads.get() > 0;
    }
}
```

### UnlockMessage

The `UnlockMessage` class represents a message sent when a lock is released:

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnlockMessage {
    private String lockKey;
    private UnlockType unlockType;
    private String ownerId;
}
```

### UnlockType

The `UnlockType` enum categorizes unlock operations by their cause:

```java
public enum UnlockType {
    EXPLICIT,  // Explicit unlock by the owner
    EXPIRED,   // Lock expired (lease time elapsed)
    FORCE;     // Forced unlock (administrative action)
    
    public boolean isExplicit() {
        return this == EXPLICIT;
    }
    
    public boolean isExpired() {
        return this == EXPIRED;
    }
    
    public boolean isForced() {
        return this == FORCE;
    }
}
```

### UnlockMessageListenerManager

The `UnlockMessageListenerManager` class manages unlock message listeners for different lock buckets:

```java
@Slf4j
public class UnlockMessageListenerManager {
    private final Map<String, UnlockMessageListener> listeners = new ConcurrentHashMap<>();
    private final RedisConnectionFactory connectionFactory;
    private final ObjectMapper objectMapper;
    private final RedisLockProperties properties;
    
    public UnlockMessageListenerManager(RedisConnectionFactory connectionFactory,
                                       ObjectMapper objectMapper,
                                       RedisLockProperties properties) {
        this.connectionFactory = connectionFactory;
        this.objectMapper = objectMapper;
        this.properties = properties;
    }
    
    public UnlockMessageListener getOrCreateListener(String bucket) {
        if (!properties.getUnlockNotificationEnabled()) {
            return null;
        }
        
        return listeners.computeIfAbsent(bucket, this::createListener);
    }
    
    public void removeListener(String bucket) {
        UnlockMessageListener listener = listeners.remove(bucket);
        if (listener != null) {
            listener.stop();
        }
    }
    
    private UnlockMessageListener createListener(String bucket) {
        String channelName = properties.getUnlockChannelPrefix() + ":" + bucket;
        UnlockMessageListener listener = new UnlockMessageListener(
            connectionFactory, channelName, objectMapper);
        listener.start();
        return listener;
    }
}
```

## Integration with Lock Implementations

Lock implementations use the unlock notification mechanism to efficiently wait for locks to become available:

```java
public abstract class AbstractRedisLock {
    protected final LockComponentRegistry lockComponentRegistry;
    
    protected boolean waitForUnlock(String lockKey, long timeoutMs) throws InterruptedException {
        LockSemaphoreHolder semaphoreHolder = registerForUnlockNotification(lockKey);
        if (semaphoreHolder == null) {
            // Fallback to polling if notification is disabled
            return waitForUnlockPolling(lockKey, timeoutMs);
        }
        
        try {
            semaphoreHolder.incrementWaitingThreads();
            
            // Try once more before waiting
            if (tryAcquireLock()) {
                return true;
            }
            
            // Wait for notification
            if (timeoutMs > 0) {
                long startTime = System.currentTimeMillis();
                long remainingTime = timeoutMs;
                
                while (remainingTime > 0) {
                    // Wait with timeout
                    if (semaphoreHolder.tryAcquire(remainingTime, TimeUnit.MILLISECONDS)) {
                        // Try to acquire lock after notification
                        if (tryAcquireLock()) {
                            return true;
                        }
                    }
                    
                    // Update remaining time
                    remainingTime = timeoutMs - (System.currentTimeMillis() - startTime);
                }
                
                return false;
            } else {
                // Wait indefinitely
                while (true) {
                    semaphoreHolder.acquire();
                    
                    // Try to acquire lock after notification
                    if (tryAcquireLock()) {
                        return true;
                    }
                }
            }
        } finally {
            semaphoreHolder.decrementWaitingThreads();
            
            // Unregister if no more waiting threads
            if (!semaphoreHolder.hasWaitingThreads()) {
                unregisterFromUnlockNotification(lockKey);
            }
        }
    }
    
    protected LockSemaphoreHolder registerForUnlockNotification(String lockKey) {
        UnlockMessageListenerManager manager = 
            lockComponentRegistry.getUnlockMessageListenerManager();
        
        if (manager == null) {
            return null;
        }
        
        String bucket = extractBucketFromLockKey(lockKey);
        UnlockMessageListener listener = manager.getOrCreateListener(bucket);
        
        if (listener == null) {
            return null;
        }
        
        return listener.registerLock(lockKey);
    }
    
    protected void unregisterFromUnlockNotification(String lockKey) {
        UnlockMessageListenerManager manager = 
            lockComponentRegistry.getUnlockMessageListenerManager();
        
        if (manager == null) {
            return;
        }
        
        String bucket = extractBucketFromLockKey(lockKey);
        UnlockMessageListener listener = manager.getOrCreateListener(bucket);
        
        if (listener == null) {
            return;
        }
        
        listener.unregisterLock(lockKey);
    }
    
    private String extractBucketFromLockKey(String lockKey) {
        // Extract bucket from lock key (format: prefix:bucket:name)
        String[] parts = lockKey.split(":");
        return parts.length >= 2 ? parts[1] : "default";
    }
    
    protected abstract boolean tryAcquireLock();
}
```

## Publishing Unlock Messages

When a lock is released, an unlock message is published to the appropriate Redis channel:

```java
@Slf4j
public class RedisLockOperations {
    private final RedisTemplate<String, String> redisTemplate;
    private final ScriptLoader scriptLoader;
    private final ObjectMapper objectMapper;
    private final RedisLockProperties properties;
    
    // Other methods...
    
    public CompletableFuture<Boolean> unlock(String lockKey, String ownerId) {
        return CompletableFuture.supplyAsync(() -> {
            Boolean result = (Boolean) redisTemplate.execute(
                scriptLoader.getScript("unlock.lua"),
                Collections.singletonList(lockKey),
                ownerId);
            
            if (Boolean.TRUE.equals(result)) {
                publishUnlockMessage(lockKey, UnlockType.EXPLICIT, ownerId);
            }
            
            return Boolean.TRUE.equals(result);
        });
    }
    
    private void publishUnlockMessage(String lockKey, UnlockType unlockType, String ownerId) {
        if (!properties.getUnlockNotificationEnabled()) {
            return;
        }
        
        try {
            String bucket = extractBucketFromLockKey(lockKey);
            String channelName = properties.getUnlockChannelPrefix() + ":" + bucket;
            
            UnlockMessage message = new UnlockMessage(lockKey, unlockType, ownerId);
            String messageJson = objectMapper.writeValueAsString(message);
            
            redisTemplate.convertAndSend(channelName, messageJson);
            log.debug("Published unlock message for lock: {}, type: {}", lockKey, unlockType);
        } catch (Exception e) {
            log.error("Error publishing unlock message for lock: {}", lockKey, e);
        }
    }
    
    private String extractBucketFromLockKey(String lockKey) {
        // Extract bucket from lock key (format: prefix:bucket:name)
        String[] parts = lockKey.split(":");
        return parts.length >= 2 ? parts[1] : "default";
    }
}
```

## Configuration

The unlock notification behavior is configured through `RedisLockProperties`:

```java
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
@Validated
public class RedisLockProperties {
    /**
     * Whether to enable unlock notifications via Redis Pub/Sub.
     */
    private boolean unlockNotificationEnabled = true;
    
    /**
     * Prefix for Redis Pub/Sub channels used for unlock notifications.
     */
    private String unlockChannelPrefix = "redis-lock-unlock";
    
    // Getters and setters...
}
```

## Lifecycle Management

The unlock message listeners are started when the application context is refreshed and stopped when the context is closed:

```java
@Component
public class UnlockMessageListenerLifecycle implements ApplicationListener<ApplicationEvent> {
    private final UnlockMessageListenerManager unlockMessageListenerManager;
    
    public UnlockMessageListenerLifecycle(UnlockMessageListenerManager unlockMessageListenerManager) {
        this.unlockMessageListenerManager = unlockMessageListenerManager;
    }
    
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ContextRefreshedEvent) {
            // Listeners are created on-demand, no need to start them here
        } else if (event instanceof ContextClosedEvent) {
            // Stop all listeners
            unlockMessageListenerManager.stopAllListeners();
        }
    }
}
```

## Unlock Notification Flow

### 1. Registration

When a thread attempts to acquire a lock that is already held:

```java
public class RedisReentrantLock extends AbstractRedisLock {
    @Override
    public boolean tryLock(long timeoutMs) throws InterruptedException {
        // Try to acquire the lock
        if (tryAcquireLock()) {
            return true;
        }
        
        // If lock is already held, wait for unlock notification
        return waitForUnlock(lockKey, timeoutMs);
    }
}
```

The thread registers with the unlock notification system:

1. Get or create an `UnlockMessageListener` for the lock's bucket
2. Register the lock with the listener to get a `LockSemaphoreHolder`
3. Increment the waiting thread count in the semaphore holder
4. Wait for notification by calling `semaphoreHolder.acquire()`

### 2. Unlock and Notification

When a lock is released:

```java
public class RedisReentrantLock extends AbstractRedisLock {
    @Override
    public void unlock() {
        // Unlock the lock
        redisLockOperations.unlock(lockKey, ownerId)
            .thenAccept(result -> {
                if (result) {
                    // Unlock successful
                    unregisterFromWatchdog(lockKey);
                }
            });
    }
}
```

The unlock process:

1. Execute the unlock Lua script in Redis
2. If successful, publish an unlock message to the appropriate channel
3. The message includes the lock key, unlock type, and owner ID

### 3. Message Processing

When an unlock message is received:

1. The `UnlockMessageListener` deserializes the message
2. It looks up the `LockSemaphoreHolder` for the lock key
3. If there are waiting threads, it releases the semaphore
4. Waiting threads are notified and attempt to acquire the lock

### 4. Lock Acquisition After Notification

When a thread is notified:

1. The thread wakes up from `semaphoreHolder.acquire()`
2. It attempts to acquire the lock
3. If successful, it returns true
4. If unsuccessful (another thread got the lock first), it waits for another notification

### 5. Unregistration

When a thread is done waiting:

1. It decrements the waiting thread count
2. If no more threads are waiting, it unregisters the lock from the listener

## Key Design Considerations

### 1. Bucket-Based Channels

The system uses bucket-based Redis Pub/Sub channels to reduce the number of channels:

```
redis-lock-unlock:{bucket}
```

This approach:
- Groups locks by bucket to reduce the number of channels
- Allows for efficient message routing
- Supports multi-tenant applications with isolated buckets

### 2. Thread Safety

The system uses thread-safe collections and synchronization to handle concurrent operations:

```java
private final Map<String, LockSemaphoreHolder> lockSemaphores = new ConcurrentHashMap<>();
private final AtomicInteger waitingThreads = new AtomicInteger(0);
```

### 3. Fallback to Polling

If unlock notification is disabled, the system falls back to polling:

```java
protected boolean waitForUnlock(String lockKey, long timeoutMs) throws InterruptedException {
    LockSemaphoreHolder semaphoreHolder = registerForUnlockNotification(lockKey);
    if (semaphoreHolder == null) {
        // Fallback to polling if notification is disabled
        return waitForUnlockPolling(lockKey, timeoutMs);
    }
    
    // Wait for notification...
}
```

### 4. Efficient Resource Management

The system efficiently manages resources:

- Listeners are created on-demand for each bucket
- Locks are registered only when threads are waiting
- Locks are unregistered when no more threads are waiting
- Listeners are stopped when the application shuts down

### 5. Error Handling

The system handles errors gracefully:

```java
try {
    String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
    UnlockMessage unlockMessage = objectMapper.readValue(messageBody, UnlockMessage.class);
    handleUnlockMessage(unlockMessage);
} catch (Exception e) {
    log.error("Error processing unlock message", e);
}
```

## Sequence of Operations

1. **Lock Acquisition Attempt**:
   - Thread attempts to acquire a lock
   - If the lock is already held, register for unlock notification
   - Increment waiting thread count
   - Wait for notification

2. **Lock Release**:
   - Lock owner releases the lock
   - Redis Lua script removes the lock
   - Publish unlock message to Redis channel

3. **Message Processing**:
   - `UnlockMessageListener` receives the message
   - Deserialize the message
   - Look up the `LockSemaphoreHolder` for the lock
   - Release the semaphore to notify waiting threads

4. **Lock Acquisition After Notification**:
   - Waiting thread wakes up
   - Attempt to acquire the lock
   - If successful, proceed with the operation
   - If unsuccessful, wait for another notification

5. **Cleanup**:
   - Decrement waiting thread count
   - If no more waiting threads, unregister the lock
   - When application shuts down, stop all listeners

## Conclusion

The Unlock Notification mechanism provides an efficient way for waiting threads to be notified when a lock becomes available, without requiring constant polling. It uses Redis Pub/Sub messaging to provide real-time notifications across distributed systems, improving performance and reducing resource usage.