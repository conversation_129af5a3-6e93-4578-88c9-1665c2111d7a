# Redis Locking Module: Final Modernization Plan & Assessment

## 1. Introduction

This document outlines the key modernization tasks and the preceding assessment for the `locking-redis-lock` module. The plan is based on a thorough analysis of previous implementations and aims to align the module with current Destilink Framework best practices, improve maintainability, ensure robustness, and implement a comprehensive logging and metrics strategy.

## 2. Modernization Assessment Summary

An assessment of prior Redis Locking Module implementations revealed several key areas requiring improvement:

*   **Inefficient Lock Acquisition (Polling)**: Previous reliance on polling Redis for lock status led to high Redis load and inefficient client resource usage.
    *   **Modernized Solution**: Notification-based mechanism using Redis Pub/Sub and semaphores.
*   **Lack of Atomicity in Key Operations**: Some multi-command operations were not atomic, risking race conditions.
    *   **Modernized Solution**: Utilization of Redis Lua scripts for atomic operations.
*   **Flawed Reentrancy Implementation (ThreadLocal)**: Storing reentrancy counts in `ThreadLocal` is incorrect for distributed systems. This primarily impacted conceptual predecessors to `RedisReentrantLock`.
    *   **Modernized Solution**: Storing reentrancy count and owner information in a Redis Hash associated with the lock key. For `RedisReentrantLock` and the write lock component of `RedisReadWriteLock`, this ensures correct distributed reentrancy. For `RedisReadWriteLock`, this also includes managing reentrancy for individual readers and the writer, along with a new granular TTL mechanism for each read lock instance.
*   **Limited Exception Handling**: Previous exceptions lacked granularity and context.
    *   **Modernized Solution**: Comprehensive exception hierarchy with `ExceptionMarkerProvider` for structured logging, managed by `RedisLockErrorHandler`.
*   **Configuration Complexity**: Configuration was not always clear or flexible.
    *   **Modernized Solution**: Clearer structure via `RedisLockProperties` (global YAML) and programmatic builder-based configuration for buckets and instances.
*   **Lack of Detailed Metrics**: Insufficient visibility into runtime behavior.
    *   **Modernized Solution**: Detailed metrics exposure via Micrometer.
*   **Ambiguity in `lock()` Method Timeout**: Potential for unintended timeouts.
    *   **Modernized Solution**: `lock()` method strictly adheres to `java.util.concurrent.Lock` by blocking indefinitely; `tryLock(timeout, unit)` provides explicit timeout behavior.

The assessment concluded that a comprehensive refactoring was necessary to build a more robust, efficient, and maintainable distributed locking solution, leading to this consolidated modernization plan.

## 3. Key Modernization Tasks

### 3.1. Refactor Spring Auto-Configuration (`RedisLockAutoConfiguration.java`)

*   **Action**:
    1.  Remove `@ComponentScan`.
    2.  Explicitly define all shared beans (`RedisLockOperationsImpl`, `ScriptLoader`, `LockBucketRegistry`, `LockWatchdog`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `UnlockMessageListenerManager`, `LockComponentRegistry`, `LockMonitor`) using `@Bean` methods.
    3.  Apply appropriate `@ConditionalOn...` annotations.
    4.  Register `RedisLockAutoConfiguration` in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.

### 3.2. Implement Central `LockComponentRegistry`

*   **Action**:
    1.  Create `LockComponentRegistry` as a Spring bean.
    2.  Inject all shared services into it.
    3.  Refactor `LockBucketRegistry`, `AbstractRedisLock`, and other components to obtain shared service dependencies from `LockComponentRegistry`.

### 3.3. Implement Bucket-Level `UnlockMessageListener` and Management

*   **Action**:
    1.  `UnlockMessageListenerManager` (bean) to manage `UnlockMessageListener` instances.
    2.  Create one `UnlockMessageListener` **per configured lock bucket**.
    3.  Each `UnlockMessageListener` (for a bucket) subscribes to a channel pattern like `<prefix>:<bucketName>:__unlock_channels__:*` and internally manages a map of `lockKey` to `LockSemaphoreHolder` instances, parsing the `lockKey` (i.e., the specific lock identifier like `{<lockName>}`) from the **channel name** it received the message on (the message payload itself contains only the `UnlockType`).
    4.  `LockSemaphoreHolder` instances (keyed by lock key) are managed within their respective bucket's `UnlockMessageListener` and can use a mechanism like Guava `CacheBuilder.newBuilder().weakValues().build()` for GC eligibility if appropriate for the listener's internal map.

### 3.4. Refine Exception Hierarchy & Integrate Structured Logging

*   **Action**:
    1.  Ensure `AbstractRedisLockException` is the base, implements `ExceptionMarkerProvider` (ideally extends `MarkerNestedRuntimeException`).
    2.  Enforce contextual fields (`lockName`, `lockType`, `lockOwnerId`, `requestUuid`, etc.) in all lock exception constructors.
    3.  Implement `getMarker()` in `AbstractRedisLockException` (and `populateSpecificMarkers(Map<String, Object> contextMap)` hook for subclasses) for rich SLF4J Markers.
    4.  Update `RedisLockErrorHandler` to translate underlying exceptions into these structured, contextual, marker-providing exceptions.

### 3.5. Align Configuration, Watchdog, and Lua Scripts

*   **Action**:
    1.  `RedisLockProperties.java`: Reflects global properties (e.g., `leaseTime`, `retryInterval`, `watchdogMaxTtl`, `watchdogRenewalMultiplier`, `stateKeyExpiration`). **Remove YAML-based map for `buckets`.**
    2.  `LockBucketConfig.java`: Initialized with global defaults, allows programmatic overrides for bucket-level defaults via `LockBucketBuilder`.
    3.  `LockWatchdog.java`:
        *   Lease extension interval calculation uses global `watchdogMaxTtl` and `watchdogRenewalMultiplier`.
        *   The `newLeaseTimeMillis` argument passed to `extend_lock.lua` by the watchdog **MUST** be the global `watchdogMaxTtl`.
    4.  Verify watchdog activation conditions: global `watchdogEnabled`, bucket `useWatchdog` policy, application-instance-bound lock, and instance `leaseTime > watchdogMaxTtl`.

### 3.6. Review and Refine Lua Scripts

*   **Action**:
    1.  Review all Lua scripts for correctness, atomicity, efficiency, and proper handling of arguments (especially regarding lease times for watchdog). This includes the new scripts/logic for `RedisReadWriteLock` (`try_read_lock.lua`, `unlock_read_lock.lua`) managing individual read lock timeout keys and the main lock hash TTL.
    2.  Ensure `unlock.lua` (and variants like `unlock_read_lock.lua`, `unlock_write_lock.lua`) correctly publish messages (containing *only* the `UnlockType`) to the specific lock's Pub/Sub channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`). The `lockKey` is derived from the channel name by the listener.
    3.  Update `lua_scripts.md` to accurately document each script, including the detailed logic for `RedisReadWriteLock` scripts.

### 3.7. Enhance `LockSemaphoreHolder` Waiting Logic

*   **Action**:
    1.  Ensure `LockSemaphoreHolder.waitForUnlock()` correctly drains stale permits (`semaphore.drainPermits()`) before `tryAcquire`.
    2.  The actual wait duration for the semaphore will be determined by `AbstractRedisLock` based on the lock's current TTL (from script) or `retryInterval`.

### 3.8. Implement Comprehensive Logging & Metrics Strategy

*   **Action**:
    1.  **MDC Setup**: Utilize `LockContextDecorator` (aligning with `AbstractContextDecorator`) to populate MDC with `lock.name`, `lock.operation`, etc., using try-with-resources `Scope`.
    2.  **Parameterized Logging**: Enforce SLF4J parameterized messages.
    3.  **Structured Exception Logging**: Leverage `ExceptionMarkerProvider` integration.
    4.  **Log Levels**: Assign appropriate log levels (`DEBUG`, `INFO`, `WARN`, `ERROR`).
    5.  **Metrics**: Implement `LockMonitor` bean to expose key operational metrics via Micrometer (see `metrics.md`).

### 3.9. General Code Modernization

*   **Action**:
    *   **Testing**: Ensure comprehensive test coverage (unit, integration, performance) as per `testing_strategy.md`.
    *   **Java Features**: Effectively use `Optional`, Stream API, lambdas.
    *   **Code Cleanup**: Remove any vestiges of unused patterns or libraries (e.g., Resilience4j if previously used and now obsolete).
    *   Ensure adherence to Destilink Framework coding standards and naming conventions.

## 4. Verification

*   Validate against Destilink Framework guidelines.
*   Conduct thorough testing as outlined in the [Testing Strategy](testing_strategy.md).

This modernization plan aims to deliver a significantly improved `locking-redis-lock` module that is robust, maintainable, performant, and compliant with framework standards.