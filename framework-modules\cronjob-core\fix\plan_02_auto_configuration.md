# Plan: Redis Lock - Step 2: Auto-Configuration and Component Registry

This step focuses on creating a single, clean auto-configuration class and a central component registry, strictly following framework guidelines.

### Step 2.1: Consolidate into a Single `RedisLockAutoConfiguration`

The existing code has two auto-configuration classes. We will merge them into one, which will define all shared beans.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockAutoConfiguration.java`

**Action:**
1.  Delete the file `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/config/RedisReentrantLockAutoConfiguration.java`.
2.  Replace the content of `RedisLockAutoConfiguration.java` with the structure below. This new structure will define all necessary beans conditionally.

```java
package com.tui.destilink.framework.locking.redis.config;

import com.tui.destilink.framework.locking.redis.service.*;
import com.tui.destilink.framework.locking.redis.service.impl.*;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.Executor;

@AutoConfiguration
@EnableConfigurationProperties(RedisLockProperties.class)
@ConditionalOnClass(ClusterCommandExecutor.class)
@ConditionalOnProperty(prefix = "destilink.fw.locking.redis", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RedisLockAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ScriptLoader scriptLoader() {
        return new ScriptLoaderImpl();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisLockOperations redisLockOperations(ClusterCommandExecutor executor, ScriptLoader loader) {
        return new RedisLockOperationsImpl(executor, loader);
    }

    @Bean
    @ConditionalOnMissingBean
    public DefaultLockOwnerSupplier defaultLockOwnerSupplier() {
        return new DefaultLockOwnerSupplier();
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisLockErrorHandler redisLockErrorHandler() {
        return new RedisLockErrorHandler();
    }

    @Bean
    @ConditionalOnMissingBean
    public LockMonitor lockMonitor(ObjectProvider<MeterRegistry> meterRegistry) {
        return new DefaultLockMonitor(meterRegistry.getIfAvailable());
    }
    
    @Bean(name = "redisLockMessageExecutor")
    @ConditionalOnMissingBean(name = "redisLockMessageExecutor")
    public Executor redisLockMessageExecutor() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(Runtime.getRuntime().availableProcessors());
        scheduler.setThreadNamePrefix("redis-lock-msg-");
        scheduler.setDaemon(true);
        scheduler.initialize();
        return scheduler;
    }

    @Bean
    @ConditionalOnMissingBean
    public UnlockMessageListenerManager unlockMessageListenerManager(RedisConnectionFactory connectionFactory, Executor redisLockMessageExecutor) {
        return new UnlockMessageListenerManager(connectionFactory, redisLockMessageExecutor);
    }

    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "destilink.fw.locking.redis.watchdog", name = "enabled", havingValue = "true", matchIfMissing = true)
    public LockWatchdog lockWatchdog(RedisLockOperations ops, RedisLockErrorHandler handler, LockMonitor monitor, RedisLockProperties props) {
        return new LockWatchdog(ops, handler, monitor, props);
    }

    @Bean
    @ConditionalOnMissingBean
    public LockComponentRegistry lockComponentRegistry(
            ScriptLoader scriptLoader,
            UnlockMessageListenerManager unlockMessageListenerManager,
            ObjectProvider<LockWatchdog> lockWatchdog,
            RedisLockOperations redisLockOperations,
            DefaultLockOwnerSupplier defaultLockOwnerSupplier,
            RedisLockErrorHandler redisLockErrorHandler,
            ObjectProvider<LockMonitor> lockMonitorProvider) {
        return new LockComponentRegistry(scriptLoader, unlockMessageListenerManager, lockWatchdog.getIfAvailable(),
                redisLockOperations, defaultLockOwnerSupplier, redisLockErrorHandler, lockMonitorProvider);
    }

    @Bean
    @ConditionalOnMissingBean
    public LockBucketRegistry lockBucketRegistry(LockComponentRegistry componentRegistry, RedisLockProperties globalProperties) {
        return new LockBucketRegistry(componentRegistry, globalProperties);
    }
}
```

### Step 2.2: Implement `LockComponentRegistry.java`

This class acts as a central holder for shared services.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockComponentRegistry.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;

@RequiredArgsConstructor
@Getter
public class LockComponentRegistry {
    private final ScriptLoader scriptLoader;
    private final UnlockMessageListenerManager unlockMessageListenerManager;
    private final LockWatchdog lockWatchdog;
    private final RedisLockOperations redisLockOperations;
    private final DefaultLockOwnerSupplier defaultLockOwnerSupplier;
    private final RedisLockErrorHandler redisLockErrorHandler;
    private final ObjectProvider<LockMonitor> lockMonitorProvider;
}
```

---
