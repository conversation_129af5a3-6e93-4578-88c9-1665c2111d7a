package com.tui.destilink.framework.locking.redis.lock.test;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

/**
 * Test application for Redis lock module testing.
 * <p>
 * This application provides the Spring Boot context needed for integration
 * tests
 * of the Redis lock implementations. It includes the necessary
 * auto-configuration
 * for Redis connectivity and lock operations.
 * </p>
 */
@SpringBootApplication
@Import(TestRedisLockConfiguration.class)
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
