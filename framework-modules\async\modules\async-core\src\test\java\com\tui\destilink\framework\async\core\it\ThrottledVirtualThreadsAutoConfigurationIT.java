package com.tui.destilink.framework.async.core.it;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(OutputCaptureExtension.class)
@SpringBootTest(classes = {TestApplication.class})
class ThrottledVirtualThreadsAutoConfigurationIT {

    @Autowired
    private AsyncService asyncService; // should be our auto-config ThrottledVirtualThreadsExecutor

    @Test
    void shouldGetThrottledVirtualThreadExecutor() throws InterruptedException, ExecutionException, TimeoutException {
        assertThat(this.asyncService.runAsync().get(1, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    void shouldLogExceptionForVoidAsync(CapturedOutput output) {

        // when
        this.asyncService.exceptionAsync();

        // wait for failing thread and the logger to succeed
        Awaitility.await().untilAsserted(() -> {
            assertThat(output.getOut()).contains(AsyncService.EXCEPTION_MESSAGE);
        });
    }
}
