# Redis Locking Module: Final Architecture Overview

## 1. Introduction

This document outlines the consolidated architecture for the `locking-redis-lock` module within the Destilink Framework. It provides a robust, performant, and developer-friendly distributed locking mechanism using Redis. The architecture emphasizes shared, Spring-managed components, efficient non-polling lock acquisition, clear configuration hierarchies, atomicity through Lua scripts, and strict adherence to **Destilink Framework guidelines (`/.amazonq/rules/guidelines.md`)**.

This architecture adopts an **Async-First approach**, where all core lock operations are fundamentally asynchronous using `CompletableFuture`s, and synchronous `java.util.concurrent.Lock` implementations act as wrappers. It mandates the use of the internal `redis-core` module, particularly `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all Redis interactions, aligning with its asynchronous nature. This architecture synthesizes the best aspects of previous planning phases and the new, explicit requirements.

## 2. Core Architectural Principles

*   **Strict Guideline Adherence**: The implementation strictly follows all rules and patterns defined in `/.amazonq/rules/guidelines.md`, including but not limited to, component scanning prohibition, explicit bean definitions, and dependency injection best practices.
*   **Async-First Design**: All lock operations are primarily implemented asynchronously using `CompletableFuture`s. The standard `java.util.concurrent.locks.Lock` interface methods (`lock()`, `tryLock()`, `unlock()`) are implemented as synchronous wrappers around these asynchronous operations.
*   **Mandatory `redis-core` Usage**: All interactions with Redis MUST use the `com.tui.destilink.framework.redis.core` module, specifically `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` for all command executions, leveraging its asynchronous capabilities. `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java` will be used for shared Redis configuration.
*   **Atomicity**: Critical lock operations (acquire, release, extend) are implemented using Redis Lua scripts to ensure atomicity and prevent race conditions.
*   **Spring-Managed Shared Components**: Critical, stateless, or resource-intensive components (`ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `LockMonitor`, `LockComponentRegistry`) are implemented as Spring-managed singleton beans, configured explicitly within `RedisLockAutoConfiguration`. `RedisLockOperations` will delegate all Redis command execution to `ClusterCommandExecutor` from `redis-core`.
*   **Centralized Component Access**: A `LockComponentRegistry` bean acts as a central holder for these shared services, simplifying dependency injection into lock implementations.
*   **Efficient Non-Polling Lock Acquisition**: Lock acquisition primarily relies on Redis Pub/Sub for unlock notifications, managed via `UnlockMessageListener` (one per bucket, managed by `UnlockMessageListenerManager`) and `LockSemaphoreHolder`. The `UnlockMessageListener` subscribes to a pattern for bucket-specific unlock channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`). Locks publish to a specific channel like `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`. Messages on this channel contain the `UnlockType`, allowing the listener to signal the appropriate `LockSemaphoreHolder` (one per lock key with waiters), minimizing direct polling. A fallback re-polling mechanism handles potential missed notifications.
*   **Contextual and Structured Exception Handling**: A defined exception hierarchy, rooted in `AbstractRedisLockException` (which implements `ExceptionMarkerProvider`), provides detailed context for improved diagnostics and structured logging.
*   **Explicit Configuration & Dependency Injection**: Strict adherence to Destilink Framework guidelines for auto-configuration:
    *   `@AutoConfiguration` for the module's entry point (`RedisLockAutoConfiguration`).
    *   Explicit `@Bean` definitions for all managed components.
    *   No use of `@ComponentScan` within the module.
    *   Constructor injection is preferred.
    *   Registration in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.
*   **Standardized Key Construction via `redis-core`**: All Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. It is important to note that these specific key construction classes are, as of the current implementation, primarily utilized by the `locking-redis-lock` module. Should specific needs arise, or if other modules begin to leverage these classes extensively, they can be further reviewed and optimized for broader framework applicability or specialized performance characteristics.
*   **Clear Configuration Hierarchy**: Global YAML settings (`destilink.fw.locking.redis.*`) provide base defaults. Programmatic builders for buckets and lock instances allow overriding specific, permissible properties. `RedisCoreProperties` will inform the base Redis setup.
*   **Lightweight Lock Instances & Resource Sharing**: Concrete lock implementations (e.g., `RedisReentrantLock`, `RedisStateLock`) and their base `AbstractRedisLock` are designed to be lightweight, short-lived objects. Given the potential for a large number of lock instances, they MUST share all possible resources. This is achieved by delegating all stateful, resource-intensive, or shared logic (like Redis communication, script loading, listener management, watchdog scheduling) to the Spring-managed singleton beans accessible via the `LockComponentRegistry`. This minimizes the per-instance overhead of each lock object.
*   **Distributed Reentrancy**: Reentrancy for locks like `RedisReentrantLock` is managed using Redis Hashes to store reentrancy count and owner information, ensuring correctness across distributed instances.
*   **Enhanced Logging & Observability**: Comprehensive logging strategy using SLF4J, MDC via `LockContextDecorator`, and structured exception details via `ExceptionMarkerProvider`. Metrics are exposed via Micrometer.
*   **Synchronous `lock()` Method as Wrapper**: The `lock()` method (and other `java.util.concurrent.Lock` methods) now acts as a blocking wrapper around the asynchronous `CompletableFuture`-based implementations. It will block indefinitely until the underlying asynchronous lock acquisition completes or is interrupted. Timeout behavior is provided by `tryLock(long timeout, TimeUnit unit)`.
*   **Asynchronous API as Primary**: Offers an `AsyncLock` interface, extending the standard `Lock` interface with `CompletableFuture`-based non-blocking operations. This is the primary implementation approach, allowing for improved resource utilization in highly concurrent, reactive applications by offloading waiting to the `CompletableFuture`'s completion stages.

## 3. Component Diagram

```mermaid
graph TD
    subgraph ApplicationCode ["Application Code"]
        AppLockUser["Service/User of Lock"];
    end

    subgraph LockingModuleFacade ["Locking Module Facade (Builders & Registry)"]
        LBR["<code>LockBucketRegistry</code> (Bean)"];
        LBB["<code>LockBucketBuilder</code>"];
        LCB["<code>LockConfigBuilder</code>"];
        ALTCB["<code>AbstractLockTypeConfigBuilder</code> & Subclasses"];
    end

    subgraph CoreLockingLogic ["Core Locking Logic (Async-First)"]
        direction LR;
        ARL["<code>AbstractRedisLock</code> (Base Class)"];
        RLI["Concrete Lock Implementations<br>(e.g., `RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`)<br>ALL Implement `AsyncLock` and wrap `java.util.concurrent.locks.Lock`."];
        LSH["<code>LockSemaphoreHolder</code> (Per Lock Key, Guava Cache Managed)"];
    end
    
    subgraph SharedSpringServices ["Shared Spring-Managed Services"]
        direction LR;
        LCR["<code>LockComponentRegistry</code> (Bean)"];
        SL["<code>ScriptLoader</code> (Bean)"];
        UMLM["<code>UnlockMessageListenerManager</code> (Bean)"];
        UML["<code>UnlockMessageListener</code> (Per Bucket, Managed by UMLM)"];
        LW["<code>LockWatchdog</code> (Bean)"];
        ROps["<code>RedisLockOperations</code> (Bean)"];
        LMP["<code>LockMonitor</code> (Bean, Optional, Micrometer Metrics)"];
        LOS["<code>DefaultLockOwnerSupplier</code> (Bean)"];
        LERRH["<code>RedisLockErrorHandler</code> (Bean)"];
    end

    subgraph Configuration ["Configuration & AutoConfiguration"]
        direction LR;
        RLP["<code>RedisLockProperties</code> (Global YAML-backed)"];
        LBC["<code>LockBucketConfig</code> (Programmatically Resolved for Bucket)"];
        RAutoConfig["<code>RedisLockAutoConfiguration</code> (@AutoConfiguration)"];
        RCProps["<code>RedisCoreProperties</code> (from redis-core)"];
    end

    subgraph Exceptions ["Exception Handling"]
        BaseExc["<code>AbstractRedisLockException</code> (implements ExceptionMarkerProvider)"];
        SpecExc["Specialized Lock Exceptions..."];
    end

    subgraph Logging ["Logging Infrastructure"]
        SLF4J["SLF4J API"];
        MDC["MDC (via LockContextDecorator)"];
        EMP["ExceptionMarkerProvider (via Exceptions)"];
        CoreLogging["Destilink Core Logging (ExceptionLogstashMarkersJsonProvider)"];
    end

    subgraph ExternalSystems ["External Systems"]
        Redis["Redis (Cluster)"];
        CCExec["<code>ClusterCommandExecutor</code> (from redis-core)"];
    end

    AppLockUser --> LBR;
    LBR -- "creates" --> LBB;
    LBB --> LCB;
    LCB --> ALTCB;
    ALTCB -- ".build() creates" --> RLI;
    RLI -- "extends" --> ARL;
    
    ARL -- "uses services from" --> LCR;
    ARL -- "uses for waiting" --> LSH;
    ARL -- "throws" --> Exceptions;
    ARL -- "logs via" --> SLF4J;

    LCR -- "provides" --> SL;
    LCR -- "provides" --> UMLM;
    LCR -- "provides" --> LW;
    LCR -- "provides" --> ROps;
    LCR -- "provides (optional)" --> LMP;
    LCR -- "provides" --> LOS;
    LCR -- "provides" --> LERRH;

    UMLM -- "manages & provides" --> UML;
    UML -- "manages `LockSemaphoreHolder` map via Guava Cache" --> LSH;
    UML -- "listens to Pub/Sub from" --> Redis;
    UML -- "signals" --> LSH;
    
    RAutoConfig -- "defines bean" --> LBR;
    RAutoConfig -- "defines bean" --> LCR;
    RAutoConfig -- "defines bean" --> SL;
    RAutoConfig -- "defines bean" --> UMLM;
    RAutoConfig -- "defines bean" --> LW;
    RAutoConfig -- "defines bean" --> ROps;
    RAutoConfig -- "defines bean (conditional)" --> LMP;
    RAutoConfig -- "defines bean" --> LOS;
    RAutoConfig -- "defines bean" --> LERRH;
    RAutoConfig -- "enables" --> RLP;
    RAutoConfig -- "uses" --> RCProps;

    RLP -- "provides global defaults for" --> LBC;
    LBR -- "initializes & resolves config into" --> LBC;
    LBC -- "provides config to" --> LBB;

    ROps -- "uses" --> SL;
    ROps -- "interacts with" --> CCExec;
    ROps -- "uses" --> LERRH;
    LW -- "extends lease in" --> CCExec;
    
    Exceptions -- "are subclasses of" --> BaseExc;
    BaseExc -- "provides marker to" --> EMP;
    EMP -- "consumed by" --> CoreLogging;
    SLF4J -- "writes to" --> MDC;
    SLF4J -- "integrates with" --> CoreLogging;
    CCExec -- "communicates with" --> Redis;


    style AppLockUser fill:#lightgrey;
    style LBR fill:#lightblue;
    style LBB fill:#lightblue;
    style LCB fill:#lightblue;
    style ALTCB fill:#lightblue;
    style ARL fill:#adebad;
    style RLI fill:#adebad;
    style LSH fill:#adebad;
    style LCR fill:#ccffcc;
    style SL fill:#ffffcc;
    style UMLM fill:#ffffcc;
    style UML fill:#ffffcc;
    style LW fill:#ffffcc;
    style ROps fill:#ffffcc;
    style LMP fill:#ffffcc;
    style LOS fill:#ffffcc;
    style LERRH fill:#ffffcc;
    style RLP fill:#ffcc99;
    style LBC fill:#fdd;
    style RAutoConfig fill:#ffcc99;
    style RCProps fill:#ffcc99;
    style Exceptions fill:#ffdddd;
    style Logging fill:#e6e6fa;
    style Redis fill:#ffcccc;
    style CCExec fill:#ffcccc;
```

## 4. Key Components and Responsibilities

(Detailed descriptions of each component, drawing from both Plan 1 and Plan 2, will be elaborated in the `implementation_details.md` and other specific documents. This section provides a high-level summary.)

*   **Configuration Components**: `RedisLockProperties`, `LockBucketConfig`, `RedisLockAutoConfiguration`, and leveraging `RedisCoreProperties` from `redis-core`.
*   **Shared Spring-Managed Services**: `LockComponentRegistry`, `ScriptLoader`, `UnlockMessageListenerManager` (Manages the lifecycle of `UnlockMessageListener` instances. Ensures that an `UnlockMessageListener` is active and subscribed to the pattern for bucket-specific unlock channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`).), `UnlockMessageListener` (A component instantiated by `UnlockMessageListenerManager` for each configured lock bucket. Subscribes to a pattern for Pub/Sub channels specific to its designated bucket (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`) and signals the corresponding `LockSemaphoreHolder` (from an internal map) upon receiving an unlock message (containing the specific `lockKey`) within that bucket.), `LockWatchdog`, `RedisLockOperations` (**which now exclusively uses `ClusterCommandExecutor` for all Redis commands**), `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `LockMonitor`.
*   **Core Locking Logic & Instantiation**: `LockBucketRegistry`, `LockBucketBuilder`, `LockConfigBuilder`, `AbstractLockTypeConfigBuilder` (and subclasses), `AbstractRedisLock`, Concrete Lock Implementations (e.g., `RedisReentrantLock` - **all these implementations MUST implement the `AsyncLock` interface, providing asynchronous counterparts to the standard lock methods, with synchronous methods acting as wrappers**), `LockSemaphoreHolder`.
*   **Exception Handling**: `AbstractRedisLockException` and its specialized subclasses.

## 5. Redis Key Schema

The module employs a structured Redis key schema for clarity, collision avoidance, and Redis Cluster compatibility. All Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. As noted in the architectural principles, these key construction classes from `redis-core` are currently primarily utilized by the locking module and can be further optimized if specific needs arise or their usage expands significantly across the framework.
(Details are in `redis_key_schema.md`, reflecting the data key structure: `<prefix>:<bucketName>:<namespace>:{<lockName>}[:<suffix>]` and subscribes to a Pub/Sub channel pattern like `<prefix>:<bucketName>:__unlock_channels__:*`. Locks publish to specific channels like `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}` (with messages on this channel containing the specific lock key). Implemented by `LockBucket.java` using `RedisKeyPrefix` from `redis-core`, and deriving prefixes from `RedisCoreProperties`).

## 6. Data Flow and Interactions

Key operational flows include:

*   **Lock Acquisition**: Detailed in `lock_acquisition.md`. The primary flow will be asynchronous, with synchronous calls wrapping `CompletableFuture`s.
*   **Lock Release & Unlock Notification**: Detailed in `messaging.md`. All Redis commands for release will be executed via `ClusterCommandExecutor`.
*   **Lease Extension (Watchdog)**: Detailed in `watchdog.md`. Watchdog operations will also use `ClusterCommandExecutor`.
*   **Asynchronous Lock Operations**: For locks implementing `AsyncLock`, acquisition and release operations are invoked asynchronously, returning `CompletableFuture`s. The underlying mechanisms (Lua scripts executed via `ClusterCommandExecutor`, Pub/Sub for waiting) are similar, but the calling thread is not blocked; instead, completion is handled via the future. This is the primary mode of operation.

## 7. Adherence to Destilink Framework Guidelines

This architecture is designed to comply with **all** Destilink Framework guidelines specified in `/.amazonq/rules/guidelines.md`, focusing on:

*   **No `@ComponentScan`** in `RedisLockAutoConfiguration`.
*   **Explicit Dependencies** via constructor injection.
*   **Standard Configuration Patterns** (`@ConfigurationProperties`, `@AutoConfiguration`).
*   **Strict Dependency Management** as outlined in the guidelines.

## 8. Logging and Observability

*   **SLF4J API** for all logging.
*   **MDC (Mapped Diagnostic Context)** via `LockContextDecorator` for contextual information.
*   **Structured Exception Logging** via `ExceptionMarkerProvider` and `ExceptionLogstashMarkersJsonProvider`.
*   **Metrics Exposure** via Micrometer, facilitated by the `LockMonitor` bean.

(Further details in the dedicated `implementation_details.md` or a logging-specific section if needed).