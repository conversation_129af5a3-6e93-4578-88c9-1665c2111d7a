# Redis Locking Module: Final Exception Handling Strategy

## 1. Introduction

Effective error handling is crucial for a robust distributed locking mechanism. The `locking-redis-lock` module employs a structured exception hierarchy to provide clear, contextual information when errors occur. This document outlines this hierarchy, emphasizing integration with the Destilink Framework's structured logging capabilities via the `ExceptionMarkerProvider` interface. This consolidated strategy draws from the strengths of previous planning phases and explicitly accounts for the "Async-First" design and interaction with `redis-core`.

The `RedisLockErrorHandler` component is central to this strategy, responsible for translating low-level Redis exceptions (often originating from `redis-core`'s `ClusterCommandExecutor`) into meaningful, module-specific exceptions.

## 2. Base Exception: `AbstractRedisLockException`

All custom exceptions thrown by the Redis locking module **must** extend `AbstractRedisLockException`.

*   **Purpose**:
    *   Provides a common type for catching all lock-related errors.
    *   Carries common contextual information.
    *   Integrates with Destilink Framework's structured logging via `com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`.
*   **Recommendation**: `AbstractRedisLockException` should extend `com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException` (or `MarkerNestedException` if a checked base is preferred) to inherit base marker provider capabilities, or implement `ExceptionMarkerProvider` directly. This aligns with Destilink Framework guidelines.
*   **Required Contextual Fields (Enforced by Constructors)**:
    *   `lockName` (String): The full Redis key of the lock involved.
    *   `lockType` (String): Specific type of lock (e.g., "RedisReentrantLock").
    *   `lockOwnerId` (String, nullable): ID of the owner attempting the operation.
    *   `requestUuid` (String, nullable): Unique ID for the lock operation attempt, generated centrally by `RedisLockOperationsImpl` for idempotency tracking.
    *   `message` (String): Descriptive error message.
    *   `cause` (Throwable, nullable): Underlying exception.
*   **`ExceptionMarkerProvider` Implementation**:
    The `getMarker()` method in `AbstractRedisLockException` (or its `MarkerNestedRuntimeException` parent) must populate an SLF4J `Marker` with these common fields and allow subclasses to add specific details.

    ```java
    // Simplified example within AbstractRedisLockException or its base
    @Override
    public Marker getMarker() {
        Map<String, Object> contextMap = new HashMap<>();
        contextMap.put("lock.name", this.lockName);
        contextMap.put("lock.type", this.lockType);
        if (this.lockOwnerId != null) contextMap.put("lock.ownerId", this.lockOwnerId);
        if (this.requestUuid != null) contextMap.put("lock.requestUuid", this.requestUuid);
        
        populateSpecificMarkers(contextMap); // For subclasses
        
        contextMap.values().removeIf(Objects::isNull); // Ensure no nulls for Markers.appendEntries
        return Markers.appendEntries(contextMap);
    }

    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        // Base implementation does nothing; overridden by subclasses.
    }
    ```

## 3. Specialized Exception Classes

These extend `AbstractRedisLockException` and add specific context via `populateSpecificMarkers`. The nature of the asynchronous operations means that exceptions often propagate through `CompletableFuture` chains.

*   **`LockAcquisitionException`**:
    *   **Purpose**: Failure during lock acquisition (not timeout/interruption). May wrap exceptions from `ClusterCommandExecutor` or Lua script errors.
    *   **Specific Context**: `redis.error` (String, e.g., Lua script error).
*   **`LockTimeoutException`**:
    *   **Purpose**: Asynchronous or blocking acquisition failed within specified timeout.
    *   **Specific Context**: `lock.timeoutMillis` (long), `lock.attempts` (int).
*   **`LockReleaseException`**:
    *   **Purpose**: Failure during lock release. May wrap exceptions from `ClusterCommandExecutor`.
    *   **Specific Context**: `lock.attemptedOwnerId` (String), `lock.actualOwnerId` (String, if known and different).
*   **`LeaseExtensionException` / `LockExtensionException`**:
    *   **Purpose**: Watchdog or reentrant acquisition failed to extend lease. May wrap exceptions from `ClusterCommandExecutor`.
    *   **Specific Context**: `lock.leaseTimeMillis` (long, target lease), `lock.extensionResult` (String/Object from Redis).
*   **`LockInterruptedException`**:
    *   **Purpose**: Wraps `InterruptedException` during synchronous lock wait (when the `CompletableFuture` is blocked on).
    *   **Specific Context**: None beyond base; `InterruptedException` is the `cause`.
*   **`LockCommandException`**:
    *   **Purpose**: Lua script or Redis command execution failures originating from `ClusterCommandExecutor` (not connection issues).
    *   **Specific Context**: `redis.scriptName` or `redis.commandName` (String), `redis.arguments` (String).
*   **`LockConnectionException`**:
    *   **Purpose**: Underlying Redis connection problems detected by `redis-core` or `ClusterCommandExecutor`.
    *   **Specific Context**: `redis.connection.error` (String, connection failure details).
*   **`LockNotOwnedException` / `LockIllegalMonitorStateException`**:
    *   **Purpose**: Attempt to operate on a lock not held by the caller, or in an invalid state for the operation (e.g., unlock by non-owner).
    *   **Specific Context**: `lock.expectedOwnerId` (String), `lock.actualOwnerId` (String, if discoverable).
*   **`LockNotFoundException`**:
    *   **Purpose**: Lock key not found when an operation expected it (e.g., extending a non-existent lock).
*   **`StateLock` Specific Exceptions**:
    *   E.g., `StateMismatchException`, `StateNotFoundException`, `StateUpdateException`.
    *   **Specific Context**: `lock.state.key` (String), `lock.state.expected` (String), `lock.state.actual` (String).
*   **`LockConfigurationException`**:
    *   **Purpose**: Errors in locking module or bucket configuration, potentially related to `RedisCoreProperties` usage.
*   **`LockSerializationException` / `LockDeserializationException`**:
    *   **Purpose**: Errors during serialization/deserialization of lock data.
*   **`LockInternalException`**:
    *   **Purpose**: Unexpected internal errors not covered by other specific exceptions.

### 3.1 Idempotency-Related Exceptions

With the centralized idempotency mechanism, several new exception types handle idempotency-related failures:

*   **`IdempotencyViolationException`**:
    *   **Purpose**: Thrown when the centralized idempotency system detects an inconsistent state or violation of idempotency guarantees.
    *   **Specific Context**: `lock.requestUuid` (String), `lock.idempotency.cacheKey` (String), `lock.idempotency.violation.type` (String).
    *   **Common Scenarios**: Response cache corruption, conflicting cached results, or unexpected cache state.

*   **`IdempotencyTimeoutException`**:
    *   **Purpose**: Thrown when operations related to the idempotency cache (reading/writing response cache) timeout.
    *   **Specific Context**: `lock.requestUuid` (String), `lock.idempotency.operation` (String, e.g., "cache_read", "cache_write"), `lock.idempotency.timeoutMillis` (long).
    *   **Recovery**: These exceptions are typically retryable as they indicate transient Redis connectivity issues.

*   **`DuplicateRequestException`**:
    *   **Purpose**: Thrown when a duplicate `requestUuid` is detected in scenarios where it should not occur (e.g., concurrent requests with the same UUID).
    *   **Specific Context**: `lock.requestUuid` (String), `lock.duplicate.source` (String), `lock.duplicate.timestamp` (long).
    *   **Note**: This is primarily a defensive exception for detecting programming errors or UUID generation issues.

*   **`ResponseCacheException`**:
    *   **Purpose**: General exception for failures in the response cache system that don't fit other specific categories.
    *   **Specific Context**: `lock.requestUuid` (String), `lock.cache.operation` (String), `lock.cache.key` (String), `redis.error` (String).
    *   **Common Scenarios**: Cache serialization/deserialization failures, unexpected cache data format.

**Idempotency Exception Handling Patterns:**

1. **Automatic Retry**: `IdempotencyTimeoutException` and some `ResponseCacheException` types are automatically retried by `RedisLockOperationsImpl` using the same `requestUuid`.

2. **Client Notification**: `IdempotencyViolationException` and `DuplicateRequestException` are typically propagated to the client as they indicate either system inconsistency or client-side issues.

3. **Logging Enhancement**: All idempotency exceptions include the `requestUuid` in their context, enabling correlation of related log entries across retry attempts.

4. **Monitoring Integration**: Idempotency exceptions are tagged with specific markers for monitoring and alerting on system health.

## 4. Error Handling by `RedisLockErrorHandler`

*   The `RedisLockErrorHandler` bean is responsible for:
    *   Catching low-level Redis exceptions (e.g., from `io.lettuce.core` or `spring-data-redis`), particularly those originating from `redis-core`'s `ClusterCommandExecutor`.
    *   Translating them into the appropriate, context-rich `AbstractRedisLockException` subtype, ensuring all necessary contextual information is populated for effective structured logging via the `ExceptionMarkerProvider` mechanism.
    *   Handling `CompletionException` or `ExecutionException` when unwrapping results from `CompletableFuture`s, extracting the true cause.
    *   **Centralized Exception Context**: Automatically enriching all exceptions with the `requestUuid` generated by `RedisLockOperationsImpl`, enabling correlation of exceptions across retry attempts and providing complete traceability for debugging and monitoring.
    *   **Idempotency-Aware Error Handling**: Recognizing and properly categorizing idempotency-related failures, ensuring appropriate retry behavior and client notification based on the nature of the idempotency violation.

## 5. Logging Integration

*   The `ExceptionMarkerProvider` implementation in `AbstractRedisLockException` (and its potential parent `MarkerNestedRuntimeException`) ensures that all contextual data from lock exceptions is automatically included as structured fields in JSON log output by the Destilink Core logging framework (specifically by `ExceptionLogstashMarkersJsonProvider`). This strictly adheres to the logging guidelines.
*   General module logging uses SLF4J with parameterized messages, as per framework guidelines.
*   The `LockContextDecorator` enriches logs with MDC data (`lock.name`, `lock.operation`, etc.) for operational context.

This structured approach to exception handling and logging significantly improves diagnosability and monitoring of the distributed locking module, fully integrating with the Async-First design and `redis-core` dependency.