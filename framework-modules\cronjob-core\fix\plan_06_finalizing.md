# Plan: Redis Lock - Step 6: Watchdog, Exceptions, and Final Touches

This final step ensures the remaining components are aligned with the documentation, providing a complete and robust implementation.

### Step 6.1: Refactor `LockWatchdog.java`

The watchdog needs to be a scheduled bean that interacts asynchronously with Redis.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockWatchdog.java`

**Action:**
Implement the watchdog as a scheduled service. It should maintain a map of monitored locks and periodically call `redisLockOperations.extendLock(...)`.

```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;

@RequiredArgsConstructor
public class LockWatchdog {
    private final RedisLockOperations redisLockOperations;
    private final RedisLockErrorHand<PERSON> errorHandler;
    private final LockMonitor lockMonitor;
    private final RedisLockProperties properties;

    // Map to store monitored locks...
    
    @Scheduled(fixedDelayString = "${destilink.fw.locking.redis.watchdog.schedule-fixed-delay}")
    public void extendLeases() {
        // Iterate through monitored locks
        // For each lock, call redisLockOperations.extendLock(...)
        // Handle success/failure, update monitor, and remove from map if failed
    }

    public void registerLock(...) { ... }
    public void unregisterLock(...) { ... }
    public void shutdown() { ... }
}
```

### Step 6.2: Align Exception Classes

Ensure all custom exceptions provide contextual markers for structured logging.

**File(s) to Modify:** All classes in `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/exception/`

**Action:**
Review each exception class.
1.  Ensure it extends `AbstractRedisLockException`.
2.  Ensure its constructor correctly passes all required context (`lockName`, `lockType`, etc.) to the super constructor.
3.  Implement the `populateSpecificMarkers` method to add exception-specific context to the log marker.

**Example for `LockTimeoutException.java`:**
```java
public class LockTimeoutException extends AbstractRedisLockException {
    // ... fields: timeoutMillis, attempts
    
    // ... constructor ...

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.timeoutMillis", this.timeoutMillis);
        contextMap.put("lock.attempts", this.attempts);
    }
}
```

### Step 6.3: Final Code Review for Guideline Adherence

Perform a final pass over the entire `locking-redis-lock` module to ensure:
1.  **No `@ComponentScan`**: The module relies solely on `RedisLockAutoConfiguration`.
2.  **Constructor Injection**: All dependencies are injected via constructors.
3.  **`redis-core` Usage**: All Redis interactions are through `ClusterCommandExecutor`. All key construction uses `RedisKeyPrefix` and `RedisKey`.
4.  **Asynchronous by Default**: Core logic uses `CompletableFuture`, and synchronous methods are simple wrappers.
5.  **Logging**: SLF4J with parameterized messages is used. MDC context is set and cleared correctly.

This completes the plan to refactor the module. Executing these steps will result in a clean, robust, and maintainable implementation that is fully aligned with the provided documentation and framework guidelines.
