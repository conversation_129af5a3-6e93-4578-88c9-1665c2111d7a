package com.tui.destilink.framework.aws.rds.aurora.postgresql.config;

import com.tui.destilink.framework.core.properties.AbstractFwPostProcessor;
import com.tui.destilink.framework.core.properties.FwCompositePropertySource;
import com.tui.destilink.framework.core.util.ConfigurationPropertyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.util.StringUtils;
import software.amazon.jdbc.Driver;

import java.util.*;

@Slf4j
@Order(AuroraPostgresqlPostProcessor.ORDER)
public class AuroraPostgresqlPostProcessor extends AbstractFwPostProcessor {

    /**
     * Must be executed at the end
     */
    public static final int ORDER = Ordered.LOWEST_PRECEDENCE;

    private static final String JDBC_WRAPPER_DRIVER = Driver.class.getName();

    private static final String URL_POSTGRES_PREFIX = "jdbc:postgresql:";

    private static final String URL_WRAPPER_PREFIX = "jdbc:aws-wrapper:postgresql:";

    private static final String DEFAULT_PROPERTIES_SOURCE_NAME = "aws-rds-aurora-postgresql-default-injected";

    private static final String OVERRIDE_PROPERTIES_SOURCE_NAME = "aws-rds-aurora-postgresql-url-driver-override";

    private static final String DRIVER_CLASS_NAME_PROP_KEY = ".driver-class-name";

    private static final String AURORA_RO_ENDPOINT_PATTERN = ".cluster-ro-";

    private static final String PROXY_RO_ENDPOINT_PATTERN = "-read-only.endpoint.proxy-";

    @Override
    protected void doPostProcessEnvironment(ConfigurableEnvironment env, SpringApplication application) {
        AuroraPostgresqlConfig configProps = ConfigurationPropertyUtils
                .loadAndValidate(env, AuroraPostgresqlConfig.PROPERTIES_PREFIX, AuroraPostgresqlConfig.class);

        Set<String> prefixes = findDatasourceConfigPrefixes(env);
        FwCompositePropertySource fwPropertySource = getFwCompositePropertySource(env);

        // Build default properties
        Properties defaultProperties = new Properties();
        prefixes.forEach(p -> configureHikariDataSource(defaultProperties, configProps, p));
        PropertiesPropertySource defaultPropertiesPropertySource
                = new PropertiesPropertySource(buildFwDefaultPropSourceName(DEFAULT_PROPERTIES_SOURCE_NAME), defaultProperties);
        fwPropertySource.addFirstDefaultPropertySource(defaultPropertiesPropertySource);

        // Build url and driver override properties
        Properties overrideProperties = new Properties();
        prefixes.forEach(p -> overrideUrlProperties(env, overrideProperties, p));
        prefixes.forEach(p -> overrideUsernameIamAuth(env, configProps, overrideProperties, p));
        prefixes.forEach(p -> overrideDriverProperties(env, overrideProperties, p));
        prefixes.forEach(p -> overrideMaxLifetimeProperty(env, configProps, overrideProperties, p));
        PropertiesPropertySource overridePropertySource
                = new PropertiesPropertySource(buildFwOverridePropSourceName(OVERRIDE_PROPERTIES_SOURCE_NAME), overrideProperties);
        env.getPropertySources().addFirst(overridePropertySource);
    }

    private Set<String> findDatasourceConfigPrefixes(ConfigurableEnvironment env) {
        Set<String> prefixes = new HashSet<>();
        env.getPropertySources().stream()
                .filter(EnumerablePropertySource.class::isInstance)
                .map(EnumerablePropertySource.class::cast)
                .flatMap(ps -> Arrays.stream(ps.getPropertyNames()))
                .filter(name -> name.endsWith(DRIVER_CLASS_NAME_PROP_KEY) || name.endsWith(".url"))
                .forEach(name -> {
                    // Either driver must be configured or url with wrapper prefix
                    if (Objects.equals(env.getProperty(name), JDBC_WRAPPER_DRIVER)) {
                        prefixes.add(name.replace(DRIVER_CLASS_NAME_PROP_KEY, ""));
                    } else if (StringUtils.startsWithIgnoreCase(env.getProperty(name), URL_WRAPPER_PREFIX)) {
                        prefixes.add(name.replace(".url", ""));
                    }
                });
        return prefixes;
    }

    private void overrideDriverProperties(ConfigurableEnvironment env, Properties properties, String prefix) {
        String driverKey = prefix + DRIVER_CLASS_NAME_PROP_KEY;
        String driver = env.getProperty(driverKey);
        if (driver != null && driver.equals(JDBC_WRAPPER_DRIVER)) {
            return;
        }
        log.debug("Replacing datasource driver {}: {} with {}", driverKey, driver, JDBC_WRAPPER_DRIVER);
        properties.put(driverKey, JDBC_WRAPPER_DRIVER);
    }

    private void overrideUsernameIamAuth(ConfigurableEnvironment env, AuroraPostgresqlConfig configProps, Properties properties, String prefix) {
        if (configProps.getIamAuth().getEnabled() && StringUtils.hasText(configProps.getIamAuth().getUsernamePrefix())) {
            String usernameKey = prefix + ".username";
            String username = env.getProperty(usernameKey);
            if (StringUtils.hasText(username) && !username.startsWith(configProps.getIamAuth().getUsernamePrefix())) {
                properties.put(usernameKey, configProps.getIamAuth().getUsernamePrefix() + username);
            }
        }
    }

    private void overrideUrlProperties(ConfigurableEnvironment env, Properties properties, String prefix) {
        String urlKey = prefix + ".url";
        String url = env.getProperty(urlKey);
        if (url == null || url.startsWith(URL_WRAPPER_PREFIX)) {
            return;
        } else if (!url.startsWith(URL_POSTGRES_PREFIX)) {
            throw new IllegalArgumentException("JDBC url " + url + " is not a valid jdbc url. Only postgres is supported at the moment");
        }
        String[] split = url.split("/{2}");
        split[0] = URL_WRAPPER_PREFIX;
        log.debug("Replacing datasource url {}: {} with {}", urlKey, url, String.join("//", split));
        properties.put(urlKey, String.join("//", split));
    }

    private void overrideMaxLifetimeProperty(ConfigurableEnvironment env, AuroraPostgresqlConfig configProps, Properties properties, String prefix) {
        String urlKey = prefix + ".url";
        String maxLifetimeKey = buildHikariPropKey(prefix, "max-lifetime");
        String url = env.getProperty(urlKey);
        if (url == null || (!url.contains(AURORA_RO_ENDPOINT_PATTERN) && !url.contains(PROXY_RO_ENDPOINT_PATTERN))) {
            return;
        }
        Long maxLifetime = env.getProperty(maxLifetimeKey, Long.class);
        if (maxLifetime == null) {
            properties.put(maxLifetimeKey, configProps.getReadOnlyHikariMaxLifetimeMs());
        }
    }

    private void configureHikariDataSource(Properties properties, AuroraPostgresqlConfig configProps, String prefix) {
        properties.put(buildHikariPropKey(prefix, "exception-override-class-name"), configProps.getExceptionOverrideClassName());
        configProps.getDefaultProperties().forEach((k, v) -> {
            if (k.equals("wrapperPlugins") && configProps.getIamAuth().getEnabled() && !v.toString().contains("iam")) {
                properties.put(buildHikariPropKey(prefix, "data-source-properties." + k), "iam," + v);
            } else {
                properties.put(buildHikariPropKey(prefix, "data-source-properties." + k), v);
            }
        });
    }

    private String buildHikariPropKey(String prefix, String key) {
        return prefix + ".hikari." + key;
    }


}
