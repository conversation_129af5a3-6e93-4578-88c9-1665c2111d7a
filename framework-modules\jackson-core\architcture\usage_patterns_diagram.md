# Usage Patterns and Best Practices

This diagram illustrates common usage patterns and best practices for the Redis locking module, focusing on different lock types, error handling, and resource management.

```mermaid
classDiagram
    class ReentrantLockPattern {
        +acquireAndRelease() void
        +tryWithResources() void
        +asyncPattern() CompletableFuture
        +withTimeout() boolean
    }

    class ReadWriteLockPattern {
        +readOperation() void
        +writeOperation() void
        +upgradeReadToWrite() void
        +multipleReaders() void
    }

    class StampedLockPattern {
        +optimisticRead() void
        +conditionalWrite() void
        +readThenWriteUpgrade() void
        +validateStamp() boolean
    }

    class StateLockPattern {
        +transitionState() void
        +checkCurrentState() String
        +conditionalTransition() boolean
        +validateStateTransition() void
    }

    class ErrorHandlingPattern {
        +tryLockWithRetry() void
        +handleLockAcquisitionFailure() void
        +handleUnlockFailure() void
        +handleStampValidationFailure() void
    }

    class ResourceManagementPattern {
        +autoCloseableLock() void
        +tryWithResourcesPattern() void
        +asyncResourceManagement() CompletableFuture
        +cleanupOnFailure() void
    }

    class PerformancePattern {
        +optimizeReentrantLocking() void
        +minimizeNetworkRoundTrips() void
        +useNonBlockingOperations() void
        +optimizeLeaseTime() void
    }

    class DistributedCoordinationPattern {
        +leaderElection() void
        +workDistribution() void
        +mutualExclusion() void
        +barrierSynchronization() void
    }

    class LockingAntiPatterns {
        +infiniteWaitWithoutTimeout() void
        +nestedLocksRiskingDeadlock() void
        +forgettingToReleaseLock() void
        +ignoringLockAcquisitionFailures() void
    }

    %% Relationships
    ReentrantLockPattern --> ResourceManagementPattern : uses
    ReadWriteLockPattern --> ResourceManagementPattern : uses
    StampedLockPattern --> ResourceManagementPattern : uses
    StateLockPattern --> ResourceManagementPattern : uses
    ReentrantLockPattern --> ErrorHandlingPattern : uses
    ReadWriteLockPattern --> ErrorHandlingPattern : uses
    StampedLockPattern --> ErrorHandlingPattern : uses
    StateLockPattern --> ErrorHandlingPattern : uses
    PerformancePattern --> ReentrantLockPattern : optimizes
    PerformancePattern --> ReadWriteLockPattern : optimizes
    PerformancePattern --> StampedLockPattern : optimizes
    DistributedCoordinationPattern --> ReentrantLockPattern : uses
    DistributedCoordinationPattern --> StateLockPattern : uses
    LockingAntiPatterns --> ErrorHandlingPattern : avoids with

    %% Notes
    class ReentrantLockPattern {
        <<Pattern>>
        Basic locking patterns
    }
    class ReadWriteLockPattern {
        <<Pattern>>
        Concurrent read access
    }
    class StampedLockPattern {
        <<Pattern>>
        Optimistic concurrency
    }
    class StateLockPattern {
        <<Pattern>>
        State machine transitions
    }
    class ErrorHandlingPattern {
        <<Pattern>>
        Robust error handling
    }
    class ResourceManagementPattern {
        <<Pattern>>
        Proper resource cleanup
    }
    class PerformancePattern {
        <<Pattern>>
        Performance optimization
    }
    class DistributedCoordinationPattern {
        <<Pattern>>
        Distributed systems patterns
    }
    class LockingAntiPatterns {
        <<Anti-Pattern>>
        Practices to avoid
    }
```

## Key Usage Patterns

### 1. Reentrant Lock Patterns

```java
// Basic acquire and release
RedisReentrantLock lock = lockFactory.createReentrantLock("bucket", "resourceName");
try {
    boolean acquired = lock.tryLock(Duration.ofSeconds(5));
    if (acquired) {
        // Critical section
    }
} finally {
    lock.unlock();
}

// Try-with-resources pattern
try (Closeable ignored = lock.acquireAndGetCloseable(Duration.ofSeconds(5))) {
    // Critical section - lock automatically released
}

// Async pattern
lock.tryLockAsync(Duration.ofSeconds(5))
    .thenAccept(acquired -> {
        if (acquired) {
            try {
                // Critical section
            } finally {
                lock.unlockAsync();
            }
        }
    });
```

### 2. Read-Write Lock Patterns

```java
// Read operation
RedisReadWriteLock rwLock = lockFactory.createReadWriteLock("bucket", "resourceName");
try (Closeable ignored = rwLock.readLock().acquireAndGetCloseable(Duration.ofSeconds(5))) {
    // Read-only operations - multiple readers allowed
}

// Write operation
try (Closeable ignored = rwLock.writeLock().acquireAndGetCloseable(Duration.ofSeconds(5))) {
    // Write operations - exclusive access
}

// Upgrade read to write (requires releasing read lock first)
ReadLock readLock = rwLock.readLock();
WriteLock writeLock = rwLock.writeLock();
try {
    readLock.lock();
    // Read operations
    readLock.unlock();
    writeLock.lock();
    // Write operations
} finally {
    writeLock.unlock();
}
```

### 3. Stamped Lock Patterns

```java
// Optimistic read
RedisStampedLock stampedLock = lockFactory.createStampedLock("bucket", "resourceName");
long stamp = stampedLock.tryOptimisticRead();
// Read data
if (stampedLock.validate(stamp)) {
    // Data was read without interference
} else {
    // Fallback to pessimistic read
    stamp = stampedLock.readLock();
    try {
        // Re-read data
    } finally {
        stampedLock.unlockRead(stamp);
    }
}

// Write lock with stamp
long writeStamp = stampedLock.writeLock();
try {
    // Write operations
} finally {
    stampedLock.unlockWrite(writeStamp);
}
```

### 4. State Lock Patterns

```java
// Define allowed states and transitions
Set<String> states = Set.of("CREATED", "PROCESSING", "COMPLETED", "FAILED");
RedisStateLock stateLock = lockFactory.createStateLock("bucket", "resourceId", states);

// Transition state
boolean transitioned = stateLock.tryTransition("CREATED", "PROCESSING", Duration.ofSeconds(5));
if (transitioned) {
    // Process the resource
    stateLock.transition("PROCESSING", "COMPLETED");
} else {
    // Resource is being processed by another instance
}

// Check current state
String currentState = stateLock.getState();
```

### 5. Error Handling Patterns

```java
// Retry pattern
int maxRetries = 3;
int retryCount = 0;
boolean acquired = false;

while (!acquired && retryCount < maxRetries) {
    try {
        acquired = lock.tryLock(Duration.ofSeconds(5));
        if (!acquired) {
            retryCount++;
            Thread.sleep(100 * retryCount); // Exponential backoff
        }
    } catch (LockAcquisitionException e) {
        retryCount++;
        log.warn("Lock acquisition failed, retrying: {}", e.getMessage());
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        break;
    }
}

// Handle unlock failure
try {
    lock.unlock();
} catch (Exception e) {
    log.error("Failed to release lock: {}", e.getMessage());
    // Consider emergency cleanup or alerting
}
```

### 6. Resource Management Patterns

```java
// Try-with-resources with LockContextDecorator
try (Closeable lockContext = LockContextDecorator.INSTANCE.withLockContext("bucket", "name", "owner");
     Closeable lockResource = lock.acquireAndGetCloseable(Duration.ofSeconds(5))) {
    // Critical section with MDC logging context
}

// Async resource management
CompletableFuture<Void> future = lock.tryLockAsync(Duration.ofSeconds(5))
    .thenCompose(acquired -> {
        if (!acquired) {
            return CompletableFuture.failedFuture(new LockAcquisitionException("Failed to acquire lock"));
        }
        
        return performOperation()
            .whenComplete((result, error) -> lock.unlockAsync());
    });
```

## Anti-Patterns to Avoid

1. **Infinite Wait Without Timeout**
   - Never use `lock()` without a timeout in distributed environments
   - Always specify a reasonable timeout with `tryLock(timeout)`

2. **Nested Locks Risking Deadlock**
   - Avoid acquiring multiple locks in different orders
   - If multiple locks are needed, always acquire them in the same order

3. **Forgetting to Release Locks**
   - Always use try-finally or try-with-resources
   - Consider using `acquireAndGetCloseable()` for automatic release

4. **Ignoring Lock Acquisition Failures**
   - Always check the return value of `tryLock()`
   - Implement proper retry mechanisms with backoff

5. **Excessive Lock Duration**
   - Keep critical sections as short as possible
   - Don't perform long-running operations while holding a lock

6. **Improper Error Handling**
   - Always catch and handle exceptions from lock operations
   - Log lock-related errors with appropriate context