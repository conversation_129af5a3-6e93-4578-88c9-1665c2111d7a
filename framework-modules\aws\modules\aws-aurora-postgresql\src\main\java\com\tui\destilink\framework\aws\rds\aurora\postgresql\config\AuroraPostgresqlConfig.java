package com.tui.destilink.framework.aws.rds.aurora.postgresql.config;

import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.LinkedHashMap;
import java.util.Map;

import static com.tui.destilink.framework.aws.rds.aurora.postgresql.config.AuroraPostgresqlConfig.PROPERTIES_PREFIX;

@Data
@Validated
@ConfigurationProperties(prefix = PROPERTIES_PREFIX, ignoreUnknownFields = false)
public class AuroraPostgresqlConfig {

    public static final String PROPERTIES_PREFIX = "destilink.fw.aws.rds.aurora.postgresql.hikari";

    @NotNull
    @Valid
    private IamAuth iamAuth = new IamAuth();

    @NotNull
    private Map<@NotBlank String, @NotNull Object> defaultProperties = new LinkedHashMap<>();

    @NotBlank
    private String exceptionOverrideClassName = "software.amazon.jdbc.util.HikariCPSQLException";

    @Positive
    private long readOnlyHikariMaxLifetimeMs = 300000; // 5min

    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        if (defaultProperties.containsKey(key)) {
            return (T) defaultProperties.get(key);
        }
        return null;
    }

    @Data
    public static class IamAuth {
        @NotNull
        private Boolean enabled = false;
        @Nullable
        private String usernamePrefix = "iam-";
    }
}
