package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Test configuration that provides mock implementations for Redis cluster
 * components
 * when running in non-cluster test environments.
 */
@Configuration
public class TestRedisLockConfiguration {

    /**
     * Represents a lock entry with expiration time
     */
    private static class LockEntry {
        private final String owner;
        private final long expirationTime;

        public LockEntry(String owner, long ttlMs) {
            this.owner = owner;
            this.expirationTime = System.currentTimeMillis() + ttlMs;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }

        public String getOwner() {
            return owner;
        }
    }

    /**
     * Provides a mock ClusterCommandExecutor for testing when no real cluster is
     * available.
     * This mock simulates lock state management with TTL expiration for testing
     * purposes.
     */
    @Bean
    @ConditionalOnMissingBean
    public ClusterCommandExecutor clusterCommandExecutor() {
        ClusterCommandExecutor mock = mock(ClusterCommandExecutor.class);

        // Stateful storage to track lock ownership with TTL
        Map<String, LockEntry> lockEntries = new ConcurrentHashMap<>();

        // Configure the mock to handle different script types
        when(mock.executeScript(anyString(), any(ImmutableLettuceScript.class), any(), any()))
                .thenAnswer(invocation -> {
                    String key = invocation.getArgument(0);
                    String[] args = invocation.getArgument(3);

                    // Clean up expired locks
                    lockEntries.entrySet().removeIf(entry -> entry.getValue().isExpired());

                    // Determine operation type based on arguments
                    if (args.length >= 3 && "SIMPLE_UNLOCK".equals(args[2])) {
                        // This is an unlock operation
                        lockEntries.remove(key);
                        return CompletableFuture.completedFuture(1L); // 1 = successful unlock
                    } else if (args.length >= 3 && "true".equals(args[2])) {
                        // This is a try lock operation (reentrancy enabled)
                        if (args.length >= 2) {
                            String lockValue = args[0]; // Owner ID
                            long ttlMs = Long.parseLong(args[1]); // TTL in milliseconds
                            lockEntries.put(key, new LockEntry(lockValue, ttlMs));
                        }
                        return CompletableFuture.completedFuture(null); // null = successful lock
                    } else {
                        // Default case - assume check lock or other operations
                        // For check operations, return 1 if locked and not expired, 0 if not locked or
                        // expired
                        LockEntry entry = lockEntries.get(key);
                        boolean isLocked = entry != null && !entry.isExpired();
                        return CompletableFuture.completedFuture(isLocked ? 1L : 0L);
                    }
                });

        return mock;
    }
}
