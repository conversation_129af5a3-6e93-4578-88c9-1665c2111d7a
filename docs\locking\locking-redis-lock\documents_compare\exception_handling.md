# Modernized Redis Locking Module: Exception Handling

This document details the exception handling strategy and the exception hierarchy used in the modernized Redis locking module. Robust and well-defined exception handling is critical for providing clear feedback to users of the module and for effective debugging. **The exception handling strategy and hierarchy described here are mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md` and implemented in the module, particularly in `RedisLockErrorHandler.java`.**

## Exception Handling Strategy

The module aims to catch low-level Redis-specific exceptions and translate them into a well-defined hierarchy of module-specific exceptions. This provides a cleaner API for users of the locking module, abstracting away the underlying data store details.

Key aspects of the exception handling strategy include:

*   **Exception Translation**: Redis-specific exceptions (e.g., connection errors, command execution errors, errors related to incorrect key types like attempting Hash operations on a String key) are caught and translated into meaningful exceptions from the `com.tui.destilink.framework.locking.redis.exception` package.
*   **Contextual Information**: Exceptions include contextual information relevant to the failure, such as the lock key, the attempted operation, and the underlying cause.
*   **Structured Logging**: Exceptions are designed to integrate with structured logging, providing details like error codes and parameters for easier analysis and monitoring. This is facilitated by the `ExceptionMarkerProvider` component.
*   **Error Handling Component**: A dedicated component, `RedisLockErrorHandler`, is responsible for performing this exception translation and logging.

## Exception Hierarchy

The module defines a clear hierarchy of exceptions, all extending from `AbstractRedisLockException`.

```mermaid
graph TD
    A[AbstractRedisLockException] --> B[LockAcquisitionException]
    A --> C[LockReleaseException]
    A --> D[LockExtensionException]
    A --> E[StateOperationException]
    A --> F[LockWatchdogException]
    A --> G[LockConfigurationException]
    A --> H[LockInterruptedException]
    A --> I[LockTimeoutException]
    A --> J[LockSerializationException]
    A --> K[LockDeserializationException]
    A --> L[LockInternalException]
    A --> M[LockNotFoundException]
    A --> N[LockIllegalMonitorStateException]
```

### `AbstractRedisLockException`

*   **Purpose:** The base class for all exceptions originating from the Redis locking module. Provides common fields and methods for contextual information and integration with structured logging.
*   **Key Fields:** Error code, lock key, operation type, cause.

### `LockAcquisitionException`

*   **Purpose:** Indicates an error occurred during the process of acquiring a lock.
*   **Possible Causes:** Redis connection issues, script execution errors, invalid arguments, **attempting to acquire a lock with the wrong type (e.g., reentrant acquire on a non-Hash key)**.

### `LockReleaseException`

*   **Purpose:** Indicates an error occurred during the process of releasing a lock.
*   **Possible Causes:** Redis connection issues, script execution errors, attempting to release a lock not held by the current owner, **attempting to release a lock with the wrong type (e.g., reentrant release on a non-Hash key)**.

### `LockExtensionException`

*   **Purpose:** Indicates an error occurred during the process of extending a lock's lease time.
*   **Possible Causes:** Redis connection issues, script execution errors, attempting to extend a lock not held by the current owner, **attempting to extend a lock with the wrong type**.

### `StateOperationException`

*   **Purpose:** Indicates an error occurred during a state-specific operation on a `RedisStateLock`.
*   **Possible Causes:** Invalid state value, Redis errors during state updates, conditional update failure, **errors related to accessing or updating the state key**.

### `LockWatchdogException`

*   **Purpose:** Indicates an error occurred within the lock watchdog mechanism (e.g., failure to extend a lock's lease).
*   **Possible Causes:** Redis connection issues during extension, script execution errors during extension, **errors related to accessing or updating the lock key during extension**.

### `LockConfigurationException`

*   **Purpose:** Indicates an error related to the configuration of the locking module or a specific lock bucket.
*   **Possible Causes:** Invalid property values, missing required configuration, inconsistent settings.

### `LockInterruptedException`

*   **Purpose:** Indicates that a thread waiting for a lock was interrupted.
*   **Possible Causes:** `InterruptedException` caught during a waiting operation.

### `LockTimeoutException`

*   **Purpose:** Indicates that a lock acquisition attempt timed out.
*   **Possible Causes:** The lock could not be acquired within the specified timeout period.

### `LockSerializationException`

*   **Purpose:** Indicates an error occurred while serializing lock-related data (e.g., owner ID, state value).
*   **Possible Causes:** Issues with the configured serializer.

### `LockDeserializationException`

*   **Purpose:** Indicates an error occurred while deserializing lock-related data.
*   **Possible Causes:** Issues with the configured deserializer, corrupted data in Redis.

### `LockInternalException`

*   **Purpose:** Represents an unexpected internal error within the locking module.
*   **Possible Causes:** Programming errors, unhandled exceptions, **unexpected behavior from Redis commands or Lua script execution not covered by other exceptions**.

### `LockNotFoundException`

*   **Purpose:** Indicates that a lock with the specified key was not found when an operation expected it to exist (e.g., attempting to release a non-existent lock).

### `LockIllegalMonitorStateException`

*   **Purpose:** Indicates that a lock operation was attempted in an invalid state, similar to `java.lang.IllegalMonitorStateException`. This is typically used for reentrant locks when `unlock()` is called by a thread that does not hold the lock or has not acquired it the required number of times.

## `RedisLockErrorHandler`

The `RedisLockErrorHandler` component plays a central role in implementing this exception handling strategy. It contains methods like `handleErrors`, `handleErrorsAsync`, `handleLockAcquisition`, `handleLockRelease`, `handleLockExtension`, and `handleStateOperation` that wrap calls to Redis operations and translate any caught exceptions into the appropriate `AbstractRedisLockException` subclass. It also integrates with the `ExceptionMarkerProvider` to ensure exceptions are logged with rich, structured information.

Properly handling and interpreting these exceptions is crucial for building reliable applications that use the Redis locking module.