# Redis Locking Module: Final Lock Acquisition Mechanism

## 1. Introduction

This document describes the final, consolidated process by which locks are acquired in the `locking-redis-lock` module. The mechanism is designed for efficiency and reliability, primarily relying on Redis Pub/Sub for unlock notifications to minimize polling, while incorporating a fallback mechanism to handle potential missed notifications and ensuring adherence to the `java.util.concurrent.Lock` interface contract.

The core logic is implemented in `AbstractRedisLock.java` and utilizes `UnlockMessageListener.java` (managed by `UnlockMessageListenerManager.java`) and `LockSemaphoreHolder.java`. All Redis interactions for lock acquisition are handled **asynchronously** via `RedisLockOperations` which uses `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java`.

## 2. Core Principles

*   **Async-First Operations**: All lock acquisition attempts are fundamentally asynchronous, returning `CompletableFuture`s. Synchronous `lock()` and `tryLock()` methods are wrappers around these asynchronous calls.
*   **Non-Polling Preference**: Threads waiting for a lock primarily wait passively for an unlock notification via Redis Pub/Sub.
*   **Atomic Operations**: Lock acquisition attempts are performed atomically using Lua scripts, executed asynchronously via `ClusterCommandExecutor`.
*   **Semaphore/CompletableFuture-Based Waiting**: `LockSemaphoreHolder` (containing a `java.util.concurrent.Semaphore` for synchronous wrappers, and managing `CompletableFuture`s for asynchronous calls) manages threads/futures waiting for a specific lock key.
*   **TTL-Aware Fallback**: If a Pub/Sub notification is missed or delayed, waiting mechanisms also time out based on the lock\'s current TTL in Redis or a configured retry interval, prompting a re-attempt acquisition.
*   **Designed for Lightweight Lock Instances**: The acquisition mechanism, through shared components like `UnlockMessageListenerManager` and `LockSemaphoreHolder` (managing resources per lock key), is designed to efficiently support numerous, potentially short-lived, lightweight lock instances, aligning with the framework\'s resource sharing philosophy.
*   **`lock()` Method Blocks Indefinitely (Synchronous Wrapper)**: The synchronous `lock()` method will block indefinitely until the underlying asynchronous acquisition completes or the thread is interrupted. It does **not** use or respect any internal default timeout property.
*   **`tryLock(timeout)` Provides Timed Waits (Synchronous Wrapper)**: Explicit timeout behavior for synchronous calls is handled by the `tryLock(long time, TimeUnit unit)` method, which waits on the `CompletableFuture` for the specified duration.
*   **`lockAsync()` and `tryLockAsync()` as Primary API**: These `CompletableFuture`-based methods are the primary entry points for lock acquisition, designed for non-blocking, reactive applications.

## 3. Lock Acquisition Scenarios

### 3.1. Synchronous Wait Scenario (`lock.lock()`) - Wrapper Implementation

This flow applies when `lock.lock()` is called, requiring an indefinite wait until the lock is acquired or the thread is interrupted. This method acts as a blocking wrapper around the asynchronous acquisition logic.

```mermaid
flowchart TD
    A["Start: lock.lock() (Synchronous Wrapper)"] --> B["Call lockAsync()"]
    B --> C["Block on CompletableFuture.get() (Waits indefinitely or until interrupted)"]
    C --> D{"CompletableFuture Completed?"}
    D -- "Yes (Lock Acquired)" --> E["Return"]
    D -- "Exceptional Completion (e.g., LockAcquisitionException, LockTimeoutException)" --> F["Unwrap Exception and Throw"]
    D -- "InterruptedException caught during get()" --> G["Re-assert Thread.interrupted() / Throw LockInterruptedException"]

    subgraph AsynchronousLockAcquisition ["Internal Asynchronous Lock Acquisition (lockAsync())"]
        ALA1["Generate Unique Request ID (requestUuid)"]
        ALA1 --> ALA2["Loop (Until Lock Acquired or Interrupted)"]
        ALA2 --> ALA3["Call tryAcquireLockInternalAsync(requestUuid, ownerId, leaseTime, lockType) (Executes Lua script for specific lock type via RedisLockOperations - uses ClusterCommandExecutor)"]
        ALA3 --> ALA4{"Lock Acquired by Script (CompletableFuture completes successfully)?"}
        ALA4 -- "Yes" --> ALA5["Register with LockWatchdog (if applicable)"]
        ALA5 --> ALA6["Complete CompletableFuture successfully"]
        ALA4 -- "No (Script returns TTL of current holder or error)" --> ALA7["Get/Create LockSemaphoreHolder for lockKey (manages CompletableFuture for this lock)"]
        ALA7 --> ALA8["Calculate Wait Duration: min(Returned TTL from script, Configured Retry Interval)"]
        ALA8 --> ALA9["Register CompletableFuture with LockSemaphoreHolder (Future will be completed on signal/timeout)"]
        ALA9 --> ALA10["Return incomplete CompletableFuture"]
        ALA10 --> ALA2
    end
    B --> ALA1
```

**Process Description (Synchronous Wrapper `lock()`):**

1.  **Initiate Acquisition**: The synchronous `AbstractRedisLock.lock()` method is called.
2.  **Delegate to Async**: It immediately calls the asynchronous `lockAsync()` method.
3.  **Block and Wait**: The current thread then blocks indefinitely by calling `CompletableFuture.get()` on the `CompletableFuture` returned by `lockAsync()`.
4.  **Handle Completion**:
    *   **Success**: If `lockAsync()` completes successfully, the `get()` call returns, and the `lock()` method also returns.
    *   **Exceptional Completion**: If `lockAsync()` completes exceptionally, the `get()` call throws an `ExecutionException` (wrapping the original cause). The `AbstractRedisLock` unwraps this and re-throws the appropriate `AbstractRedisLockException` subtype (e.g., `LockAcquisitionException`).
    *   **Interruption**: If the calling thread is interrupted while blocking on `get()`, an `InterruptedException` is caught. The thread\'s interrupt status is re-asserted, and a `LockInterruptedException` is thrown.

### 3.2. Asynchronous Acquisition (`lockAsync()`) - Primary Flow

This flow describes the core, non-blocking asynchronous lock acquisition.

```mermaid
flowchart TD
    A["Start: lockAsync() (Primary API)"] --> B["Generate Unique Request ID (requestUuid)"]
    B --> C["Create new CompletableFuture<Void> (lockFuture)"]
    C --> D["Recursive Attempt Function (tryAcquireLoop)"]
    D --> E["Call tryAcquireLockInternalAsync(requestUuid, ownerId, leaseTime, lockType) (Executes Lua script for specific lock type via RedisLockOperations - uses ClusterCommandExecutor)"]
    E --> F{"CompletableFuture from Script Completed?"}
    F -- "Success (Lock Acquired)" --> G["Register with LockWatchdog (if leaseTime > 0 and applicable)"]
    G --> H["lockFuture.complete(null)"]
    H --> I["Return lockFuture"]
    F -- "Failure (Lock Held / Error)" --> J["Determine Next Action (Retry or Fail)"]
    J --> K{"Is Lock Held (TTL returned)?"}
    K -- "Yes" --> L["Calculate Wait Duration: min(Returned TTL from script, Configured Retry Interval)"]
    L --> M["Get/Create LockSemaphoreHolder for lockKey (Manages CompletableFuture for this lock)"]
    M --> N["Register lockFuture with LockSemaphoreHolder (Future will be completed on signal/timeout)"]
    N --> O["Return lockFuture (still incomplete, waiting)"]
    K -- "No (Error/Timeout)" --> P["lockFuture.completeExceptionally(exception)"]
    P --> I
```

**Process Description (Asynchronous `lockAsync()`):**

1.  **Initiate Acquisition**: `AbstractRedisLock.lockAsync()` is called.
2.  **Create Future**: A new `CompletableFuture<Void>` (`lockFuture`) is created and returned immediately.
3.  **Attempt Atomic Acquisition (Asynchronously)**:
    *   A unique `requestUuid` is generated.
    *   `tryAcquireLockInternalAsync()` is called, which invokes the appropriate Lua script (e.g., \"try\\_lock.lua\") via `RedisLockOperations`. This operation itself returns a `CompletableFuture`.
4.  **Handle Script Result (Chained)**:
    *   **Success**: If the script execution `CompletableFuture` completes successfully, the lock is acquired.
        *   If applicable, the lock is registered with the `LockWatchdog` (which also performs asynchronous operations).
        *   The `lockFuture` is completed successfully.
    *   **Failure (Lock Held / Error)**: If the script indicates the lock is held (e.g., returns the current holder\'s TTL) or another issue occurred.
5.  **Wait for Notification/Retry (Asynchronously)**:
    *   A `LockSemaphoreHolder` is obtained for the specific `lockKey`.
    *   Instead of blocking a thread, `lockFuture` is registered with the `LockSemaphoreHolder` to be completed when an unlock notification is received, or after a calculated timeout.
    *   The `lockFuture` remains incomplete and is returned, allowing the calling application thread to continue.
6.  **Completion by `UnlockMessageListener`**: When the `UnlockMessageListener` receives a Pub/Sub message (deriving the `lockName` from the channel and parsing the `UnlockType` from the payload), it signals the `LockSemaphoreHolder`, which then completes the associated `CompletableFuture` (e.g., by re-invoking the try-acquire logic or directly completing it).

### 3.3. Timeout Scenario (`tryLockAsync(long time, TimeUnit unit)`) - Primary Flow

This flow applies when `lock.tryLockAsync(long time, TimeUnit unit)` is called, attempting to acquire the lock within a specific duration without blocking the calling thread.

```mermaid
flowchart TD
    A[Start: tryLockAsync(timeout, unit)] --> B[Calculate Deadline (startTime + timeout)]
    B --> C[Create new CompletableFuture<Boolean> (tryLockFuture)]
    C --> D[Recursive Attempt Function (tryAcquireLoopWithTimeout)]
    D --> E[Call tryAcquireLockInternalAsync(requestUuid, ownerId, leaseTime, lockType) (Executes Lua script via RedisLockOperations - uses ClusterCommandExecutor)]
    E --> F{CompletableFuture from Script Completed?}
    F -- Success (Lock Acquired) --> G[Register with LockWatchdog (if applicable)]
    G --> H[tryLockFuture.complete(true)]
    H --> I[Return tryLockFuture]
    F -- Failure (Lock Held / Error) --> J[Check Remaining Time & Determine Next Action]
    J --> K{Is currentTime < Deadline?}
    K -- No (Deadline Reached) --> L[tryLockFuture.complete(false)]
    L --> I
    K -- Yes --> M[Calculate Wait Duration: min(Returned TTL, Retry Interval, Remaining Overall Timeout)]
    M --> N[Get/Create LockSemaphoreHolder for lockKey]
    N --> O[Register tryLockFuture with LockSemaphoreHolder for signal/timeout]
    O --> P[Return tryLockFuture (still incomplete, waiting)]
    J -- Error (not just lock held) --> Q[tryLockFuture.completeExceptionally(exception)]
    Q --> I
```

**Process Description (Asynchronous `tryLockAsync()`):**

This flow is similar to `lockAsync()`, with the addition of an overall `deadline` and completing the `CompletableFuture` with `false` if the deadline is reached.

1.  **Initiate Acquisition & Deadline**: `AbstractRedisLock.tryLockAsync(time, unit)` calculates a `deadline`.
2.  **Create Future**: A new `CompletableFuture<Boolean>` (`tryLockFuture`) is created and returned immediately.
3.  **Attempt Atomic Acquisition (Asynchronously)**: Same as `lockAsync()`.
4.  **Handle Script Result & Check Deadline**:
    *   **Success**: If the script indicates success, `tryLockFuture` is completed with `true`.
    *   **Failure**: If the script indicates the lock is held or an error:
        *   A check is made if `System.currentTimeMillis() >= deadline`. If true, `tryLockFuture` is completed with `false`.
        *   If the deadline is not yet met, `tryLockFuture` is registered with `LockSemaphoreHolder` with a wait time calculated as `min(TTL from script, retryInterval, timeRemainingUntilDeadline)`.
        *   If the calculated wait time is `<= 0` (meaning timeout is immediate or already passed), `tryLockFuture` is completed with `false`.
        *   If a non-lock-held error occurs, `tryLockFuture` is completed exceptionally.
5.  **Completion by `UnlockMessageListener`**: Similar to `lockAsync()`, the `UnlockMessageListener` signals the `LockSemaphoreHolder` which then completes `tryLockFuture`.

## 4. Key Components Involved

*   **`AbstractRedisLock`**: Orchestrates the asynchronous loops, delegates Lua script calls to `RedisLockOperations`, and manages interaction with `LockSemaphoreHolder` and `LockWatchdog`. Implements the synchronous `Lock` interface as a wrapper.
*   **Concrete Lock Implementations** (e.g., `RedisReentrantLock`): Extend `AbstractRedisLock` and implement `AsyncLock`, providing specific asynchronous Lua script calls for their semantics.
*   **`RedisLockOperations`**: **Crucial component that exclusively uses `ClusterCommandExecutor` for all Redis command and Lua script executions**, ensuring asynchronous interaction.
*   **`UnlockMessageListenerManager` / `UnlockMessageListener`**: Manages Pub/Sub subscriptions and signals `LockSemaphoreHolder` (or directly completes `CompletableFuture`s) upon receiving unlock messages. Message processing is asynchronous.
*   **`LockSemaphoreHolder`**: Manages internal `Semaphore` for synchronous wrappers and `CompletableFuture`s for asynchronous waiters.
*   **Lua Scripts**: Perform atomic lock operations on Redis, returning status or TTL. Executed asynchronously via `RedisLockOperations`.
*   **`ClusterCommandExecutor` (from `redis-core`)**: The underlying component used by `RedisLockOperations` and `LockWatchdog` for all non-blocking Redis communication.

This refined, Async-First, notification-driven approach with robust fallback and timeout handling ensures efficient and correct distributed lock acquisition, fully compliant with framework guidelines.