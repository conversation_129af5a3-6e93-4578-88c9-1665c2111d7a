package com.tui.destilink.framework.locking.redis.lock.service.impl;

import com.tui.destilink.framework.locking.redis.lock.service.LockMonitor;
import lombok.extern.slf4j.Slf4j;

/**
 * Default implementation of the LockMonitor interface.
 * <p>
 * This implementation provides basic monitoring capabilities for lock
 * operations.
 * It logs lock acquisition, release, and extension events, and can be extended
 * to integrate with metrics systems like Micrometer.
 * </p>
 */
@Slf4j
public class DefaultLockMonitor implements LockMonitor {

    @Override
    public void onLockAcquired(String lockName, String lockOwner, long acquiredAfterMillis, int retryCount) {
        log.debug("Lock '{}' acquired by '{}' after {}ms with {} retries",
                lockName, lockOwner, acquiredAfterMillis, retryCount);
    }

    @Override
    public void onLockAcquisitionFailed(String lockName, String lockOwner, long failedAfterMillis, int retryCount) {
        log.debug("Lock '{}' acquisition failed for '{}' after {}ms with {} retries",
                lockName, lockOwner, failedA<PERSON><PERSON><PERSON><PERSON>, retryCount);
    }

    @Override
    public void onLockReleased(String lockName, String lockOwner, long heldForMillis) {
        log.debug("Lock '{}' released by '{}' after being held for {}ms",
                lockName, lockOwner, heldForMillis);
    }

    @Override
    public void onLockExtended(String lockName, String lockOwner, long extendedForMillis) {
        log.debug("Lock '{}' extended by '{}' for {}ms",
                lockName, lockOwner, extendedForMillis);
    }
}