# Redis Lock Component Interactions

## Hybrid Lock Acquisition Flow
Combines Plan 1's polling reliability with Plan 2's pub/sub efficiency:

```mermaid
sequenceDiagram
    participant App as Application
    participant LM as LockManager
    participant RT as RedisTemplate
    participant Listener
    
    App->>LM: acquireLock()
    LM->>RT: tryLock() with polling
    RT-->>LM: LockResult (success/timeout)
    LM->>Listener: registerCallback() if waiting
    Listener->>RT: subscribeToChannel()
    RT-->>Listener: lockReleased event
    Listener->>LM: notifyWaiters()
    LM->>App: return lock