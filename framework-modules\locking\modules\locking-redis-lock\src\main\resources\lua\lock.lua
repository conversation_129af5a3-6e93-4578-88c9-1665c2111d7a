-- Script to acquire a lock
-- KEYS[1] = Lock key
-- ARGV[1] = Lock value (owner ID)
-- ARGV[2] = Lease time in milliseconds

local lockKey = KEYS[1]
local lockValue = ARGV[1]
local leaseTimeMillis = tonumber(ARGV[2])

-- Try to acquire the lock using SET NX PX
if redis.call("SET", lockKey, lockValue, "NX", "PX", leaseTimeMillis) then
    return 1 -- Lock acquired successfully
else
    -- Check if the current holder is this instance (re-entrancy)
    if redis.call("GET", lockKey) == lockValue then
        -- Refresh the lease time
        redis.call("PEXPIRE", lockKey, leaseTimeMillis)
        return 1 -- Lock re-acquired (re-entrant)
    else
        return 0 -- Lock not acquired
    end
end