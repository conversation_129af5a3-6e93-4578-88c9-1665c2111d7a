-- Redis Lock Release Script
-- This script releases a lock if the current holder matches the provided value
-- KEYS[1] - The lock key
-- ARGV[1] - The lock value (typically a unique identifier for the lock holder)

-- Check if the key exists and has the same value
local current = redis.call('get', KEYS[1])
if current == ARGV[1] then
    -- Lock holder matches, release the lock
    redis.call('del', KEYS[1])
    return 1
elseif current == false then
    -- Lock doesn't exist anymore
    return 0
else
    -- Lock is held by someone else
    return 0
end