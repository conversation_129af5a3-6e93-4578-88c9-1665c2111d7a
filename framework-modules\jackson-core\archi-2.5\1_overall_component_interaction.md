# Overall Component Interaction

```mermaid
classDiagram
    direction LR

    package "locking-redis-lock Module" {
        class RedisLockAutoConfiguration {
            +configureAllLockComponents()
        }
        note for RedisLockAutoConfiguration "Spring @AutoConfiguration"
        class LockComponentRegistry {
            +ScriptLoader scriptLoader
            +ObjectProvider<LockMonitor> lockMonitorProvider
            +RedisLockOperations lockOperations
            +LockWatchdog lockWatchdog
            +UnlockMessageListenerManager unlockMessageListenerManager
            +RedisLockErrorHandler errorHandler
            +LockBucket defaultLockBucket // Example, actual buckets created via registry
            +DefaultLockOwnerSupplier lockOwnerSupplier
            +... (other shared components)
        }
        note for LockComponentRegistry "Central Spring Bean injected into lock builders and lock types"
        class AbstractRedisLock {
            <<Abstract>>
            #LockComponentRegistry lockComponentRegistry // Provides access to shared services
            +tryLockAsync(...): CompletableFuture<Boolean>
            +unlockAsync(...): CompletableFuture<Void>
            #executeScriptAsync(String scriptName, List<String> keys, List<String> args): CompletableFuture<Object>
        }
        class ConcreteRedisLock {
            <<Concrete Implementation>>
            +ConcreteRedisLock(config, lockComponentRegistry, ...)\n        }
        ConcreteRedisLock --|> AbstractRedisLock
        interface RedisLockOperations {
            +executeScriptAsync(String scriptName, List<String> keys, List<String> args): CompletableFuture<Object>
            +tryLockAsyncInternal(...): CompletableFuture<LockAcquisitionResult>
            +unlockAsyncInternal(...): CompletableFuture<Void>
            +renewLeaseAsync(...): CompletableFuture<Boolean>
            +publishUnlockMessage(...): CompletableFuture<Void>
        }
        class RedisLockOperationsImpl {
            -ClusterCommandExecutor commandExecutor
            -ScriptLoader scriptLoader
            -LockBucket lockBucket // Specific to the operation context
            -RedisLockErrorHandler errorHandler
            +executeScriptAsync(String scriptName, List<String> keys, List<String> args): CompletableFuture<Object>
            +tryLockAsyncInternal(...): CompletableFuture<LockAcquisitionResult>
            +unlockAsyncInternal(...): CompletableFuture<Void>
            +renewLeaseAsync(...): CompletableFuture<Boolean>
            +publishUnlockMessage(...): CompletableFuture<Void>
        }
        RedisLockOperationsImpl ..|> RedisLockOperations
        class LockWatchdog {
            -RedisLockOperations lockOperations
            -LockOwnerSupplier lockOwnerSupplier // Resolved via LockComponentRegistry or specific config
            -ScheduledExecutorService executorService
            -Map<String, LockInfo> monitoredLocks // Internal registry of active locks
            +registerLock(String lockKey, String lockOwnerId, long effectiveLeaseTimeMillis)
            +unregisterLock(String lockKey, String lockOwnerId)
            +extendLocks() // Internally scheduled
        }
        note for LockWatchdog "Manages lease renewal for active locks. Uses internal 'monitoredLocks' map."

        class LockWatchdog.LockInfo { // Inner data structure
            String lockKey
            String lockOwnerId
            long originalLeaseTimeMillis
            long extensionIntervalMillis
            long nextExtensionTime
        }

        interface LockOwnerSupplier {
            +String getOwnerId(String lockName)
            +boolean isApplicationInstanceBound()
        }
        note for LockOwnerSupplier "Application-provided or default supplier for lock ownership information"

        class UnlockMessageListenerManager {
            -ClusterCommandExecutor commandExecutor
            -LockComponentRegistry lockComponentRegistry // To get bucket config for channel names
            +registerListener(String lockKey, String channelName, LockSemaphoreHolder semaphoreHolder): UnlockMessageListener
            +unregisterListener(UnlockMessageListener listener)
        }
        class UnlockMessageListener {
            -LockSemaphoreHolder semaphoreHolder
            -String channelName // Specific channel like <prefix>:<bucket>:__unlock_channels__:{{lockName}}
            +onMessage(channel, message)
            +subscribe()
            +unsubscribe()
        }
        note for UnlockMessageListener "Listens to Redis Pub/Sub for unlock messages on a specific channel"

        class LockSemaphoreHolder {
            -Semaphore semaphore
            +acquireOrListen(): CompletableFuture<Boolean>
            +signalUnlock()
            +getSemaphoreName(): String
        }
        note for LockSemaphoreHolder "Manages local semaphores for waiting threads, one per lock key"
        class ScriptLoader {
            -ClusterCommandExecutor commandExecutor
            -Map<String, RedisScript> loadedScripts
            -Map<String, String> scriptShas
            +@PostConstruct loadScripts(): void
            +getScriptSha(scriptName): String
            +getScriptContent(scriptName): String
            +getScript(scriptName): RedisScript<T>
        }
        note for ScriptLoader "Loads and caches Lua scripts"

        class RedisLockErrorHandler {
            +handleRedisException(Throwable t, String lockName): AbstractRedisLockException
        }
        note for RedisLockErrorHandler "Translates Redis/Lettuce exceptions"
        class LockBucket { // Configuration and key generation context
            -RedisKeyPrefix basePrefix
            -String bucketName
            -LockBucketConfig config
            +getLockKey(lockName): RedisKey
            +getUnlockChannel(lockName): String // Generates specific channel for a lock name
            +getResponseCacheKey(lockName, requestUuid): RedisKey
        }
        note for LockBucket "Manages Redis key construction and schema based on resolved configuration"

        // Removed RedisLockRegistry as a separate top-level bean.
        // RedisLock class below represents the conceptual data for a lock.
        class RedisLockData { // Conceptual data for a lock, managed by Watchdog or Lock Implementations
            +String lockKey
            +String ownerId // Renamed from clientId for clarity
            +long leaseTimeMillis
            +long acquireTimeMillis
            +int reentrancyCount // If reentrant
            +String state // If state lock
        }
        note for RedisLockData "Represents conceptual data of an active distributed lock"
    }

    package "redis-core Module (External)" {
        class ClusterCommandExecutor {
            +executeScriptAsync(sha, keys, args): CompletableFuture<Object>
            +evalScriptAsync(script, keys, args): CompletableFuture<Object>
            +publishAsync(channel, message): CompletableFuture<Long>
            +subscribeAsync(channels, listener): CompletableFuture<Void>
            +psubscribeAsync(patterns, listener): CompletableFuture<Void> // UMLM might use psubscribe for a pattern initially if listeners are dynamic
        }
        class RedisKey {
            +getKey(): String
        }
        class RedisKeyPrefix {
            +getPrefix(): String
        }
        RedisKey --|> AbstractRedisKey
        class AbstractRedisKey {}
    }

    RedisLockAutoConfiguration --> LockComponentRegistry : creates & configures beans

    // LockComponentRegistry provides dependencies to builders, which then pass them to Lock Implementations
    AbstractRedisLock o-- LockComponentRegistry : (receives LCR via constructor from builder)

    // Direct dependencies of shared services (often provided by LCR or Spring DI)
    RedisLockOperationsImpl o-- ClusterCommandExecutor : (via LCR or direct DI)
    RedisLockOperationsImpl o-- ScriptLoader : (via LCR)
    // RedisLockOperationsImpl uses a LockBucket instance relevant to its current operation, not a single default one from LCR.
    RedisLockOperationsImpl o-- RedisLockErrorHandler : (via LCR)

    LockWatchdog o-- RedisLockOperations : (via LCR)
    LockWatchdog o-- LockOwnerSupplier : (resolved via LCR/config)
    LockWatchdog +-- LockWatchdog.LockInfo

    UnlockMessageListenerManager o-- ClusterCommandExecutor : (via LCR or direct DI)
    UnlockMessageListenerManager o-- LockComponentRegistry : (to get bucket config for channels)
    UnlockMessageListenerManager "1" *-- "0..*\" UnlockMessageListener : manages

    UnlockMessageListener o-- LockSemaphoreHolder : signals

    ScriptLoader o-- ClusterCommandExecutor : (for SCRIPT LOAD, via LCR or direct DI)
    // LockBucket instances are created and configured, not a single global one.
    // LCR might hold a factory or prototype bean for LockBucket if needed.
```
