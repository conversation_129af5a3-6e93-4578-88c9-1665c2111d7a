# Destilink Framework: Redis Locking Module - Final Architecture & Plan

This document outlines the final consolidated architecture and plan for the `locking-redis-lock` module, derived from the analysis and synthesis of previous planning phases and updated to reflect the **Async-First design**, **mandatory `redis-core` integration**, and **strict adherence to Destilink Framework guidelines**.

## Core Documentation

*   [Architecture Overview](architecture_overview.md) - *Updated to reflect Async-First and `redis-core` usage.*
*   [Configuration](configuration.md) - *Updated to detail `redis-core` property integration.*
*   [Redis Key Schema](redis_key_schema.md) - *Updated for `redis-core` prefix usage.*
*   [<PERSON><PERSON>ripts](lua_scripts_2.md) - *Consolidated and updated to reflect Redisson-inspired logic for ReadWriteLocks, emphasizing `ClusterCommandExecutor` usage.*
*   [Lock Acquisition Mechanism](lock_acquisition.md) - *Updated to highlight Async-First flow and `CompletableFuture` handling.*
*   [Unlock Messaging](messaging.md) - *Updated to emphasize asynchronous message processing and `ClusterCommandExecutor` usage.*
*   [Watchdog Mechanism](watchdog.md) - *Updated to clarify asynchronous operations via `ClusterCommandExecutor`.*
*   [Exception Handling](exception_handling.md) - *Updated to detail exception propagation in async flows and `redis-core` related exceptions.*
*   [Implementation Details](implementation_details.md) - *Updated to extensively cover Async-First, `redis-core` integration, and guideline adherence.*

## Supporting Documentation

*   [Performance Considerations](performance_considerations.md) - *To be reviewed for Async-First impact.*
*   [Metrics](metrics.md) - *To be reviewed for Async-First impact.*
*   [Testing Strategy](testing_strategy.md) - *To be updated with explicit `test-support` module requirements and async testing.*
*   [Modernization Plan & Assessment](modernization.md) - *To be reviewed for comprehensive reflection of Async-First and `redis-core` adoption.*
*   [Migration Notes](migration_notes.md) - *To be reviewed for implications of Async-First and `redis-core` changes for users.*
*   [Glossary](glossary.md) - *Updated with new terms and clarifications related to Async-First and `redis-core`.*