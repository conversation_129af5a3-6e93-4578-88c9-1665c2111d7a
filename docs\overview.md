# Destilink Framework Overview

Welcome to the Destilink Framework documentation. This overview will help you navigate the various components and modules of the framework.

## Project Structure

The Destilink Framework is organized into several key areas:

1. **Core Modules**: Located in `framework-modules/`
2. **Test Applications**: Found in `framework-test-applications/`
3. **Documentation**: You are here! (`docs/`)

## Key Documentation

### Guidelines
- [General Guidelines](guidelines.md)
- [Collision Handling](collisions.md)

### Locking Mechanisms
- [Async Lock Usage Examples](locking/async-lock-usage-examples.md)
- [Error Handling Differences](locking/error-handling-differences.md)
- [Migration Guidance](locking/migration-guidance.md)
- [Performance Considerations](locking/performance-considerations.md)
- [Testing Recommendations](locking/testing-recommendations.md)

#### Redis Locking
- [Redis Lock Overview](locking/locking-redis-lock/index.md)
- [Redis Read-Write Lock](locking/locking-redis-lock/redis-read-write-lock-diagram.md)
- [Redis Reentrant Lock](locking/locking-redis-lock/redis-reentrant-lock-diagram.md)
- [Redis Stamped Lock](locking/locking-redis-lock/redis-stamped-lock-diagram.md)
- [Redis State Lock](locking/locking-redis-lock/redis-state-lock-diagram.md)

### Release Notes
- [Version 1.0.23](releases/v1_0_23.md)
- [Version 1.0.24](releases/v1_0_24.md)

### Test Support
- [Keycloak Test Support](test-support/keycloak-test-support.md)
- [LocalStack Test Support](test-support/localstack-test-support.md)
- [Redis Test Support](test-support/redis-test-support.md)
- [SFTP Test Support](test-support/sftp-test-support.md)
- [Test Core](test-support/test-core.md)

### Web Components
- [Web Client](web/web-client.md)
- [Web Core](web/web-core.md)
- [OpenAPI Integration](web/web-openapi.md)
- [OAuth2 Server Security](web/web-security-oauth2-server.md)
- [Web Server](web/web-server.md)

## Getting Started

To get started with the Destilink Framework, we recommend the following steps:

1. Review the [General Guidelines](guidelines.md) for an overview of best practices.
2. Familiarize yourself with the core modules in `framework-modules/`.
3. Check out the test applications in `framework-test-applications/` for examples of framework usage.
4. Refer to specific documentation as needed for detailed information on components.

## Contributing

If you'd like to contribute to the Destilink Framework or its documentation, please follow our contribution guidelines (link to be added).

## Need Help?

If you encounter any issues or have questions, please refer to our troubleshooting guide (link to be added) or reach out to the development team.

Happy coding with Destilink Framework!