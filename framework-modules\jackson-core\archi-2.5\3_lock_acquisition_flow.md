# Lock Acquisition Flow

```mermaid
sequenceDiagram
    participant ClientApp
    participant RedisLockService
    participant LockComponentRegistry
    participant RedisLockRegistry
    participant RedisLockOperationsImpl
    participant ScriptLoader
    participant RedisConnection

    ClientApp->>+RedisLockService: acquire(lockName, timeout, leaseTime)
    RedisLockService->>+LockComponentRegistry: getLockBuilder()
    LockComponentRegistry->>+RedisLockRegistry: createLock(lockName, leaseTime)
    RedisLockRegistry-->>-LockComponentRegistry: RedisLock instance
    LockComponentRegistry-->>-RedisLockService: RedisLock instance
    RedisLockService->>+RedisLockOperationsImpl: executeAcquireScript(lockKey, clientId, leaseTime)
    RedisLockOperationsImpl->>+ScriptLoader: getScript("acquire.lua")
    ScriptLoader->>+RedisConnection: EVALSHA/EVAL (acquire.lua)
    RedisConnection-->>-ScriptLoader: Script Result (OK or nil)
    alt Lock Acquired
        ScriptLoader-->>-RedisLockOperationsImpl: Lock Acquired (true)
        RedisLockOperationsImpl-->>-RedisLockService: Lock Acquired (true)
        RedisLockService->>RedisLockRegistry: registerLock(RedisLock)
        opt Watchdog Enabled
            RedisLockService->>LockWatchdog: scheduleHeartbeat(RedisLock)
        end
        RedisLockService-->>-ClientApp: LockInfo (success)
    else Lock Not Acquired (e.g., timeout or contention)
        ScriptLoader-->>-RedisLockOperationsImpl: Lock Not Acquired (false)
        RedisLockOperationsImpl-->>-RedisLockService: Lock Not Acquired (false)
        opt FailFast is false and PubSub enabled
            RedisLockService->>UnlockMessageListener: subscribeToUnlock(lockName)
            Note over RedisLockService, UnlockMessageListener: Waits for unlock message or timeout
            alt Unlock Message Received
                 UnlockMessageListener->>RedisLockService: notifyUnlock(lockName)
                 RedisLockService->>RedisLockOperationsImpl: executeAcquireScript(...)
                 RedisLockOperationsImpl->>+ScriptLoader: getScript("acquire.lua")
                 ScriptLoader->>+RedisConnection: EVALSHA/EVAL (acquire.lua)
                 RedisConnection-->>-ScriptLoader: Script Result
                 ScriptLoader-->>-RedisLockOperationsImpl: Script Result
                 RedisLockOperationsImpl-->>-RedisLockService: Lock Acquired (true/false)
                 Note over RedisLockService, RedisLockOperationsImpl: Retry acquisition
                 loop Until Acquired or Timeout
                    RedisLockOperationsImpl->>+ScriptLoader: getScript("acquire.lua")
                    ScriptLoader->>+RedisConnection: EVALSHA/EVAL (acquire.lua)
                    RedisConnection-->>ScriptLoader: Script Result
                    ScriptLoader-->>RedisLockOperationsImpl: Script Result
                    RedisLockOperationsImpl-->>RedisLockService: Lock Acquired (true/false)
                    alt Lock Acquired
                        RedisLockService->>RedisLockRegistry: registerLock(RedisLock)
                        opt Watchdog Enabled
                           RedisLockService->>LockWatchdog: scheduleHeartbeat(RedisLock)
                        end
                        RedisLockService-->>ClientApp: LockInfo (success)
                    end
                 end
            else Timeout Waiting for Unlock
                RedisLockService-->>-ClientApp: LockInfo (failure - timeout)
            end
        else FailFast is true or PubSub disabled
            RedisLockService-->>-ClientApp: LockInfo (failure - timeout/contention)
        end
    end
```
