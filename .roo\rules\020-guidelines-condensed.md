# Destilink Framework - Condensed Guidelines (for Daily Development)

**IMPORTANT:** This is a condensed version. For complete details, edge cases, explanations, and advanced topics, **ALWAYS refer to the full `.roo\destilink-framework-guidelines\020-guidelines-full.md` document.**

This version prioritizes information crucial for day-to-day feature development.

## Core Principles to Remember

1. **NO Component Scan:** Absolutely prohibited in modules. Use explicit `@Import` and `@Bean` methods.
2. **Constructor Injection ONLY:** No `@Autowired` on fields.
3. **Conditional Beans:** EVERY bean MUST have a conditional annotation (e.g., `@ConditionalOnProperty`, `@ConditionalOnMissingBean`).
4. **Explicit Imports:** All components (services, repositories, etc.) must be explicitly imported via `@Import` in a configuration class.
5. **Test-Support Modules:** MANDATORY for ALL tests (unit and integration). Never use raw Spring Boot test dependencies.
6. **Transitive Dependency Analysis:** MANDATORY for all non-Destilink dependencies. Use `mvn dependency:tree`.

## 1. Naming Conventions

```yaml
# MODULE NAMING
modules: lowercase-with-hyphens # e.g., user-profile, order-management
functional_areas: single-word (web, aws, locking)
sub_modules: area-subarea (web-core, aws-sqs)

# PACKAGE NAMING
base_package: com.tui.destilink.framework.<module>[.<submodule>]
# e.g., com.tui.destilink.framework.user-profile.service

# CLASS NAMING PATTERNS
# --- Configuration ---
PropertiesClass: <Feature>Properties # e.g., UserProfileProperties
AutoConfigurationClass: <Feature>AutoConfiguration # e.g., UserProfileAutoConfiguration
# --- Services ---
ServiceInterface: <Domain>Service # e.g., UserProfileService
ServiceImpl: <Technology><Domain>ServiceImpl # e.g., DefaultUserProfileServiceImpl, RedisUserProfileServiceImpl
# --- Repositories (if applicable) ---
RepositoryInterface: <Domain>Repository # e.g., UserProfileRepository
# --- Utility ---
UtilityClass: <Domain>Utils # e.g., UserProfileUtils (use @UtilityClass)
# --- Factories ---
FactoryClass: <Technology>Factory # e.g., HttpClientFactory
# --- Decorators (Logging) ---
ContextDecorator: <Domain>ContextDecorator # e.g., UserProfileContextDecorator
# --- Tests ---
UnitTest: <ClassName>Test # e.g., UserProfileServiceTest
IntegrationTest: <ClassName>IT # e.g., UserProfileServiceIT
```

## 2. Standard Package Structure (per module)

```text
com.tui.destilink.framework.<module-name>/
  ├── config/             # @AutoConfiguration, @ConfigurationProperties, @Configuration
  ├── service/            # Service interfaces and implementations
  │   └── impl/           # (Optional) Internal implementations if interface is complex
  ├── client/             # External service clients (interfaces and implementations)
  ├── repository/         # (If applicable) Repository interfaces and implementations
  ├── model/              # DTOs, Entities, Value Objects (use @Data, @Value, @Builder)
  ├── exception/          # Custom exceptions for the module
  ├── util/               # Utility classes (use @UtilityClass)
  ├── event/              # (If applicable) Event classes for asynchronous communication
  ├── validation/         # Custom validation constraints and utilities
  │   ├── constraint/
  │   └── util/
  └── annotations/        # Custom annotations specific to the module
```

## 3. Auto-Configuration Essentials

* **Main Entry Point:**
  * Must be annotated with `@AutoConfiguration`.
  * Must be registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.
  * Typically enables `@ConfigurationProperties` for the module.
  * Must have a top-level `@ConditionalOnProperty` for enabling/disabling the module (e.g., `destilink.fw.mymodule.enabled`, `matchIfMissing = true`).
* **Bean Definitions:**
  * All beans defined via `@Bean` methods.
  * Bean method name MUST match return type in camelCase (e.g., `public MyService myService()`).
  * EVERY `@Bean` method MUST have at least one conditional annotation (`@ConditionalOnMissingBean`, `@ConditionalOnProperty`, `@ConditionalOnClass`, `@ConditionalOnBean`).
  * Use `@ConditionalOnMissingBean` for all primary service beans to allow user overrides.
* **Component Imports:**
  * If using stereotype annotations (`@Service`, `@Repository`, `@Component`), these classes MUST be explicitly imported via `@Import` in a configuration class (often a nested static `@Configuration` class within the `AutoConfiguration`).
  * Organize imports by layer (e.g., repository, service).
* **Nested Configurations:**
  * Use nested static `@Configuration` classes for grouping related beans or conditional features.
  * Apply `@ConditionalOnClass` or other relevant conditions at the nested class level.
  * RECOMMENDED: Use `proxyBeanMethods = false` on nested `@Configuration` classes unless proxying is explicitly needed.

**Example Snippet (`MyModuleAutoConfiguration.java`):**

```java
@AutoConfiguration
@EnableConfigurationProperties(MyModuleProperties.class)
@ConditionalOnProperty(prefix = "destilink.fw.mymodule", name = "enabled", havingValue = "true", matchIfMissing = true)
public class MyModuleAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public MyService myService(MyModuleProperties properties /*, Other Dependencies */) {
        return new DefaultMyServiceImpl(properties.getSomeConfig());
    }

    @Configuration(proxyBeanMethods = false) // Recommended
    @ConditionalOnClass(AnotherDependency.class)
    @Import(MyComponentAnnotatedService.class) // If MyComponentAnnotatedService uses @Service
    static class OptionalFeatureConfiguration {

        @Bean
        @ConditionalOnMissingBean
        @ConditionalOnProperty(prefix = "destilink.fw.mymodule.optional-feature", name = "enabled", havingValue = "true")
        public OptionalService optionalService(/* dependencies */) {
            return new OptionalServiceImpl(/* ... */);
        }
    }
}
```

**Registration (`AutoConfiguration.imports`):**

```text
com.tui.destilink.framework.mymodule.config.MyModuleAutoConfiguration
```

## 4. Dependency Management Quick Rules

* **Parent POMs:** Versions are managed by `framework-dependencies-parent` and `framework-bom`. Omit `<version>` tags for framework modules and common Spring Boot dependencies.
* **Transitive Dependencies:**
  * **MANDATORY:** Before adding ANY non-Destilink dependency, run `mvn dependency:tree -Dverbose`.
  * Verify the dependency is NOT already present transitively.
  * Document this check if there's any doubt.
  * **CRITICAL VIOLATION:** Adding starters like `spring-boot-starter-web` if `ms-core` (which includes it) is already a dependency.
* **Scopes:** Use `compile` (default), `provided` (e.g., Servlet API), `runtime` (e.g., DB drivers), `test`.
* **Test Dependencies:**
  * **MANDATORY:** Use `test-support` modules (e.g., `test-core`, `redis-test-support`).
  * Add `test-core` to ALL test classes.
  * Add technology-specific test-support modules as needed (e.g., `redis-test-support` for Redis integration tests).
  * **PROHIBITED:** Direct use of `spring-boot-starter-test` or other Spring Boot testing dependencies if a test-support module equivalent exists.

## 5. Code Style & Lombok

* **Lombok:**
  * `@Data` or `@Value` for DTOs/models.
  * `@Builder` for complex object creation.
  * `@RequiredArgsConstructor` for constructor injection (on classes with `final` fields).
  * `@Slf4j` for loggers.
  * `@UtilityClass` for utility classes.
* **Injection:** Constructor injection ONLY. All injected fields should be `final`.
* **Imports:** No wildcard imports.

## 6. Logging

* Use `org.slf4j.Logger` (via `@Slf4j`).
* Use `AbstractContextDecorator` subclasses with try-with-resources for MDC context (e.g., `TripsContextDecorator`).
* Use specialized `Markers` (e.g., `MonitoringMarkerUtils`, `SqsMessageMarker`).

## 7. Error Handling

* Web errors: `@ControllerAdvice` returning `ResponseEntity<ProblemDetail>`.
* Custom exceptions: Extend `MarkerNestedRuntimeException` or `MarkerNestedException`.

## Key Anti-Patterns to AVOID

* **`@ComponentScan` in modules.**
* **`@Autowired` on fields.**
* **`@SpringBootApplication` in framework modules.**
* Relying on package structure for component discovery.
* Defining beans without conditional annotations.
* Hardcoding versions for dependencies managed by parent POMs/BOM.
* Adding dependencies without verifying they aren't already transitive.
* Using Spring Boot test starters directly instead of `test-support` modules.

---
*This condensed guide is for quick reference. For detailed explanations, justifications, and advanced topics, please consult the full `.roo\destilink-framework-guidelines\020-guidelines-full.md`.*
