# Redis Locking Module: Lock Implementations

This diagram illustrates the different lock implementations in the `locking-redis-lock` module and their relationships.

```mermaid
classDiagram
    %% Base Interfaces
    class AsyncLock {
        <<interface>>
        +lockAsync(): CompletableFuture~Void~
        +tryLockAsync(): CompletableFuture~Boolean~
        +tryLockAsync(timeout: long, unit: TimeUnit): CompletableFuture~Boolean~
        +unlockAsync(): CompletableFuture~Void~
        +isLockedAsync(): CompletableFuture~Boolean~
        +isHeldByCurrentThreadAsync(): CompletableFuture~Boolean~
        +getHoldCountAsync(): CompletableFuture~Integer~
    }
    
    class Lock {
        <<interface>>
        +lock(): void
        +lockInterruptibly(): void
        +tryLock(): boolean
        +tryLock(timeout: long, unit: TimeUnit): boolean
        +unlock(): void
        +newCondition(): Condition
    }
    
    class ReadWriteLock {
        <<interface>>
        +readLock(): Lock
        +writeLock(): Lock
    }
    
    class StampedLock {
        <<interface>>
        +readLock(): long
        +tryReadLock(): long
        +tryReadLock(time: long, unit: TimeUnit): long
        +writeLock(): long
        +tryWriteLock(): long
        +tryWriteLock(time: long, unit: TimeUnit): long
        +tryConvertToWriteLock(stamp: long): long
        +tryConvertToReadLock(stamp: long): long
        +tryOptimisticRead(): long
        +validate(stamp: long): boolean
        +unlock(stamp: long): void
    }
    
    %% Abstract Base Class
    class AbstractRedisLock {
        <<abstract>>
        #redisLockOperations: RedisLockOperations
        #lockKey: String
        #lockOwnerId: String
        #lockBucket: String
        #lockConfig: LockBucketConfig
        #requestUuid: UUID
        #lockSemaphoreHolder: LockSemaphoreHolder
        #lockWatchdog: LockWatchdog
        #lockComponentRegistry: LockComponentRegistry
        #lockMonitor: LockMonitor
        #lockErrorHandler: RedisLockErrorHandler
        #acquireLock(nonBlocking: boolean, timeoutMs: long): CompletableFuture~Boolean~
        #releaseLock(): CompletableFuture~Boolean~
        #isLocked(): CompletableFuture~Boolean~
        #isHeldByCurrentThread(): CompletableFuture~Boolean~
        #getHoldCount(): CompletableFuture~Integer~
        #registerLockInWatchdog(): void
        #unregisterLockFromWatchdog(): void
    }
    
    %% Concrete Lock Implementations
    class RedisReentrantLock {
        -threadLocalHoldCount: ThreadLocal~Integer~
        +lock(): void
        +lockInterruptibly(): void
        +tryLock(): boolean
        +tryLock(timeout: long, unit: TimeUnit): boolean
        +unlock(): void
        +lockAsync(): CompletableFuture~Void~
        +tryLockAsync(): CompletableFuture~Boolean~
        +tryLockAsync(timeout: long, unit: TimeUnit): CompletableFuture~Boolean~
        +unlockAsync(): CompletableFuture~Void~
        +isLocked(): boolean
        +isHeldByCurrentThread(): boolean
        +getHoldCount(): int
    }
    
    class RedisReadWriteLock {
        -readLock: ReadLock
        -writeLock: WriteLock
        +readLock(): Lock
        +writeLock(): Lock
        
        class ReadLock {
            -parent: RedisReadWriteLock
            -threadLocalReadHoldCount: ThreadLocal~Integer~
            +lock(): void
            +tryLock(): boolean
            +unlock(): void
        }
        
        class WriteLock {
            -parent: RedisReadWriteLock
            -threadLocalWriteHoldCount: ThreadLocal~Integer~
            +lock(): void
            +tryLock(): boolean
            +unlock(): void
        }
    }
    
    class RedisStampedLock {
        -threadLocalLocks: ThreadLocal~ConcurrentMap~String, Integer~~
        -stampPrefix: Map~StampType, String~
        +readLock(): long
        +tryReadLock(): long
        +writeLock(): long
        +tryWriteLock(): long
        +tryOptimisticRead(): long
        +validate(stamp: long): boolean
        +tryConvertToWriteLock(stamp: long): long
        +tryConvertToReadLock(stamp: long): long
        +unlock(stamp: long): void
        
        class WriteLock {
            -parent: RedisStampedLock
            +lock(): void
            +tryLock(): boolean
            +unlock(): void
        }
        
        class ReadLock {
            -parent: RedisStampedLock
            +lock(): void
            +tryLock(): boolean
            +unlock(): void
        }
        
        class OptimisticReadLock {
            -parent: RedisStampedLock
            +tryLock(): boolean
            +validate(): boolean
        }
        
        enum StampType {
            WRITE
            READ
            OPTIMISTIC
        }
    }
    
    class RedisStateLock {
        -stateKey: String
        -stateSerializer: Function~Object, String~
        -stateDeserializer: Function~String, Object~
        +tryLockWithState~T~(expectedState: T, newState: T): boolean
        +tryLockWithStateAsync~T~(expectedState: T, newState: T): CompletableFuture~Boolean~
        +getState~T~(): T
        +getStateAsync~T~(): CompletableFuture~T~
    }
    
    %% Supporting Services
    class RedisLockOperations {
        +tryLock(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
        +isLocked(lockKey: String): CompletableFuture~Boolean~
        +getLockOwner(lockKey: String): CompletableFuture~String~
        +tryReadLock(lockKey: String, readerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlockReadLock(lockKey: String, readerId: String): CompletableFuture~Boolean~
        +tryWriteLock(lockKey: String, writerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlockWriteLock(lockKey: String, writerId: String): CompletableFuture~Boolean~
        +tryStampedLock(lockKey: String, ownerId: String, stampType: String, leaseTimeMs: long): CompletableFuture~String~
        +validateStamp(lockKey: String, stamp: String): CompletableFuture~Boolean~
        +unlockStampedLock(lockKey: String, stamp: String): CompletableFuture~Boolean~
        +tryStateLock(lockKey: String, ownerId: String, stateKey: String, expectedState: String, newState: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +getState(lockKey: String, stateKey: String): CompletableFuture~String~
    }
    
    class LockWatchdog {
        +registerLock(lockKey: String, ownerId: String, leaseTimeMs: long): void
        +unregisterLock(lockKey: String, ownerId: String): void
        +refreshLease(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
    }
    
    class LockSemaphoreHolder {
        +getSemaphore(lockKey: String): Semaphore
        +releaseSemaphore(lockKey: String): void
        +notifyUnlock(lockKey: String): void
    }
    
    %% Inheritance Relationships
    Lock <|.. RedisReentrantLock
    AsyncLock <|.. RedisReentrantLock
    AbstractRedisLock <|-- RedisReentrantLock
    
    ReadWriteLock <|.. RedisReadWriteLock
    AbstractRedisLock <|-- RedisReadWriteLock
    RedisReadWriteLock *-- RedisReadWriteLock.ReadLock
    RedisReadWriteLock *-- RedisReadWriteLock.WriteLock
    Lock <|.. RedisReadWriteLock.ReadLock
    Lock <|.. RedisReadWriteLock.WriteLock
    
    StampedLock <|.. RedisStampedLock
    AbstractRedisLock <|-- RedisStampedLock
    RedisStampedLock *-- RedisStampedLock.WriteLock
    RedisStampedLock *-- RedisStampedLock.ReadLock
    RedisStampedLock *-- RedisStampedLock.OptimisticReadLock
    RedisStampedLock *-- RedisStampedLock.StampType
    Lock <|.. RedisStampedLock.WriteLock
    Lock <|.. RedisStampedLock.ReadLock
    
    Lock <|.. RedisStateLock
    AsyncLock <|.. RedisStateLock
    AbstractRedisLock <|-- RedisStateLock
    
    %% Dependency Relationships
    AbstractRedisLock --> RedisLockOperations : uses
    AbstractRedisLock --> LockWatchdog : uses
    AbstractRedisLock --> LockSemaphoreHolder : uses
    
    %% Notes
    note for AbstractRedisLock "Base class with common Redis lock operations"
    note for RedisReentrantLock "Standard reentrant lock with thread-local reentrancy tracking"
    note for RedisReadWriteLock "Multiple readers, single writer lock"
    note for RedisStampedLock "Optimistic/pessimistic lock with stamped access"
    note for RedisStateLock "Lock with atomic state transition"
```

## Lock Implementation Types

The Redis Locking module provides four main lock implementations, each serving different concurrency control needs:

### 1. RedisReentrantLock

A standard reentrant mutual exclusion lock implementation that:
- Allows the same thread to acquire the lock multiple times (reentrancy)
- Requires the same number of unlock calls as lock calls
- Tracks reentrancy using ThreadLocal variables
- Implements both synchronous (java.util.concurrent.locks.Lock) and asynchronous (AsyncLock) interfaces

**Key Features:**
- Thread-local hold count tracking
- Automatic lease extension via watchdog
- Non-polling wait for lock acquisition via Redis Pub/Sub
- Configurable timeout and retry behavior

**Usage Example:**
```java
RedisReentrantLock lock = lockFactory.createReentrantLock("user:123:profile");
try {
    lock.lock();
    // Critical section
} finally {
    lock.unlock();
}

// Async usage
lock.lockAsync()
    .thenRun(() -> {
        // Critical section
    })
    .whenComplete((v, e) -> lock.unlockAsync());
```

### 2. RedisReadWriteLock

A read-write lock implementation that:
- Allows multiple concurrent readers
- Allows only one exclusive writer
- Prevents readers when a writer holds the lock
- Prevents writers when any reader holds the lock
- Provides separate ReadLock and WriteLock instances

**Key Features:**
- Separate read and write lock tracking in Redis
- Reader count tracking via Redis Hash
- Individual read lock timeout keys
- Writer exclusivity via owner ID

**Usage Example:**
```java
RedisReadWriteLock rwLock = lockFactory.createReadWriteLock("catalog:products");

// Read access
Lock readLock = rwLock.readLock();
try {
    readLock.lock();
    // Read-only operations
} finally {
    readLock.unlock();
}

// Write access
Lock writeLock = rwLock.writeLock();
try {
    writeLock.lock();
    // Write operations
} finally {
    writeLock.unlock();
}
```

### 3. RedisStampedLock

A stamped lock implementation that:
- Supports three modes: write (exclusive), read (shared), and optimistic read
- Returns stamps that represent lock acquisition state
- Allows conversion between lock modes
- Provides optimistic concurrency control

**Key Features:**
- String-based stamps with mode prefixes (W:, R:, O:)
- Stamp validation for optimistic reads
- Lock mode conversion
- ThreadLocal tracking of all acquired stamps

**Usage Example:**
```java
RedisStampedLock stampedLock = lockFactory.createStampedLock("order:123:processing");

// Write lock
long writeLockStamp = stampedLock.writeLock();
try {
    // Write operations
} finally {
    stampedLock.unlock(writeLockStamp);
}

// Read lock
long readLockStamp = stampedLock.readLock();
try {
    // Read operations
} finally {
    stampedLock.unlock(readLockStamp);
}

// Optimistic read
long optimisticStamp = stampedLock.tryOptimisticRead();
// Read data
if (stampedLock.validate(optimisticStamp)) {
    // Data was read consistently
} else {
    // Data may be inconsistent, retry with read lock
}

// Lock conversion
long stamp = stampedLock.readLock();
// Read operations
// Try to upgrade to write lock
long writeStamp = stampedLock.tryConvertToWriteLock(stamp);
if (writeStamp != 0L) {
    // Upgrade successful
    stamp = writeStamp;
    // Write operations
}
stampedLock.unlock(stamp);
```

### 4. RedisStateLock

A specialized lock that:
- Combines locking with atomic state transition
- Ensures the current state matches an expected value before acquiring the lock
- Updates the state atomically when the lock is acquired
- Provides type-safe state access via serialization/deserialization functions

**Key Features:**
- Atomic compare-and-set state operations
- Type-safe state access via serialization/deserialization
- Separate state key within the lock key
- Customizable state serialization

**Usage Example:**
```java
RedisStateLock<OrderStatus> stateLock = lockFactory.createStateLock(
    "order:123:status",
    OrderStatus.class,
    status -> status.name(),
    name -> OrderStatus.valueOf(name)
);

boolean acquired = stateLock.tryLockWithState(OrderStatus.PENDING, OrderStatus.PROCESSING);
if (acquired) {
    try {
        // Process order
    } finally {
        stateLock.unlock();
    }
} else {
    OrderStatus currentStatus = stateLock.getState();
    // Handle inability to transition state
}
```

## Implementation Details

### Common Base: AbstractRedisLock

All lock implementations extend `AbstractRedisLock`, which provides:

1. **Core Redis Operations**:
   - Delegates to `RedisLockOperations` for Redis commands
   - Handles retry logic and timeout behavior
   - Manages lock acquisition and release

2. **Watchdog Integration**:
   - Registers locks with `LockWatchdog` for lease extension
   - Automatically extends lock leases to prevent expiration
   - Unregisters locks when explicitly released

3. **Non-Blocking Wait**:
   - Uses `LockSemaphoreHolder` for efficient waiting
   - Leverages Redis Pub/Sub for unlock notifications
   - Avoids polling for lock availability

4. **Error Handling**:
   - Translates Redis errors to specific exception types
   - Enriches exceptions with contextual information
   - Provides consistent error behavior across implementations

5. **Metrics**:
   - Records acquisition attempts, success/failure rates
   - Tracks lock hold times and contention
   - Monitors watchdog activity

### Thread Safety and Reentrancy

All lock implementations use ThreadLocal variables to track:

1. **Hold Counts**: Number of times the current thread has acquired the lock
2. **Owner IDs**: Unique identifiers for lock ownership
3. **Stamps**: For StampedLock, all acquired stamps by the current thread

This approach ensures thread safety without relying on synchronized blocks or atomic variables, which would create contention points.

### Redis Data Structures

Each lock type uses different Redis data structures:

1. **RedisReentrantLock**:
   - String key with owner ID as value
   - TTL for automatic expiration

2. **RedisReadWriteLock**:
   - Hash with fields:
     - `write_owner_id`: ID of write lock holder (or empty)
     - `read_holders`: Count of read lock holders
   - Separate TTL keys for each reader

3. **RedisStampedLock**:
   - Hash with fields:
     - `owner_id`: Current lock owner
     - `mode`: Current lock mode (WRITE, READ, OPTIMISTIC)
     - `stamp`: Current valid stamp
   - TTL for automatic expiration

4. **RedisStateLock**:
   - String key with owner ID as value
   - Separate key for state value
   - TTL for automatic expiration

### Lua Scripts

Lock operations are implemented as Lua scripts to ensure atomicity:

1. **try_lock.lua**: Atomic lock acquisition with owner check
2. **unlock.lua**: Atomic lock release with owner validation
3. **try_read_lock.lua**: Atomic read lock acquisition
4. **unlock_read_lock.lua**: Atomic read lock release
5. **try_write_lock.lua**: Atomic write lock acquisition
6. **unlock_write_lock.lua**: Atomic write lock release
7. **try_stamped_lock.lua**: Atomic stamped lock acquisition
8. **validate_stamp.lua**: Atomic stamp validation
9. **unlock_stamped_lock.lua**: Atomic stamped lock release
10. **try_state_lock.lua**: Atomic state lock acquisition with CAS