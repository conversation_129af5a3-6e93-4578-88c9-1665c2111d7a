# Lua Script Execution Architecture

```mermaid
classDiagram
    class ScriptLoader {
        +ClusterCommandExecutor commandExecutor
        +Map<String, RedisScript> loadedScripts
        +Map<String, String> scriptShas
        +@PostConstruct void loadScripts()
        +String getScriptSha(String scriptName)
        +String getScriptContent(String scriptName)
        +RedisScript<T> getScript(String scriptName)
    }
    note for ScriptLoader "Loads and caches Lua scripts at startup"

    class RedisScript {
        <<Interface>>
        +String getScriptAsString()
        +String getSha1()
        +Class<T> getResultType()
    }

    class DefaultRedisScript {
        +String scriptText
        +String sha1
        +Class<T> resultType
        +void setScriptText(String scriptText)
        +void setResultType(Class<T> resultType)
    }
    DefaultRedisScript ..|> RedisScript

    class RedisLockOperations {
        <<Interface>>
        +CompletableFuture<T> executeScriptAsync(String scriptName, List<String> keys, List<String> args)
    }

    class RedisLockOperationsImpl {
        +ScriptLoader scriptLoader
        +ClusterCommandExecutor commandExecutor
        +RedisLockErrorHandler errorHandler
        +LockBucket lockBucket
        +CompletableFuture<T> executeScriptAsync(String scriptName, List<String> keys, List<String> args)
        +CompletableFuture<T> tryLockAsyncInternal(String lockName, String requestUuid, long leaseTimeMs, String lockOwnerId, boolean reentrancyEnabled)
        +CompletableFuture<T> unlockAsyncInternal(String lockName, String requestUuid, String lockOwnerId, boolean decrementReentrant)
        +CompletableFuture<T> renewLeaseAsync(String lockName, String requestUuid, long newLeaseTimeMs, String lockOwnerId)
        +CompletableFuture<Void> publishUnlockMessage(String lockName, String unlockType)
    }
    RedisLockOperationsImpl ..|> RedisLockOperations

    class ClusterCommandExecutor {
        <<External from redis-core>>
        +CompletableFuture<Object> executeScriptAsync(String sha, List<String> keys, List<String> args)
        +CompletableFuture<Object> evalScriptAsync(String script, List<String> keys, List<String> args)
        +CompletableFuture<Long> publishAsync(String channel, String message)
    }

    class LuaScripts {
        <<Enumeration>>
        TRY_LOCK("try_lock.lua")
        UNLOCK("unlock.lua")
        EXTEND_LOCK("extend_lock.lua")
        TRY_STATE_LOCK("try_state_lock.lua")
        UPDATE_STATE("update_state.lua")
        UNLOCK_STATE_LOCK("unlock_state_lock.lua")
        TRY_READ_LOCK("try_read_lock.lua")
        UNLOCK_READ_LOCK("unlock_read_lock.lua")
        TRY_WRITE_LOCK("try_write_lock.lua")
        UNLOCK_WRITE_LOCK("unlock_write_lock.lua")
        TRY_STAMPED_LOCK("try_stamped_lock.lua")
        UNLOCK_STAMPED_LOCK("unlock_stamped_lock.lua")
        VALIDATE_STAMP("validate_stamp.lua")
        CONVERT_TO_WRITE_LOCK("convert_to_write_lock.lua")
        CONVERT_TO_READ_LOCK("convert_to_read_lock.lua")
        +String fileName
        +String getFileName()
    }

    class LockBucket {
        +RedisKeyPrefix basePrefix
        +String bucketName
        +String getLockKey(String lockName)
        +String getUnlockChannel(String lockName)
        +String getUnlockChannelPattern()
        +String getStateLockKey(String lockName)
        +String getResponseCacheKey(String lockName, String requestUuid)
    }

    class RedisLockErrorHandler {
        +AbstractRedisLockException handleScriptException(Throwable t, String scriptName, List<String> keys, List<String> args)
    }

    ScriptLoader "1" *-- "0..*" RedisScript : loads and caches
    ScriptLoader --> LuaScripts : uses for script enumeration
    ScriptLoader o-- ClusterCommandExecutor : uses for SCRIPT LOAD

    RedisLockOperationsImpl o-- ScriptLoader : retrieves cached scripts
    RedisLockOperationsImpl o-- ClusterCommandExecutor : executes scripts
    RedisLockOperationsImpl o-- RedisLockErrorHandler : handles script errors
    RedisLockOperationsImpl o-- LockBucket : constructs Redis keys

    DefaultRedisScript --> LuaScripts : script content source

    RedisLockOperations --> RedisScript : executes
    RedisScript --> ClusterCommandExecutor : via ScriptLoader
```

## Lua Script Execution Flow

```mermaid
sequenceDiagram
    participant RedisLockOperationsImpl
    participant ScriptLoader
    participant ClusterCommandExecutor
    participant Redis

    Note over ScriptLoader: @PostConstruct initialization
    ScriptLoader->>+ClusterCommandExecutor: SCRIPT LOAD for all scripts
    ClusterCommandExecutor->>+Redis: SCRIPT LOAD try_lock.lua, unlock.lua, etc.
    Redis-->>-ClusterCommandExecutor: SHA1 hashes for each script
    ClusterCommandExecutor-->>-ScriptLoader: Store SHA1 mappings

    Note over RedisLockOperationsImpl: Runtime script execution
    RedisLockOperationsImpl->>+ScriptLoader: getScriptSha("try_lock")
    ScriptLoader-->>-RedisLockOperationsImpl: SHA1 hash
    
    RedisLockOperationsImpl->>+ClusterCommandExecutor: executeScriptAsync(sha1, keys, args)
    ClusterCommandExecutor->>+Redis: EVALSHA sha1 keys args
    
    alt Script exists in cache
        Redis-->>-ClusterCommandExecutor: Script result
        ClusterCommandExecutor-->>-RedisLockOperationsImpl: CompletableFuture<Result>
    else Script not in cache (NOSCRIPT error)
        Redis-->>ClusterCommandExecutor: NOSCRIPT error
        ClusterCommandExecutor->>ScriptLoader: getScriptContent("try_lock")
        ScriptLoader-->>ClusterCommandExecutor: Lua script content
        ClusterCommandExecutor->>Redis: EVAL script_content keys args
        Redis-->>ClusterCommandExecutor: Script result
        ClusterCommandExecutor-->>RedisLockOperationsImpl: CompletableFuture<Result>
    end
```
