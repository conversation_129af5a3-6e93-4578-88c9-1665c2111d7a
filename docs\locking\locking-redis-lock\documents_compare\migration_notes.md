# Modernized Redis Locking Module: Migration Notes

This document provides guidance for users migrating from the previous implementation of the Redis Locking Module to the modernized version. The modernized module introduces significant changes to improve performance, reliability, and maintainability. This document highlights the key differences and potential impacts on existing applications. **Understanding these changes is mandatory for a successful migration, as defined in the source document `lock-final/redis-lock-improvements.md`.**

## Key Changes and Migration Considerations

The following are the major changes introduced in the modernized module that may require adjustments in applications using the previous version:

*   **Lock Acquisition Mechanism (Non-Polling)**: The most significant change is the move from a polling-based lock acquisition to a notification-based mechanism using Redis Pub/Sub and semaphores.
    *   **Impact**: Applications no longer need to configure polling intervals or worry about excessive Redis traffic from polling. The waiting behavior is now driven by unlock notifications.
    *   **Migration**: No code changes are typically required in client applications for basic lock acquisition (`lock()`, `tryLock()`). However, understanding the new mechanism is important for debugging and performance tuning.
*   **Use of Lua Scripts**: Critical lock operations are now performed using atomic Lua scripts executed on the Redis server.
    *   **Impact**: Improved atomicity and reduced round trips to Redis for complex operations.
    *   **Migration**: No direct impact on client applications unless they were interacting with Redis keys managed by the lock in ways that assumed non-atomic operations.
*   **Updated Exception Hierarchy**: The module introduces a new, more granular exception hierarchy rooted at `AbstractRedisLockException`.
    *   **Impact**: Applications that caught specific exceptions from the previous module may need to update their catch blocks to handle the new exception types.
    *   **Migration**: Review exception handling code and update it to catch the appropriate exceptions from the `com.tui.destilink.framework.locking.redis.exception` package. Refer to the [Exception Handling](exception_handling.md) documentation.
*   **Messaging Mechanism (Redis Pub/Sub)**: The module relies on Redis Pub/Sub for unlock notifications.
    *   **Impact**: Requires Redis Pub/Sub to be enabled and accessible.
    *   **Migration**: Ensure your Redis infrastructure supports Pub/Sub and that network configurations allow communication. The `unlockMessageListenerEnabled` configuration property controls this feature.
*   **New Redis Key Schema**: The structure of Redis keys used by the module has changed. This includes a new prefix structure, namespaces, and the use of Redis HashMaps for storing reentrant lock data.
    *   **Impact**: Direct interaction with Redis keys managed by the lock module (e.g., for monitoring or debugging) will require using the new schema.
    *   **Migration**: Update any monitoring dashboards, scripts, or tools that directly access lock-related keys in Redis to use the new schema. Refer to the [Redis Key Schema](redis_key_schema.md) documentation.
*   **Lock Watchdog Behavior**: The watchdog mechanism for extending lock leases may have updated behavior or configuration options.
    *   **Impact**: Review the watchdog configuration properties and ensure they meet your application's requirements.
    *   **Migration**: Refer to the [Watchdog](watchdog.md) documentation for details on configuration and behavior.
*   **Metrics**: The module exposes metrics via Micrometer.
    *   **Impact**: Provides enhanced visibility into lock usage and performance.
    *   **Migration**: Configure Micrometer reporting in your application to collect and visualize these metrics. Refer to the [Metrics](metrics.md) documentation.
*   **Configuration Changes**: Configuration is now managed primarily through the `RedisLockProperties` class. Some property names or structures may have changed.
    *   **Impact**: Existing configuration in application properties or YAML files will need to be updated to the new structure and property names.
    *   **Migration**: Review your application's configuration files and update them according to the [Configuration](configuration.md) documentation. **Note specifically that the `defaultTimeout` property is not used for the indefinite `lock()` method; this method now blocks forever as per the `java.util.concurrent.Lock` interface.**
*   **Removal of `defaultTimeout` for `lock()`**: The `lock()` method no longer uses a configurable default timeout and will block indefinitely.
    *   **Impact**: Applications relying on a default timeout for the `lock()` method will need to switch to using `tryLock(long timeout, TimeUnit unit)` instead.
    *   **Migration**: Identify any usages of the parameterless `lock()` method where a timeout was implicitly expected and refactor to use `tryLock(long timeout, TimeUnit unit)` with an explicit timeout.

## Compatibility

The modernized module aims to maintain source compatibility where possible for core `java.util.concurrent.Lock` interface methods (`lock()`, `unlock()`, `tryLock()`). However, due to the underlying architectural changes, behavioral differences exist (e.g., non-polling).

## Rollback Plan

In case of issues after migrating, the rollback plan involves reverting to the previous version of the Redis Locking Module dependency and restoring the previous configuration.

Careful review of this document and the linked detailed documentation is recommended for a smooth migration process.