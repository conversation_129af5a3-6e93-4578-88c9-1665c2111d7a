# Modernized Redis Locking Module: Lock Acquisition Mechanism

This document details the modernized lock acquisition mechanism, which moves away from inefficient polling towards a notification-based approach using Redis Pub/Sub and semaphores. **The flows described in this document are mandatory and must be followed, as defined in the source documents `lock-final/redis-lock-improvements.md`, `lock-final/redis-lock-infinite-wait-flowchart.md`, and `lock-final/redis-lock-timeout-flowchart.md`.**

## Issues with Current Polling Mechanism

The previous implementation relied on repeatedly polling Redis to check the lock status. This approach had several drawbacks:

*   **Inefficient Redis Traffic**: Generated unnecessary load on the Redis server, especially under high contention.
*   **Wasted Client Resources**: Threads actively waited and consumed CPU resources while polling.
*   **Thundering Herd Problem**: Could exacerbate issues under contention as many threads simultaneously attempt to acquire the lock.

## Notification-Based Acquisition with Semaphores

The modernized approach replaces polling with a mechanism where threads wait for an unlock notification via Redis Pub/Sub.

### Core Mechanism

1.  When a lock acquisition attempt fails because the lock is held, the thread registers itself with an `UnlockMessageListener` for the specific lock key.
2.  The thread then waits passively on a `LockSemaphoreHolder` associated with that lock key. The wait has a timeout based on the lock's remaining TTL or a configured retry interval.
3.  When another thread releases the lock, it publishes an unlock message to a Redis Pub/Sub channel.
4.  The `UnlockMessageListener` receives this message, identifies the relevant lock key, and signals the corresponding `LockSemaphoreHolder`.
5.  Signaling the semaphore wakes up one or more waiting threads (depending on the lock type and configuration).
6.  The awakened thread(s) then retry the lock acquisition attempt.

This process continues until the lock is successfully acquired or, in the case of `tryLock(timeout)`, the overall timeout is reached.

### `lock()` vs. `tryLock()`

The module provides two primary methods for acquiring a lock:

*   **`lock()`**: This method attempts to acquire the lock and **blocks indefinitely** until the lock is available and acquired, or the waiting thread is interrupted. This behavior strictly adheres to the `java.util.concurrent.Lock` interface specification. **The `defaultTimeout` property is not used in this method.**
*   **`tryLock(long timeout, TimeUnit unit)`**: This method attempts to acquire the lock and waits for a specified maximum time. If the lock is acquired within the timeout, it returns `true`; otherwise, it returns `false`.

### Lock Acquisition Flowcharts

The following flowcharts illustrate the process for infinite wait (`lock()`) and timeout (`tryLock(timeout)`) scenarios. **These flows are mandatory and define the required behavior.**

#### Infinite Wait Scenario (`lock()`)

```mermaid
flowchart TD
    A[Start: Acquire Lock (lock())] --> B[Register Semaphore/CompletableFuture]
    B --> C[Issue Direct Polling Lock Request]
    C --> D[Redis Server Executes Lua Script]
    D --> E{Lock Acquired?}
    E -- Yes --> F[Lock Acquired]
    F --> G[Unregister Semaphore/CompletableFuture]
    G --> H[End]
    E -- No --> I[Lock Locked, Return TTL]
    I --> J[Wait on Semaphore/CompletableFuture up to TTL or Interruption]
    J --> K{Unlock Message Received or TTL Expired or Interrupted?}
    K -- Yes --> C
    K -- Interrupted --> L[Throw LockInterruptedException]
    L --> G
```

**Process Description:**

1.  The process begins by registering a semaphore/CompletableFuture with the UnlockMessageListener.
2.  A direct polling lock request is issued to Redis.
3.  Redis executes a Lua script to attempt lock acquisition.
4.  If the lock is acquired, the process completes successfully.
5.  If the lock is already locked:
    *   The TTL is returned by the Lua script.
    *   The thread waits on the semaphore/CompletableFuture up to the TTL or until interrupted.
    *   When an unlock message is received, the TTL expires, or the thread is interrupted, the process continues.
    *   If interrupted, a `LockInterruptedException` is thrown.
    *   Otherwise (unlock message or TTL expired), the process loops back to issue another direct polling lock request.
    *   This is because checking the lock status is not atomic and must be done within a transaction via the Lua script.
6.  This process continues indefinitely until the lock is acquired or the thread is interrupted.

#### Timeout Scenario (`tryLock(timeout)`)

```mermaid
flowchart TD
    A[Start: Acquire Lock (tryLock(timeout))] --> B[Register Semaphore/CompletableFuture]
    B --> C[Issue Direct Polling Lock Request]
    C --> D[Redis Server Executes Lua Script]
    D --> E{Lock Acquired?}
    E -- Yes --> F[Lock Acquired]
    F --> G[Unregister Semaphore/CompletableFuture]
    G --> H[End: Return true]
    E -- No --> I[Lock Locked, Return TTL]
    I --> J{Wait with Timeout}
    J -- Timeout Reached --> K[End: Return false]
    K --> G
    J -- Unlock Message Received Before Timeout --> C
```

**Process Description:**

1.  The process begins by registering a semaphore/CompletableFuture with the UnlockMessageListener.
2.  A direct polling lock request is issued to Redis.
3.  Redis executes a Lua script to attempt lock acquisition.
4.  If the lock is acquired, the process completes successfully and returns `true`.
5.  If the lock is already locked:
    *   The TTL is returned by the Lua script.
    *   The thread waits on the semaphore/CompletableFuture with a timeout.
    *   If the timeout is reached before an unlock message, the process stops and returns `false`.
    *   If an unlock message is received before timeout, the process loops back to issue another direct polling lock request.
    *   This is because checking the lock status is not atomic and must be done within a transaction via the Lua script.
6.  In all cases, the semaphore/CompletableFuture is unregistered at the end.

## Implementation Details

*   **`LockSemaphoreHolder`**: A class holding a `java.util.concurrent.Semaphore` (initialized with 0 permits, fair mode) for a specific lock key. It tracks the number of threads currently waiting (`waitingThreads` AtomicInteger) on that specific lock. It includes methods to increment/decrement waiters, get the waiters count, get the last access time, wait for an unlock signal with a timeout (`waitForUnlock`), and signal the semaphore (`signal`). This replaces the previous `CompletableFuture`-based map for managing waiting threads.
*   **`waitingNotifications` Map**: A `ConcurrentHashMap<String, LockSemaphoreHolder>` is used to map lock keys to their corresponding `LockSemaphoreHolder` instances. This allows the `UnlockMessageListener` to quickly find and signal the correct semaphore when an unlock message is received.
*   **Fallback Mechanism**: Due to the non-guaranteed delivery of Redis Pub/Sub messages, a fallback mechanism is in place where threads also wake up after the lock's TTL expires (or a configured retry interval) to re-check the lock status via a direct request.
*   **Atomic Operations**: Lock acquisition and release operations are performed using Lua scripts executed on the Redis server to ensure atomicity.

This modernized acquisition mechanism significantly reduces the load on Redis and improves the efficiency of client threads waiting for locks.