# Redis Locking Module: Final Glossary

This document defines key terms used within the `locking-redis-lock` module and its associated documentation, consolidated from previous planning phases and updated to reflect the Async-First design and explicit `redis-core` integration.

*   **AbstractRedisLock**: Base Java class providing common functionality for all Redis lock implementations in the module. It orchestrates the asynchronous `CompletableFuture`-based operations.
*   **Application-Instance-Bound Lock**: A lock whose ownership is tied to a specific application instance (e.g., a specific pod ID). These locks are typically eligible for watchdog lease renewal.
*   **Async-First**: A core design principle of this module where all fundamental lock operations are asynchronous, returning `CompletableFuture`s. Synchronous `java.util.concurrent.locks.Lock` methods are implemented as wrappers around these asynchronous operations.
*   **AsyncLock**: An interface that extends `java.util.concurrent.locks.Lock`. It provides non-blocking, `CompletableFuture`-based asynchronous versions of standard lock operations, suitable for reactive programming models and high-concurrency scenarios to avoid thread blocking. All concrete lock implementations in this module must implement this interface.
*   **Atomic Operation**: An operation that is guaranteed to execute fully without interruption or interference from other operations, typically ensured by executing commands as a single transaction or, more commonly in this module, as a Lua script on the Redis server. All atomic operations are executed via `redis-core`'s `ClusterCommandExecutor`.
*   **Bucket (Lock Bucket)**: A logical grouping or namespace for locks. Buckets allow for applying common default configurations (e.g., `leaseTime`, `retryInterval`) to a set of related locks, primarily configured programmatically via builders. The `LockOwnerSupplier` configured at the bucket level is a key factor in determining watchdog eligibility for locks within that bucket.
*   **Builder API**: A fluent API, starting with `LockBucketRegistry.builder(...)`, used to configure and create specific lock instances (e.g., `RedisReentrantLock`, `RedisStateLock`).
*   **ClusterCommandExecutor**: A critical component from `com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor.java` used for all Redis command executions within this module. It provides asynchronous, cluster-aware command execution capabilities, ensuring compliance with framework guidelines for Redis interaction.
*   **Distributed Lock**: A synchronization primitive used to coordinate access to shared resources among multiple processes or services running in a distributed environment.
*   **ExceptionMarkerProvider**: A Destilink Framework interface (`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`) implemented by custom exceptions to provide detailed contextual information for structured JSON logging, as per framework guidelines.
*   **Hash Tag (Redis Cluster)**: A mechanism (`{{...}}`) used in Redis key names to ensure that multiple keys are allocated to the same hash slot, and thus the same node, in a Redis Cluster setup. Crucial for multi-key Lua scripts executed via `ClusterCommandExecutor`.
*   **Idempotency**: Ensuring that performing an operation multiple times has the same effect as performing it once. The module implements a centralized idempotency mechanism where all mutating Redis operations are protected by a response cache system. Each logical operation is assigned a unique `requestUuid` that is used to cache operation results, preventing duplicate execution if the same operation is retried due to network issues or client failures. This centralized approach provides consistent behavior across all lock types and operations.
*   **Lease Time (`leaseTime`)**: The total duration a developer *intends* for a lock to be held. This is a configuration parameter. If the watchdog is active for the lock, it will attempt to renew the lock's presence in Redis up to this total duration. If the watchdog is not active, `leaseTime` directly becomes the TTL of the lock key in Redis. For a `RedisReadWriteLock`, each individual read lock acquisition also establishes its own lease managed by an `Individual Read Lock Timeout Key`; the main `RedisReadWriteLock` key's TTL is then maintained as the maximum of its configured lease (potentially watchdog-extended) and the maximum remaining TTL of any active individual read lock leases.
*   **Individual Read Lock Timeout Key**: A Redis String key with an associated TTL, created for each specific instance of an acquired read lock within a `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerId>:<count>`). It manages the lease for that individual read access, contributing to the overall lifetime management of the main `RedisReadWriteLock` key.
*   **Lock Key**: The unique string identifier for a specific lock's main data structure in Redis (e.g., `myApp:__lock_buckets__:orders:__locks__:{order123}`).
*   **`LockBucketConfig`**: A Java class (non-Spring managed) that holds the resolved, effective configuration settings for a specific lock bucket, after merging global defaults and programmatic builder overrides.
*   **`LockComponentRegistry`**: A Spring-managed bean that acts as a central holder for shared, stateless locking services (like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`), making them available to lock instances and other internal components.
*   **`LockOwnerSupplier`**: An interface responsible for providing a unique identifier for the current lock owner (typically combining an application instance ID and a thread ID). A `DefaultLockOwnerSupplier` bean is provided.
*   **`LockSemaphoreHolder`**: A helper class, managed per lock key (likely via Guava Cache with weak values), used by `UnlockMessageListener` to manage waiting threads for that specific lock key. It encapsulates a `java.util.concurrent.Semaphore` for synchronous waits, and is integrated with `CompletableFuture` for asynchronous waits.
*   **`LockWatchdog`**: A Spring-managed bean that periodically extends the lease (TTL) of active, application-instance-bound locks in Redis to prevent premature expiration. Its operations are performed asynchronously via `ClusterCommandExecutor`.
*   **Lua Scripts**: Scripts written in the Lua programming language that are executed atomically on the Redis server to perform complex lock operations (e.g., acquire, release, extend, state manipulation). All Lua script executions are handled by `RedisLockOperations` which in turn uses `ClusterCommandExecutor`.
*   **MDC (Mapped Diagnostic Context)**: A logging mechanism used with SLF4J to enrich log messages with contextual data (e.g., `lock.name`, `lock.operation`). Managed via `LockContextDecorator`, adhering to framework logging guidelines.
*   **Non-Polling Wait**: A mechanism where threads waiting for a lock do not continuously check its status. Instead, they wait on a synchronization primitive (like a `Semaphore` in `LockSemaphoreHolder`) that is signaled when the lock is released (via Redis Pub/Sub). This is the primary waiting strategy, used by both synchronous and asynchronous lock acquisition.
*   **Override Precedence**: The order in which configuration settings are applied: Instance-specific settings (via builder methods) > Programmatic Bucket Configuration (via `LockBucketBuilder`) > Global Configuration (YAML/Java defaults in `RedisLockProperties`).
*   **Pub/Sub (Publish/Subscribe)**: A Redis messaging paradigm used by `UnlockMessageListener` instances to receive notifications when locks are released, enabling efficient, non-polling waits.
*   **RedisCoreProperties**: The configuration properties class (`com.tui.destilink.framework.redis.core.config.RedisCoreProperties.java`) from the `redis-core` module. This module uses these properties to ensure consistent Redis client configuration across the framework.
*   **Reentrant Lock**: A lock that can be acquired multiple times by the same owner (e.g., the same thread) without deadlocking. Each `lock()` call must be matched by an `unlock()` call. Reentrancy data (owner, count) is stored in a Redis Hash.
*   **`RedisLockAutoConfiguration`**: The Spring Boot `@AutoConfiguration` class responsible for setting up all the necessary beans for the locking module, strictly adhering to framework guidelines (e.g., no `@ComponentScan`, explicit `@Bean` definitions).
*   **`RedisLockErrorHandler`**: A Spring-managed bean responsible for translating low-level Redis exceptions (often from `ClusterCommandExecutor`) into specific, contextual `AbstractRedisLockException` subtypes.
*   **`RedisLockOperations`**: A Spring-managed bean that abstracts direct Redis communication for lock-specific commands. **Crucially, it delegates all Redis command executions to `ClusterCommandExecutor` from `redis-core`, ensuring an asynchronous interaction model.**
*   **`RedisLockProperties`**: A Spring `@ConfigurationProperties` class that binds global settings from YAML files (e.g., `destilink.fw.locking.redis.*`).
*   **Request UUID (`requestUuid`)**: A unique identifier (UUID) generated centrally by `RedisLockOperationsImpl` for each logical lock operation (e.g., a single `tryLock` call, including its internal retries). This UUID serves as the key for the centralized idempotency mechanism, ensuring that if the same logical operation is retried due to network issues or client failures, it will not be executed multiple times. The same `requestUuid` is used for all retry attempts of a single logical operation, providing consistent idempotency behavior across all lock types and operations.
*   **Response Cache**: A centralized Redis-based cache system that stores the results of mutating lock operations, keyed by `requestUuid`. This cache is managed entirely within Lua scripts and provides the foundation for the idempotency mechanism. When a Lua script receives a `requestUuid`, it first checks this cache; if a result exists, it returns the cached result instead of re-executing the operation. If no cached result exists, the operation proceeds and its result is stored in the cache with a configurable TTL (`responseCacheTtl`).
*   **Retry Interval (`retryInterval`)**: The duration `AbstractRedisLock` waits after a semaphore wait (which might have timed out based on the current lock holder's TTL) before re-attempting to acquire the lock via Lua script. This acts as a fallback.
*   **`ScriptLoader`**: A Spring-managed bean that loads and caches all Lua scripts from the classpath at application startup.
*   **StateLock (`RedisStateLock`)**: A type of lock that, in addition to standard locking, also manages and potentially gates access based on an associated "state" value stored in Redis.
*   **StampedLock (`RedisStampedLock`)**: A lock type that provides optimistic read locking and pessimistic write locking, using a "stamp" or version number to detect intervening writes.
*   **TTL (Time-To-Live)**: The actual expiration time set on a key in Redis. For locks managed by the watchdog, this is typically the `watchdogMaxTtl`. For locks not managed by the watchdog, this is the `leaseTime`.
*   **`UnlockMessageListener`**: A component, managed by `UnlockMessageListenerManager` and instantiated per lock bucket. It subscribes to a Redis Pub/Sub channel pattern specific to its designated bucket (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`). Upon receiving a message on a specific channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`), it derives the `lockName` (e.g., `{<lockName>}`) from the channel and parses the `UnlockType` from the message payload (which contains only the `UnlockType` string). It then signals the appropriate `LockSemaphoreHolder` or completes a `CompletableFuture` for asynchronous waiters.
*   **`UnlockMessageListenerManager`**: A Spring-managed bean that creates and manages the lifecycle of `UnlockMessageListener` instances (typically one per configured lock bucket).
*   **`UnlockType`**: A string constant published as the message payload to a specific lock's Pub/Sub unlock channel. It indicates the nature of the unlock event (e.g., "REENTRANT_FULLY_RELEASED", "RW_WRITE_RELEASED_WAKEN_ALL"). The `UnlockMessageListener` uses this type, along with the `lockName` derived from the channel, to apply optimized waking strategies for waiting threads. (See [Unlock Messaging](messaging.md) for detailed types).
*   **Watchdog Maximum TTL (`watchdogMaxTtl`)**: An internal global configuration property. It's the duration for which the `LockWatchdog` sets/extends the TTL of a Redis lock key during each renewal cycle.
*   **Watchdog Renewal Multiplier (`watchdogRenewalMultiplier`)**: An internal global configuration property used to determine how frequently the watchdog attempts to renew a lock. Renewal Interval = `watchdogMaxTtl / watchdogRenewalMultiplier`.