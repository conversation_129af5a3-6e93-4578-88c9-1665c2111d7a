package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * Redis-based implementation of a Read Lock, part of a ReadWriteLock pair.
 * <p>
 * This lock allows multiple readers to acquire the lock concurrently as long as
 * no writer holds the lock. It supports reentrancy and is implemented using
 * Redis operations for distributed locking.
 * </p>
 */
@Slf4j
public class RedisReadLock extends AbstractRedisLock {

    public static final String LOCK_MODE = "READ";
    public static final String READ_LOCK_TYPE = "RedisReadLock";

    /**
     * Constructs a RedisReadLock.
     *
     * @param redisLockOperations Service for Redis lock operations.
     * @param lockOwnerSupplier   Supplier for the lock owner ID.
     * @param properties          Configuration properties for Redis locks.
     * @param lockKey             The unique key for the parent ReadWriteLock in
     *                            Redis.
     * @param lockTtlMillis       Time-to-live for the lock in milliseconds.
     * @param retryIntervalMillis Interval between retry attempts in milliseconds.
     * @param maxRetries          Maximum number of retry attempts.
     */
    public RedisReadLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey, // This is the main ReadWriteLock key
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey,
                lockTtlMillis, retryIntervalMillis, maxRetries);
        // No additional initialization needed
    }

    @Override
    protected String getLockType() {
        return READ_LOCK_TYPE;
    }

    @Override
    protected CompletableFuture<Void> doLock(String ownerId, Duration effectiveTimeout) {
        log.debug("Attempting to acquire read lock (doLock): lockKey={}, ownerId={}, timeout={}", getLockKey(), ownerId,
                effectiveTimeout);
        // Note: Retry mechanism is handled by AbstractRedisLock
        return redisLockOperations.tryReadLock(
                getLockKey(),
                getResponseCacheKey(),
                getRequestUuid(),
                String.valueOf(effectiveTimeout.toMillis()),
                ownerId,
                String.valueOf(getResponseCacheTtlSeconds().toMillis()))
                .thenCompose(result -> {
                    if (result != null && result > 0) {
                        log.debug("Read lock acquired (doLock): lockKey={}, ownerId={}", getLockKey(), ownerId);
                        return updateLockState(ownerId).thenApply(v -> null);
                    } else {
                        log.debug("Failed to acquire read lock (doLock): lockKey={}, ownerId={}", getLockKey(),
                                ownerId);
                        return CompletableFuture.failedFuture(
                                new LockAcquisitionException(getLockKey(), getLockType(), ownerId, getRequestUuid(),
                                        null, "Failed to acquire read lock"));
                    }
                });
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        log.debug("Attempting to acquire read lock (doTryLock): lockKey={}, ownerId={}, timeout={}", getLockKey(),
                ownerId, effectiveTimeout);
        return redisLockOperations.tryReadLock(
                getLockKey(),
                getResponseCacheKey(),
                getRequestUuid(),
                String.valueOf(effectiveTimeout.toMillis()),
                ownerId,
                String.valueOf(getResponseCacheTtlSeconds().toMillis())).thenApply(result -> {
                    boolean acquired = result != null && result > 0;
                    log.debug("Read lock tryLock {}: lockKey={}, ownerId={}", acquired ? "successful" : "failed",
                            getLockKey(), ownerId);
                    return acquired;
                });
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        log.debug("Attempting to release read lock: lockKey={}, ownerId={}", getLockKey(), ownerId);
        return redisLockOperations.unlockReadLock(getLockKey(), ownerId, getRequestUuid())
                .thenAccept(result -> {
                    if ("OK".equals(result)) {
                        log.debug("Read lock released: lockKey={}, ownerId={}", getLockKey(), ownerId);
                    } else {
                        log.warn("Failed to release read lock: lockKey={}, ownerId={}, result={}", getLockKey(),
                                ownerId, result);
                        throw new LockReleaseException(getLockKey(), getLockType(), ownerId, getRequestUuid(),
                                ownerId, null, "Failed to release read lock");
                    }
                });
    }

    // Removed duplicate helper method - using parent class implementation

    @Override
    protected CompletableFuture<Void> updateLockState(String ownerId) {
        return CompletableFuture.allOf(
                redisLockOperations.hset(getLockKey(), "mode", "READ"),
                redisLockOperations.hset(getLockKey(), "owner", ownerId));
    }

    @Override
    public boolean isReadLock() {
        return true;
    }

    @Override
    public boolean isWriteLock() {
        return false;
    }

    // Removed duplicate helper methods
}
