# Redis Cluster Compatibility

This diagram illustrates how the Redis locking module ensures compatibility with Redis Cluster, focusing on key co-location strategies, hash tags, and cluster-aware operations.

```mermaid
classDiagram
    class RedisKeyBuilder {
        +buildLockKey(bucket, name) String
        +buildLockDataKey(bucket, name) String
        +buildLockLeaseKey(bucket, name) String
        +ensureHashTagsForCluster(key) String
        -applyHashTag(key) String
    }

    class ClusterCommandExecutor {
        +executeCommand(RedisConnection, String, Object[]) Object
        +executeScript(RedisConnection, String, List~RedisKey~, Object[]) Object
        +executeScriptInPipeline(RedisPipeline, String, List~RedisKey~, Object[]) Object
        -handleClusterRedirection(RedisConnection, Exception) Object
        -isClusterRedirectionException(Exception) boolean
    }

    class RedisLockProperties {
        +String keyPrefix
        +Map~String, LockBucketConfig~ buckets
        +getHashTagConfiguration() HashTagConfig
    }

    class HashTagConfig {
        +boolean enabled
        +String openBrace
        +String closeBrace
    }

    class LockBucketConfig {
        +String name
        +boolean useHashTags
        +getEffectiveHashTagConfig(globalConfig) HashTagConfig
    }

    class ScriptLoader {
        +loadScript(String) String
        -registerScript(String, String) String
        -handleClusterScriptLoading(Exception, String) String
    }

    class RedisLockOperations {
        -ClusterCommandExecutor commandExecutor
        -ScriptLoader scriptLoader
        -RedisKeyBuilder keyBuilder
        +tryLock(bucket, name, owner, leaseTime) CompletableFuture~Boolean~
        +unlock(bucket, name, owner) CompletableFuture~UnlockType~
        -executeScriptAcrossCluster(script, keys, args) Object
    }

    class AbstractRedisLock {
        #RedisLockOperations lockOperations
        #RedisKeyBuilder keyBuilder
        #String bucket
        #String name
        #ensureKeysColocated() void
    }

    %% Relationships
    RedisLockProperties --> HashTagConfig : contains
    RedisLockProperties --> LockBucketConfig : contains buckets
    LockBucketConfig --> HashTagConfig : uses
    RedisKeyBuilder --> HashTagConfig : uses for key generation
    RedisLockOperations --> ClusterCommandExecutor : uses
    RedisLockOperations --> ScriptLoader : uses
    RedisLockOperations --> RedisKeyBuilder : uses
    AbstractRedisLock --> RedisLockOperations : uses
    AbstractRedisLock --> RedisKeyBuilder : uses

    %% Notes
    class RedisKeyBuilder {
        <<Service>>
        Ensures keys use hash tags for co-location
    }
    class ClusterCommandExecutor {
        <<Service>>
        Handles cluster redirections and retries
    }
    class ScriptLoader {
        <<Service>>
        Manages script loading across cluster nodes
    }
    class RedisLockOperations {
        <<Service>>
        Executes Lua scripts with cluster awareness
    }
    class AbstractRedisLock {
        <<Abstract>>
        Base class for all lock implementations
    }
```

## Key Redis Cluster Compatibility Features

1. **Hash Tags for Key Co-location**
   - All related keys for a lock (lock key, data key, lease key) use hash tags to ensure they are stored on the same Redis Cluster node
   - Configurable at global and per-bucket levels through `HashTagConfig`

2. **Cluster-Aware Command Execution**
   - `ClusterCommandExecutor` handles cluster redirections (MOVED/ASK responses)
   - Automatically retries commands on the correct node when redirection occurs

3. **Script Loading Across Cluster**
   - `ScriptLoader` ensures Lua scripts are loaded on all relevant cluster nodes
   - Handles NOSCRIPT errors by reloading scripts when needed

4. **Bucket-Based Configuration**
   - Each lock bucket can have its own hash tag configuration
   - Allows for fine-grained control over key distribution in the cluster

5. **Key Structure**
   - Keys follow the pattern: `{prefix}:{bucket}:{name}` where the hash tag ensures co-location
   - The hash tag is typically applied to the bucket portion: `prefix:{bucket}:name`