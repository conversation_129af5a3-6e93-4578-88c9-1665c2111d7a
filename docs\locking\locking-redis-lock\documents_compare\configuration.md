# Modernized Redis Locking Module: Configuration

This document details how to configure the modernized Redis locking module using Spring Boot's `@ConfigurationProperties`. Proper configuration is essential for tailoring the module's behavior to specific application requirements and environments. **The configuration options and structure described here are mandatory and must be followed, as defined in the source document `lock-final/redis_lock_configuration_proposal.md` and implemented in the module.**

## Configuration Class

The primary class for configuring the Redis locking module is `RedisLockProperties`. This class is annotated with `@ConfigurationProperties` and typically bound to a prefix (e.g., `destilink.locking.redis`) in your application's configuration files (e.g., `application.yml`, `application.properties`).

## Key Configuration Properties

The `RedisLockProperties` class and its nested configuration classes expose various properties to control the module's behavior:

*   **`prefix`**: (String) A prefix applied to all Redis keys managed by the module to prevent collisions. (e.g., `destilink.locking.redis.prefix=my-app`)
*   **`watchdogEnabled`**: (boolean) Enables or disables the lock watchdog mechanism. (e.g., `destilink.locking.redis.watchdogEnabled=true`)
*   **`watchdogIntervalMs`**: (long) The interval at which the watchdog attempts to extend lock leases, in milliseconds.
*   **`unlockMessageListenerEnabled`**: (boolean) Enables or disables the Redis Pub/Sub unlock message listener. Disabling this will force the module to rely solely on the fallback re-polling mechanism.
*   **`lockOwnerSupplier`**: (Bean Name) Specifies the name of the Spring bean to use as the `LockOwnerSupplier` for generating unique lock owner identifiers.
*   **`exceptionMarkerProvider`**: (Bean Name) Specifies the name of the Spring bean to use as the `ExceptionMarkerProvider` for structured logging of exceptions.
*   **`buckets`**: (Map<String, LockBucketConfig>) A map defining different lock buckets with specific configurations. The map key is the bucket name.

### `LockBucketConfig` Properties

Each lock bucket defined in the `buckets` map can have its own configuration:

*   **`defaultLeaseTimeMs`**: (long) The default lease time (TTL) for locks acquired within this bucket, in milliseconds. This can be overridden when acquiring a lock.
*   **`retryIntervalMs`**: (long) The interval to wait before retrying lock acquisition after a failed attempt or a missed unlock notification, in milliseconds.
*   **`lockTypes`**: (Nested Configuration) Configuration specific to different lock types within the bucket (e.g., reentrant, state, readwrite).

#### Lock Type Specific Properties

Nested within `LockBucketConfig`, you can configure properties specific to each lock type:

*   **`reentrant`**: (Nested Configuration for Reentrant Locks)
    *   `reentrantEnabled`: (boolean) Enables or disables reentrancy for locks in this bucket.
*   **`state`**: (Nested Configuration for State Locks)
    *   `stateExpirationMs`: (long) The default expiration time for state keys, in milliseconds.
*   **`readwrite`**: (Nested Configuration for ReadWrite Locks)
    *   `readLeaseTimeMs`: (long) Default lease time for read locks.
    *   `writeLeaseTimeMs`: (long) Default lease time for write locks.

**Note:** The `lock()` method, which blocks indefinitely until the lock is acquired, does not use a configurable timeout. Its behavior aligns with the `java.util.concurrent.Lock` interface specification. Timeout behavior is provided by the `tryLock(long timeout, TimeUnit unit)` method, where the timeout is passed as an argument.

## Configuring Metrics

The module exposes metrics using Micrometer. Configuration for metrics reporting (e.g., pushing to a monitoring system) is typically handled at the application level through Spring Boot Actuator and Micrometer's configuration properties. Refer to the [Metrics Documentation](metrics.md) for details on the specific metrics exposed by the Redis locking module.

## Example Configuration

```yaml
destilink:
  locking:
    redis:
      prefix: my-application
      watchdogEnabled: true
      watchdogIntervalMs: 10000
      unlockMessageListenerEnabled: true
      lockOwnerSupplier: myLockOwnerSupplierBean
      exceptionMarkerProvider: myExceptionMarkerProviderBean
      buckets:
        default:
          defaultLeaseTimeMs: 30000
          retryIntervalMs: 500
          reentrant:
            reentrantEnabled: true
        state-locks:
          defaultLeaseTimeMs: 60000
          retryIntervalMs: 1000
          state:
            stateExpirationMs: 5000
        read-write-locks:
          defaultLeaseTimeMs: 20000
          retryIntervalMs: 300
          readwrite:
            readLeaseTimeMs: 15000
            writeLeaseTimeMs: 25000
```

This example demonstrates configuring the global properties and defining three different lock buckets (`default`, `state-locks`, and `read-write-locks`) with their respective settings.