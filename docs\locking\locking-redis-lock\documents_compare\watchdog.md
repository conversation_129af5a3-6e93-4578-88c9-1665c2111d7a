# Modernized Redis Locking Module: Watchdog

This document describes the lock watchdog mechanism in the modernized Redis Locking Module. The watchdog is a critical component for ensuring the reliability of long-held locks by automatically extending their lease time before they expire. **The watchdog mechanism described here is mandatory and must be understood for reliable lock usage, as defined in the source document `lock-final/redis-lock-improvements.md`.**

## Purpose of the Watchdog

In a distributed system, a client holding a lock might experience delays (e.g., due to garbage collection pauses, network issues, or long-running operations) that could cause the lock's lease time in Redis to expire prematurely. If the lock expires before the client is finished, another client could acquire the lock, leading to multiple clients holding the same lock simultaneously and potentially causing data corruption or inconsistent state.

The watchdog mechanism is designed to prevent this by automatically extending the lease time of a lock as long as the client holding the lock is still alive and active.

## How the Watchdog Works

The watchdog operates as a background task within the application using the locking module.

1.  **Registration**: When a client successfully acquires a lock, the lock instance registers itself with the `LockWatchdog` component.
2.  **Scheduling**: The watchdog schedules a task to extend the lease of the acquired lock. This task is typically scheduled to run before the lock's current lease time expires (e.g., after one-third of the lease time has elapsed).
3.  **Lease Extension**: When the scheduled task executes, it attempts to extend the lease of the lock in Redis. This operation is performed atomically using a Lua script (`extend_lock.lua`) to ensure that the lease is only extended if the client still holds the lock.
4.  **Rescheduling**: If the lease extension is successful, the watchdog reschedules the task for the same lock to run again before the new expiration time.
5.  **Cancellation**: When the client releases the lock, the lock instance unregisters itself from the watchdog, and the scheduled lease extension task is cancelled.

This process ensures that as long as a client holds a lock and the watchdog is active, the lock's lease in Redis will be periodically renewed, preventing it from expiring prematurely.

## Interaction with Reentrancy (Redis HashMaps)

For reentrant locks, which store the owner ID and reentrancy count in a Redis Hash, the watchdog extends the TTL of the Hash key. The `extend_lock.lua` script used by the watchdog verifies ownership by checking the 'owner' field in the Hash before extending the expiration time of the entire Hash key. This ensures that the lease is only extended for the correct owner.

## Configuration

The watchdog mechanism can be configured using properties in the `RedisLockProperties` class (see [Configuration](configuration.md)):

*   **`watchdogEnabled`**: (boolean) Enables or disables the watchdog. Default is typically `true`.
*   **`watchdogIntervalMs`**: (long) The interval at which the watchdog attempts to extend leases, in milliseconds. This should be less than the lock's lease time.

## Considerations

*   **Watchdog Failure**: If the watchdog itself fails or is unable to communicate with Redis, lock leases will not be extended, and locks may expire prematurely. Applications should have monitoring in place to detect watchdog issues.
*   **Application Shutdown**: The watchdog should be properly shut down when the application stops to avoid attempting to extend leases for locks that are no longer held. The `UnlockMessageListenerManager` and potentially other components handle graceful shutdown.
*   **Lease Time vs. Watchdog Interval**: The lock's lease time should be significantly longer than the watchdog interval to provide sufficient time for the watchdog to execute and extend the lease. A common pattern is to set the watchdog interval to one-third of the lease time.

The watchdog is a vital part of the modernized module's reliability, ensuring that acquired locks remain valid for the duration they are needed by the client.