<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>framework-dependencies-parent</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../framework-dependencies-parent/pom.xml</relativePath>
    </parent>
    <artifactId>framework-build-parent-cronjob</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>cronjob-parent</module>
        <module>cronjob-parent-legacy</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tui.destilink.framework</groupId>
                <artifactId>framework-bom</artifactId>
                <version>1.0.26-dli-6231-SNAPSHOT</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>cronjob-core</artifactId>
        </dependency>
        <!-- Tests -->
        <dependency>
            <groupId>com.tui.destilink.framework.test-support</groupId>
            <artifactId>test-core</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>cronjob-parent-jar-only</id>
            <activation>
                <property>
                    <name>packaging</name>
                    <value>jar</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>io.github.git-commit-id</groupId>
                        <artifactId>git-commit-id-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>