package com.tui.destilink.framework.locking.redis.lock.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import static org.assertj.core.api.Assertions.*;

/**
 * Unit tests for VirtualThreadContextUtils to verify MDC context propagation
 * to Virtual Threads works correctly.
 */
class VirtualThreadContextUtilsTest {

    private ExecutorService virtualThreadExecutor;

    @BeforeEach
    void setUp() {
        // Clear any existing MDC context
        MDC.clear();
        // Create Virtual Thread executor for testing
        virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
    }

    @AfterEach
    void tearDown() {
        // Clean up
        MDC.clear();
        if (virtualThreadExecutor != null) {
            virtualThreadExecutor.shutdown();
        }
    }

    @Test
    void withContext_Runnable_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        AtomicReference<String> capturedValue = new AtomicReference<>();
        CountDownLatch latch = new CountDownLatch(1);
        
        // When
        Runnable contextualTask = VirtualThreadContextUtils.withContext(() -> {
            capturedValue.set(MDC.get(testKey));
            latch.countDown();
        });
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        // Execute in separate thread
        new Thread(contextualTask).start();
        latch.await(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(capturedValue.get()).isEqualTo(testValue);
        assertThat(MDC.get(testKey)).isNull(); // Main thread context should be clear
    }

    @Test
    void withContext_Callable_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        // When
        Callable<String> contextualTask = VirtualThreadContextUtils.withContext((Callable<String>) () -> MDC.get(testKey));
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        // Execute in separate thread
        FutureTask<String> futureTask = new FutureTask<>(contextualTask);
        new Thread(futureTask).start();
        String result = futureTask.get(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(result).isEqualTo(testValue);
        assertThat(MDC.get(testKey)).isNull(); // Main thread context should be clear
    }

    @Test
    void withContext_Supplier_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        AtomicReference<String> capturedValue = new AtomicReference<>();
        CountDownLatch latch = new CountDownLatch(1);
        
        // When
        Runnable contextualTask = VirtualThreadContextUtils.withContext(() -> {
            Supplier<String> supplier = VirtualThreadContextUtils.withContext((Supplier<String>) () -> MDC.get(testKey));
            capturedValue.set(supplier.get());
            latch.countDown();
        });
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        // Execute in separate thread
        new Thread(contextualTask).start();
        latch.await(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(capturedValue.get()).isEqualTo(testValue);
    }

    @Test
    void withContext_shouldHandleNullContext() throws Exception {
        // Given - no MDC context set
        AtomicReference<Map<String, String>> capturedContext = new AtomicReference<>();
        CountDownLatch latch = new CountDownLatch(1);
        
        // When
        Runnable contextualTask = VirtualThreadContextUtils.withContext(() -> {
            capturedContext.set(MDC.getCopyOfContextMap());
            latch.countDown();
        });
        
        new Thread(contextualTask).start();
        latch.await(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(capturedContext.get()).isNull();
    }

    @Test
    void withContext_shouldRestorePreviousContext() throws Exception {
        // Given
        String testKey = "test.key";
        String originalValue = "original.value";
        String newValue = "new.value";
        
        AtomicReference<String> capturedOriginal = new AtomicReference<>();
        AtomicReference<String> capturedNew = new AtomicReference<>();
        CountDownLatch latch = new CountDownLatch(1);
        
        // When
        Runnable contextualTask = () -> {
            // Set original context in the thread
            MDC.put(testKey, originalValue);
            capturedOriginal.set(MDC.get(testKey));
            
            // Execute nested context
            MDC.put(testKey, newValue);
            Runnable nestedTask = VirtualThreadContextUtils.withContext(() -> {
                capturedNew.set(MDC.get(testKey));
            });
            nestedTask.run();
            
            // Verify original context is restored
            assertThat(MDC.get(testKey)).isEqualTo(originalValue);
            latch.countDown();
        };
        
        new Thread(contextualTask).start();
        latch.await(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(capturedOriginal.get()).isEqualTo(originalValue);
        assertThat(capturedNew.get()).isEqualTo(newValue);
    }

    @Test
    void executeAsync_Runnable_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        AtomicReference<String> capturedValue = new AtomicReference<>();
        
        // When
        CompletableFuture<Void> future = VirtualThreadContextUtils.executeAsync(virtualThreadExecutor, () -> {
            capturedValue.set(MDC.get(testKey));
        });
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        future.get(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(capturedValue.get()).isEqualTo(testValue);
        assertThat(MDC.get(testKey)).isNull(); // Main thread context should be clear
    }

    @Test
    void executeAsync_Callable_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        // When
        Supplier<String> supplier = () -> MDC.get(testKey);
        CompletableFuture<String> future = VirtualThreadContextUtils.executeAsync(virtualThreadExecutor, supplier);
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        String result = future.get(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(result).isEqualTo(testValue);
        assertThat(MDC.get(testKey)).isNull(); // Main thread context should be clear
    }

    @Test
    void executeAsync_Supplier_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        // When
        CompletableFuture<String> future = VirtualThreadContextUtils.executeAsync(virtualThreadExecutor, (Callable<String>) () -> MDC.get(testKey));
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        String result = future.get(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(result).isEqualTo(testValue);
        assertThat(MDC.get(testKey)).isNull(); // Main thread context should be clear
    }

    @Test
    void supplyAsync_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        // When
        CompletableFuture<String> future = VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> MDC.get(testKey));
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        String result = future.get(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(result).isEqualTo(testValue);
    }

    @Test
    void runAsync_shouldPropagateContext() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        AtomicReference<String> capturedValue = new AtomicReference<>();
        
        // When
        CompletableFuture<Void> future = VirtualThreadContextUtils.runAsync(virtualThreadExecutor, () -> {
            capturedValue.set(MDC.get(testKey));
        });
        
        // Clear main thread context to ensure isolation
        MDC.clear();
        
        future.get(1, TimeUnit.SECONDS);
        
        // Then
        assertThat(capturedValue.get()).isEqualTo(testValue);
    }

    @Test
    void captureContext_shouldReturnCurrentContext() {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        MDC.put(testKey, testValue);
        
        // When
        Map<String, String> capturedContext = VirtualThreadContextUtils.captureContext();
        
        // Then
        assertThat(capturedContext).containsEntry(testKey, testValue);
    }

    @Test
    void captureContext_shouldReturnNullWhenNoContext() {
        // Given - no MDC context
        
        // When
        Map<String, String> capturedContext = VirtualThreadContextUtils.captureContext();
        
        // Then
        assertThat(capturedContext).isNull();
    }

    @Test
    void restoreContext_shouldRestoreAndCleanup() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        String originalKey = "original.key";
        String originalValue = "original.value";
        
        MDC.put(originalKey, originalValue);
        
        Map<String, String> contextToRestore = Map.of(testKey, testValue);
        
        // When
        try (AutoCloseable scope = VirtualThreadContextUtils.restoreContext(contextToRestore)) {
            // Then - context should be restored
            assertThat(MDC.get(testKey)).isEqualTo(testValue);
            assertThat(MDC.get(originalKey)).isNull(); // Original context should be replaced
        }
        
        // After scope - original context should be restored
        assertThat(MDC.get(testKey)).isNull();
        assertThat(MDC.get(originalKey)).isEqualTo(originalValue);
    }

    @Test
    void restoreContext_shouldHandleNullContext() throws Exception {
        // Given
        String originalKey = "original.key";
        String originalValue = "original.value";
        MDC.put(originalKey, originalValue);
        
        // When
        try (AutoCloseable scope = VirtualThreadContextUtils.restoreContext(null)) {
            // Then - context should be cleared
            assertThat(MDC.getCopyOfContextMap()).isNull();
        }
        
        // After scope - original context should be restored
        assertThat(MDC.get(originalKey)).isEqualTo(originalValue);
    }

    @Test
    void contextIsolation_shouldMaintainSeparateContextsInConcurrentExecution() throws Exception {
        // Given
        int numberOfTasks = 10;
        CountDownLatch latch = new CountDownLatch(numberOfTasks);
        ConcurrentHashMap<String, String> results = new ConcurrentHashMap<>();
        
        // When - execute multiple tasks concurrently with different contexts
        for (int i = 0; i < numberOfTasks; i++) {
            final String taskId = "task-" + i;
            final String contextValue = "value-" + i;
            
            // Set unique context for each task
            MDC.put("taskId", taskId);
            MDC.put("contextValue", contextValue);
            
            VirtualThreadContextUtils.executeAsync(virtualThreadExecutor, () -> {
                // Capture context in Virtual Thread
                String capturedTaskId = MDC.get("taskId");
                String capturedValue = MDC.get("contextValue");
                
                // Simulate some work
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                results.put(capturedTaskId, capturedValue);
                latch.countDown();
            });
        }
        
        // Clear main thread context
        MDC.clear();
        
        // Wait for all tasks to complete
        latch.await(5, TimeUnit.SECONDS);
        
        // Then - each task should have maintained its own context
        assertThat(results).hasSize(numberOfTasks);
        for (int i = 0; i < numberOfTasks; i++) {
            String expectedTaskId = "task-" + i;
            String expectedValue = "value-" + i;
            assertThat(results).containsEntry(expectedTaskId, expectedValue);
        }
    }

    @Test
    void exceptionHandling_shouldMaintainContextCleanup() throws Exception {
        // Given
        String testKey = "test.key";
        String testValue = "test.value";
        String originalKey = "original.key";
        String originalValue = "original.value";
        
        MDC.put(originalKey, originalValue);
        MDC.put(testKey, testValue);
        
        AtomicReference<Exception> caughtException = new AtomicReference<>();
        
        // When - execute task that throws exception
        CompletableFuture<Void> future = VirtualThreadContextUtils.executeAsync(virtualThreadExecutor, (Runnable) () -> {
            // Verify context is propagated
            assertThat(MDC.get(testKey)).isEqualTo(testValue);
            // Throw exception
            throw new RuntimeException("Test exception");
        });
        
        try {
            future.get(1, TimeUnit.SECONDS);
        } catch (ExecutionException e) {
            caughtException.set((Exception) e.getCause());
        }
        
        // Then - exception should be caught and context should be maintained
        assertThat(caughtException.get()).isInstanceOf(RuntimeException.class);
        assertThat(caughtException.get().getMessage()).isEqualTo("Test exception");
        
        // Main thread context should still be intact
        assertThat(MDC.get(originalKey)).isEqualTo(originalValue);
        assertThat(MDC.get(testKey)).isEqualTo(testValue);
    }
}