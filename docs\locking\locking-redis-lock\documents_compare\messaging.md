# Modernized Redis Locking Module: Messaging

This document describes the messaging mechanism used by the modernized Redis locking module, specifically the utilization of Redis Publish/Subscribe (Pub/Sub) for unlock notifications. This mechanism is a key part of the non-polling lock acquisition strategy. **The messaging mechanism described here is mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md` and implemented in the module, particularly in `UnlockMessageListener.java` and `UnlockMessageListenerManager.java`.**

## Redis Publish/Subscribe (Pub/Sub)

Redis Pub/Sub is a messaging paradigm where senders (publishers) are not programmed to send their messages directly to specific receivers (subscribers). Instead, published messages are characterized into channels, without knowledge of what subscribers, if any, there may be. Similarly, subscribers express interest in one or more channels, and receive only messages that are of interest, without knowledge of what publishers, if any, there are.

In the context of the locking module:

*   **Publishers**: Threads that successfully release a lock.
*   **Channels**: Dynamically created channels based on the lock key (see [Redis Key Schema](redis_key_schema.md)).
*   **Subscribers**: `UnlockMessageListener` instances waiting for unlock notifications for specific lock keys.

## Unlock Notification Mechanism

When a thread releases a lock, it publishes a message to the corresponding unlock channel. This message typically contains information about the released lock, such as the lock key.

The `UnlockMessageListener` instances are subscribed to these channels. When a message is received, the listener identifies the lock key from the message content and signals the appropriate `LockSemaphoreHolder` (see [Lock Acquisition Mechanism](lock_acquisition.md)). This wakes up one or more threads that are waiting on that semaphore, allowing them to retry acquiring the lock.

## Components

### `UnlockMessageListener`

*   **Purpose:** Listens for messages on Redis Pub/Sub channels related to lock unlocks.
*   **Implementation:** Implements the `MessageListener` interface provided by the Redis client library. The `onMessage()` method is invoked when a message is received.
*   **Key Functionality:**
    *   Receives the unlock message.
    *   Parses the message to extract the lock key.
    *   Finds the corresponding `LockSemaphoreHolder` in the `waitingNotifications` map.
    *   Signals the semaphore to wake up waiting threads.
    *   Includes logic to calculate the number of permits to release based on the lock type and the number of waiting threads (`calculatePermitsToRelease()`).
    *   Uses an optimal executor for processing messages to avoid blocking the Redis client's message loop.
    *   **Note**: The listener's logic for identifying the lock and signaling the correct semaphore must be compatible with the different Redis key types used for locks, including Redis Strings and Redis Hashes (for reentrant locks).

### `UnlockMessageListenerManager`

*   **Purpose:** Manages the lifecycle of `UnlockMessageListener` instances.
*   **Implementation:** A Spring component responsible for creating and destroying listeners.
*   **Key Functionality:**
    *   Typically creates one `UnlockMessageListener` instance per lock bucket.
    *   Subscribes the listeners to the appropriate unlock channels when the application starts or when a new bucket is configured.
    *   Unsubscribes the listeners and manages their shutdown when the application stops (`@PreDestroy`).
    *   Uses `ObjectProvider` for dependency injection of components required by the listeners.

## Unlock Channel Key Schema

The Redis Pub/Sub channel names follow a specific schema to ensure messages are routed correctly. As described in the [Redis Key Schema](redis_key_schema.md) document, the channel name is typically:

```
<prefix>:__unlock_channels__:<bucket_name>:<lock_key>
```

This schema ensures that unlock messages for a specific lock instance within a specific bucket are published and received on a unique channel.

## Considerations

*   **Message Delivery Guarantees**: Redis Pub/Sub is a fire-and-forget messaging system. Messages are not guaranteed to be delivered to subscribers if the subscriber is not connected at the time the message is published. The locking module's fallback mechanism (re-polling after TTL expiration) is essential to handle potential missed unlock notifications.
*   **Message Content**: The content of the unlock message should be sufficient for the listener to identify the lock key and any other relevant information needed to signal waiting threads correctly.
*   **Executor**: Using an appropriate executor in the `UnlockMessageListener` is crucial to prevent the `onMessage()` method from blocking, which could disrupt the Redis client's ability to receive further messages.

The messaging mechanism, combined with the semaphore-based waiting, forms the core of the modernized, non-polling lock acquisition strategy, significantly improving performance and efficiency compared to the previous polling-based approach.