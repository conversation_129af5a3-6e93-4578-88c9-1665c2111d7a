{"folders": [{"path": "."}], "settings": {"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "maven.view": "hierarchical", "java.search.scope": "main", "promptLink.servers": [], "debug.javascript.defaultRuntimeExecutable": {"pwa-node": "c:\\users\\<USER>\\appdata\\local\\mise\\shims\\node"}, "java.dependency.packagePresentation": "hierarchical", "java.project.explorer.showNonJavaResources": false, "java.jdt.ls.java.home": "C:\\Users\\<USER>\\AppData\\Local\\mise\\installs\\java\\corretto-********.1", "java.import.gradle.java.home": "C:\\Users\\<USER>\\AppData\\Local\\mise\\installs\\java\\corretto-********.1", "python.defaultInterpreterPath": "C:\\Users\\<USER>\\AppData\\Local\\mise\\installs\\python\\3.11.11\\python.exe", "jira-plugin.workingProject": ""}}