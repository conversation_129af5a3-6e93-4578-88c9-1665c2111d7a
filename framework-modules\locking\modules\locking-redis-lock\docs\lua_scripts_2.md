# Redis Locking Module: Final Lua Scripts Documentation

## 1. Introduction

The `locking-redis-lock` module relies heavily on Lua scripts executed on the Redis server to ensure atomicity for complex lock operations. These scripts are crucial for the correctness and consistency of the distributed locks. All Lua scripts are loaded and cached by the `<PERSON>ript<PERSON>oa<PERSON>` bean at application startup, and `RedisLockOperations` executes them.

**Redis Cluster Compatibility**: All Redis keys in these scripts that store data for a specific lock instance (e.g., main lock, state, response cache) are expected to use the specific lock identifier (`<lockName>`) within curly braces `{<lockName>}` to act as a hash tag. This tag is appended to a base key structure including prefix and bucket (e.g., `<prefix>:<bucketName>:<namespace>:{<lockName>}`). Pub/Sub channel patterns for listeners like `<prefix>:<bucketName>:__unlock_channels__:*` are handled by the client, while scripts publish to specific channels like `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`.

## 2. Core Lock Operations Scripts

### 2.1. `try_lock.lua`

*   **Purpose**: Atomically attempts to acquire a standard or reentrant lock, with idempotency support.
*   **KEYS**:
    1.  `lockName`: The main key for the lock (e.g., `prefix:bucket:__locks__:{myLock}`).
    2.  `responseCacheKey`: The fully constructed key for the response cache (e.g., `prefix:bucket:__resp_cache__:{myLock}:requestUuid`), passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Unique ID for request idempotency.
    2.  `leaseTimeMs`: Lock expiration time in milliseconds.
    3.  `lockOwnerId`: Identifier of the client attempting to acquire the lock.
    4.  `reentrancyEnabled`: String "true" or "false" indicating if reentrancy is enabled for this lock type.
*   **Logic**:
    1.  Checks the `responseCacheKey` (KEYS[2]) for a cached response. If found, returns it.
    2.  If the `lockName` does not exist:
        *   Creates the lock by `HSET`ting `owner` to `lockOwnerId`.
        *   If `reentrancyEnabled` is "true", `HSET`s `count` to `1`.
        *   Sets `PEXPIRE` on `lockName` to `leaseTimeMs`.
        *   Result is `nil` (indicating lock acquired).
    3.  If `lockName` exists:
        *   Retrieves current `owner` using `HGET`.
        *   If `owner` matches `lockOwnerId` AND `reentrancyEnabled` is "true":
            *   Increments reentrant `count` using `HINCRBY`.
            *   Resets `PEXPIRE` on `lockName` to `leaseTimeMs`.
            *   Result is `nil` (lock acquired reentrantly).
        *   Else (lock held by another, or not reentrant for this owner):
            *   Result is the `PTTL` of `lockName` (as a string).
    4.  Caches the `result` (or "nil" string for nil results) in the `responseCacheKey` (KEYS[2]) with a short TTL (e.g., 300 seconds).
*   **Returns**: `nil` if the lock was acquired (or re-acquired). The remaining TTL (in milliseconds, as a string) of the lock if it's held by another owner. Cached response if available.

### 2.2. `unlock.lua`

*   **Purpose**: Atomically releases a standard or reentrant lock, with idempotency and Pub/Sub notification.
*   **KEYS**:
    1.  `lockName`: The main key for the lock (e.g., `prefix:bucket:__locks__:{myLock}`). This is used by the script to derive the specific lock instance part of the channel name.
    2.  `unlockChannelBaseName`: Base part of the unlock channel name (e.g., `<prefix>:<bucketName>:__unlock_channels__`). The script appends `:{lockNameFromKeys1}`.
    3.  `responseCacheKey`: The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Unique ID for request idempotency.
    2.  `lockOwnerId`: Identifier of the client attempting to release the lock.
    3.  `decrementReentrant`: String "true" or "false". If "false", forces full unlock even if reentrant.
*   **Logic**:
    1.  Checks `responseCacheKey` (KEYS[3]) for a cached response. If found, returns it.
    2.  If `lockName` does not `HEXISTS` or its `owner` field does not match `lockOwnerId`, result is "0".
    3.  Else (owner matches):
        *   Retrieves reentrant `count` using `HGET`.
        *   If `count` is not present (non-reentrant lock), or `count` is "1", or `decrementReentrant` is "false":
            *   Deletes `lockName` using `DEL`.
            *   Determines appropriate `UnlockType` (e.g., "REENTRANT_FULLY_RELEASED" or "NON_REENTRANT_RELEASED").
            *   Publishes a message containing *only* this `UnlockType` to the specific lock's unlock channel. The channel is constructed by appending the lock identifier (extracted from `KEYS[1]`, e.g., the `{myLock}` part) to `KEYS[2]` (the `unlockChannelBaseName`). Example constructed channel: `<prefix>:<bucketName>:__unlock_channels__:{myLock}`.
            *   Result is "1".
        *   Else (reentrant lock and count > 1 and decrementReentrant is "true"):
            *   Decrements `count` using `HINCRBY`.
            *   Result is "0" (still held reentrantly).
    4.  Caches the `result` in `responseCacheKey` (KEYS[3]).
*   **Returns**: "1" if the lock was fully released. "0" if the lock was not held by the caller, did not exist, or was only partially released (reentrancy count decremented but not to zero). Cached response if available.

### 2.3. `extend_lock.lua`

*   **Purpose**: Atomically extends the lease (TTL) of an existing lock if the caller is the current owner, with idempotency. Used by `LockWatchdog` and for reentrant lock lease extensions.
*   **KEYS**:
    1.  `lockName`: The main key for the lock.
    2.  `responseCacheKey`: The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Unique ID for request idempotency.
    2.  `newLeaseTimeMs`: The new lease time in milliseconds. For `LockWatchdog` calls, this **MUST** be the `watchdogMaxTtl`.
    3.  `lockOwnerId`: The expected owner of the lock.
*   **Logic**:
    1.  Checks `responseCacheKey` (KEYS[2]) for a cached response. If found, returns it.
    2.  If `lockName` does not `HEXISTS` or its `owner` field (retrieved via `HGET`) does not match `lockOwnerId`, result is "0".
    3.  Else (owner matches):
        *   Sets `PEXPIRE` on `lockName` to `newLeaseTimeMs`.
        *   Result is "1".
    4.  Caches the `result` in `responseCacheKey` (KEYS[2]).
*   **Returns**: "1" if the lease was successfully extended. "0" otherwise (e.g., lock not found or owner mismatch). Cached response if available.

## 3. StateLock Specific Scripts

### 3.1. `try_state_lock.lua`

*   **Purpose**: Atomically attempts to acquire a state-based lock, checking an associated state key, with idempotency and optional state initialization.
*   **KEYS**:
    1.  `lockName`: The main key for the lock.
    2.  `responseCacheKey`: The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `leaseTimeMs`: Lock expiration.
    3.  `lockOwnerId`: Client identifier.
    4.  `expectedState`: The required state value.
    5.  `stateKeySuffix`: Suffix for the state key (e.g., ":state").
    6.  `initIfNotExist`: String "true" to initialize state if it doesn't exist, "false" otherwise.
    7.  `stateExpirationMs`: Optional TTL for the state key (string "nil" if not set).
*   **Logic**:
    1.  Checks `responseCacheKey` (KEYS[2]) for a cached response. If found, returns it.
    2.  Constructs `stateKey` (`lockName .. stateKeySuffix`).
    3.  Gets `currentState` from `stateKey`.
    4.  If `currentState` is `nil`:
        *   If `initIfNotExist` is "true", sets `stateKey` to `expectedState` (and `PEXPIRE` if `stateExpirationMs` is provided). `currentState` becomes `expectedState`.
        *   Else, result is "-3" (State Not Found).
    5.  If result is already set (e.g., from step 4), cache and return.
    6.  If `currentState` does not match `expectedState`, result is "-1" (State Mismatch).
    7.  Else (state matches): Attempts lock acquisition similar to `try_lock.lua` (using HASH for owner/count).
        *   If acquired, result is `nil`.
        *   If held by another, result is `PTTL` of `lockName`.
    8.  Caches the `result` in `responseCacheKey` (KEYS[2]).
*   **Returns**: `nil` (lock acquired), `PTTL` (lock held by another), "-1" (state mismatch), "-3" (state not found and not initialized), or cached response.

### 3.2. `update_state.lua`

*   **Purpose**: Atomically updates the value of a state key, but only if the caller holds the main lock, with idempotency.
*   **KEYS**:
    1.  `lockName`: The main key for the lock.
    2.  `responseCacheKey`: The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `lockOwnerId`: Expected owner of the main lock.
    3.  `newState`: The new value for the state key.
    4.  `stateKeySuffix`: Suffix for the state key.
    5.  `stateExpirationMs`: Optional TTL for the state key (string "nil" if not set).
*   **Logic**:
    1.  Checks `responseCacheKey` (KEYS[2]) for a cached response. If found, returns it.
    2.  Verifies ownership of `lockName` (via `HGET owner`).
    3.  If owner matches:
        *   Constructs `stateKey` (`lockName .. stateKeySuffix`).
        *   Sets `stateKey` to `newState`.
        *   If `stateExpirationMs` is provided, sets `PEXPIRE` on `stateKey`.
        *   Result is "1".
    4.  Else, result is "0".
    5.  Caches the `result` in `responseCacheKey` (KEYS[2]).
*   **Returns**: "1" if state updated successfully. "0" if not lock owner or lock doesn't exist. Cached response if available.

### 3.3. `unlock_state_lock.lua`

*   **Purpose**: Atomically releases a `RedisStateLock`, updates the state, with idempotency and Pub/Sub.
*   **KEYS**:
    1.  `lockName`: The main lock key.
    2.  `unlockChannelBaseName`: Base part of the unlock channel name (e.g., `<prefix>:<bucketName>:__unlock_channels__`). The script appends `:{lockNameFromKeys1}`.
    3.  `responseCacheKey`: The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `lockOwnerId`: Expected owner of the lock.
    3.  `newStateOnUnlock`: The state to set the associated state key to upon unlock.
    4.  `stateKeySuffix`: Suffix for the state key.
    5.  `stateExpirationMs`: Optional TTL for the state key after update (string "nil" if not set).
*   **Logic**:
    1.  Checks `responseCacheKey` (KEYS[3]) for a cached response. If found, returns it.
    2.  Verifies ownership of `lockName` and reentrancy count (similar to `unlock.lua`).
    3.  If full unlock occurs (count reaches zero or not reentrant):
        *   Deletes `lockName`.
        *   Constructs `stateKey` (`lockName .. stateKeySuffix`).
        *   Sets `stateKey` to `newStateOnUnlock`.
        *   If `stateExpirationMs` is provided, sets `PEXPIRE` on `stateKey`.
        *   Determines appropriate `UnlockType` (e.g., "STATE_LOCK_RELEASED_STATE_UPDATED" or "STATE_LOCK_RELEASED_STATE_UNCHANGED" based on whether `newStateOnUnlock` differs from previous state, if known to script).
        *   Publishes a message containing *only* this `UnlockType` to the specific lock's unlock channel. The channel is constructed by appending the lock identifier (extracted from `KEYS[1]`) to `KEYS[2]` (the `unlockChannelBaseName`). Example constructed channel: `<prefix>:<bucketName>:__unlock_channels__:{myLock}`.
        *   Result is "1".
    4.  Else (reentrancy count decremented but > 0), result is "0".
    5.  Caches the `result` in `responseCacheKey` (KEYS[3]).
*   **Returns**: "1" if fully unlocked and state updated. "0" otherwise. Cached response if available.

## 4. ReadWriteLock Scripts

These scripts are used by `RedisReadWriteLock` to manage shared read access and exclusive write access. They operate on the main `lockName` which internally uses Redis Hash fields like `write_owner_id`, `write_reentrancy_count`, and `readers` (e.g., a counter or set of reader IDs/counts).

### 4.1. `try_read_lock.lua` (Adapted from Redisson Logic)

*   **Purpose**: Atomically attempts to acquire a read lock. It succeeds if no write lock is held by a different owner. Multiple readers (or the same reader reentrantly) can hold the lock. Manages individual TTLs for each reentrant read lock acquisition and updates the main lock hash's TTL.
*   **KEYS**:
    1.  `mainLockKey`: The main Redis Hash key for the `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__locks__:{myRWLock}`).
    2.  `readWriteTimeoutNamePrefix`: Base path for constructing individual read lock timeout keys for the current thread/reader (e.g., `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerIdFromArgv>`). The script appends `:<reentrantCount>`.
*   **ARGV**:
    1.  `leaseTimeMs`: Desired lease time in milliseconds for this specific read lock instance.
    2.  `readerId`: Current thread/reader's unique lock name (e.g., `getLockName(threadId)` from client). This will be used as a field name in `KEYS[1]`.
    3.  `writerId`: Current thread/reader's unique *write* lock name (e.g., `getWriteLockName(threadId)` from client). Used to check if the current reader already holds the write lock (allowing reentrant read).
*   **Logic (Adapted from Redisson `tryLockInnerAsync` for read locks)**:
    ```lua
    -- KEYS[1] mainLockKey (hash)
    -- KEYS[2] readWriteTimeoutNamePrefix (base for individual read timeout keys for this reader)
    -- ARGV[1] leaseTimeMs
    -- ARGV[2] readerId (current thread's read lock name)
    -- ARGV[3] writerId (current thread's write lock name, to check if it holds the write lock)
    
    local mode = redis.call('hget', KEYS[1], 'mode');
    if (mode == false) then
        redis.call('hset', KEYS[1], 'mode', 'read');
        redis.call('hset', KEYS[1], ARGV[2], 1); -- Store readerId as field, count as value
        redis.call('set', KEYS[2] .. ':1', 1, 'px', ARGV[1]); -- Individual timeout key for this first instance
        redis.call('pexpire', KEYS[1], ARGV[1]); -- Expire main hash
        return nil; -- Lock acquired
    end;
    
    if (mode == 'read') or (mode == 'write' and redis.call('hexists', KEYS[1], ARGV[3]) == 1) then
        local ind = redis.call('hincrby', KEYS[1], ARGV[2], 1); -- Increment reentrant count for this reader
        local individualTimeoutKey = KEYS[2] .. ':' .. ind;
        redis.call('set', individualTimeoutKey, 1, 'px', ARGV[1]); -- Set TTL for this specific reentrant acquisition
        
        local mainKeyCurrentPttl = redis.call('pttl', KEYS[1]);
        if mainKeyCurrentPttl < 0 then mainKeyCurrentPttl = 0 end; -- Treat expired or non-existent as 0 for max
        redis.call('pexpire', KEYS[1], math.max(mainKeyCurrentPttl, ARGV[1])); -- Ensure main hash lives at least as long as this new read
        return nil; -- Lock acquired reentrantly or by write lock holder
    end;
    
    return redis.call('pttl', KEYS[1]); -- Write lock held by another, return its PTTL
    ```
*   **Returns**: `nil` if the read lock was acquired (or re-acquired by the same reader/write-lock-holder). The remaining PTTL (in milliseconds, as a number) of the main lock key if a conflicting write lock is held by another.

### 4.2. `unlock_read_lock.lua` (Adapted from Redisson Logic)

*   **Purpose**: Atomically releases a reentrant read lock instance. It decrements the reader's reentrant count in the main hash, deletes the corresponding individual read lock timeout key, and recalculates the main lock hash's TTL based on other active readers' timeout keys. If no active components (readers or writer) remain, the main lock hash is deleted. Publishes an unlock message.
*   **KEYS**:
    1.  `mainLockKey`: The main Redis Hash key for the `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__locks__:{myRWLock}`).
    2.  `unlockChannelBaseName`: Base for the Pub/Sub channel name. The script will extract the `{<lockName>}` part from `KEYS[1]` to append to this.
    3.  `timeoutPrefixForCurrentReaderInstance`: The exact prefix for the *current reader's* individual timeout keys. For example, `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerIdFromArgv>`. The script appends `:<releasingReentrantCount>`.
    4.  `generalTimeoutKeyPrefixForAllReaders`: The prefix used to iterate and find *all* individual read lock timeout keys for *all* readers of this `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}`). The script appends `:<anyReaderIdField>:<count>`.
    5.  `responseCacheKey`: (Optional) Key for script idempotency.
*   **ARGV**:
    1.  `requestUuid`: (Used if `KEYS[5]` is provided) Unique ID for request idempotency.
    2.  `readerId`: Unique identifier (field name in `KEYS[1]`) for the reader releasing the lock.
    3.  `publishCommand`: The Redis command for publish (e.g., "PUBLISH").
    4.  `unlockMessagePayload`: The `UnlockType` string to publish.
    5.  `responseCacheTTLSeconds`: (Used if `KEYS[5]` is provided) TTL for the response cache.
    6.  `writerHolderFieldNameInMainHash`: The field name in `KEYS[1]` that stores the writer's ID (e.g., "write_owner_id").
    7.  `modeFieldNameInMainHash`: The field name in `KEYS[1]` that stores the mode (e.g., "mode").
*   **Logic (Adapted from Redisson `unlockInnerAsync` for read locks, using responseCacheKey)**:
    ```lua
    -- KEYS[1] mainLockKey
    -- KEYS[2] unlockChannelBaseName
    -- KEYS[3] timeoutPrefixForCurrentReaderInstance (e.g. ...:{lockName}:thisReaderId)
    -- KEYS[4] generalTimeoutKeyPrefixForAllReaders (e.g. ...:{lockName})
    -- KEYS[5] responseCacheKey (optional)
    
    -- ARGV[1] requestUuid
    -- ARGV[2] readerId
    -- ARGV[3] publishCommand
    -- ARGV[4] unlockMessagePayload
    -- ARGV[5] responseCacheTTLSeconds (optional)
    -- ARGV[6] writerHolderFieldNameInMainHash
    -- ARGV[7] modeFieldNameInMainHash
    
    if ARGV[5] ~= nil and KEYS[5] ~= nil and redis.call('exists', KEYS[5]) == 1 then
        local cachedValue = redis.call('get', KEYS[5]);
        if cachedValue ~= false then return tonumber(cachedValue); end
    end;
    
    local mode = redis.call('hget', KEYS[1], ARGV[7]);
    if (mode == false) then -- Main lock key itself doesn't exist
        -- Attempt to extract lockName from KEYS[1] for channel construction
        local key1_parts = {}
        for part in string.gmatch(KEYS[1], "[^:]+") do table.insert(key1_parts, part) end
        local lockNameSuffix = key1_parts[#key1_parts] 
        redis.call(ARGV[3], KEYS[2] .. ':' .. lockNameSuffix, ARGV[4]);
        if ARGV[5] ~= nil and KEYS[5] ~= nil then redis.call('set', KEYS[5], '1', 'px', ARGV[5]); end;
        return 1;
    end;
    
    local currentReentrantCount = tonumber(redis.call('hget', KEYS[1], ARGV[2]));
    if (currentReentrantCount == nil or currentReentrantCount == 0) then
        if ARGV[5] ~= nil and KEYS[5] ~= nil then redis.call('set', KEYS[5], '0', 'px', ARGV[5]); end;
        return 0; -- Not held by this readerId or count error
    end;
    
    -- Delete the individual timeout key for the specific reentrant instance being released
    redis.call('del', KEYS[3] .. ':' .. currentReentrantCount); 
    
    local newReentrantCount = redis.call('hincrby', KEYS[1], ARGV[2], -1);
    if (newReentrantCount == 0) then
        redis.call('hdel', KEYS[1], ARGV[2]); -- Remove reader field from hash
    end;
    
    local maxRemainTime = -3; 
    local activeElements = false;
    
    -- Check if writer holds the lock
    if redis.call('hexists', KEYS[1], ARGV[6]) == 1 then
        activeElements = true;
        maxRemainTime = redis.call('pttl', KEYS[1]); -- Writer active, main key TTL is authoritative for now
    else
        -- No writer, check other readers
        local allFields = redis.call('hkeys', KEYS[1]);
        for i, fieldName in ipairs(allFields) do
            if fieldName ~= ARGV[7] /*modeFieldName*/ and fieldName ~= ARGV[6] /*writerHolderFieldName*/ then
                local readerCount = tonumber(redis.call('hget', KEYS[1], fieldName));
                if readerCount ~= nil and readerCount > 0 then
                    activeElements = true;
                    for k=1, readerCount do
                        -- Construct general timeout key for this other reader's instance
                        local otherReaderTimeoutKey = KEYS[4] .. ':' .. fieldName .. ':' .. k; 
                        local pttl = redis.call('pttl', otherReaderTimeoutKey);
                        if pttl > maxRemainTime then
                            maxRemainTime = pttl;
                        end;
                    end;
                end;
            end;
        end;
    end;
    
    if not activeElements then
        redis.call('del', KEYS[1]); -- Delete main lock hash
    elseif maxRemainTime > 0 then
        redis.call('pexpire', KEYS[1], maxRemainTime);
    elseif redis.call('pttl', KEYS[1]) < 0 then -- No positive PTTL from active elements, ensure cleanup
         redis.call('del', KEYS[1]);
    end;
    
    -- Publish unlock message
    local key1_parts_pub = {}
    for part_pub in string.gmatch(KEYS[1], "[^:]+") do table.insert(key1_parts_pub, part_pub) end
    local lockNameSuffix_pub = key1_parts_pub[#key1_parts_pub] 
    redis.call(ARGV[3], KEYS[2] .. ':' .. lockNameSuffix_pub, ARGV[4]);
    
    if ARGV[5] ~= nil and KEYS[5] ~= nil then redis.call('set', KEYS[5], '1', 'px', ARGV[5]); end;
    return 1;
    ```
*   **Returns**: "1" if this read lock instance was successfully released. "0" if not held by this reader or an error occurred. Returns cached response if available.

### 4.3. `try_write_lock.lua` (Adapted from Redisson Logic)

*   **Purpose**: Atomically attempts to acquire an exclusive write lock. Fails if any read locks are active or if the write lock is held by a different owner. Supports reentrancy for the same writer.
*   **KEYS**:
    1.  `mainLockKey`: The main Redis Hash key for the `RedisReadWriteLock`.
    2.  `responseCacheKey`: (Optional) Key for script idempotency.
*   **ARGV**:
    1.  `requestUuid`: (Used if `KEYS[2]` is provided) Unique ID for request idempotency.
    2.  `leaseTimeMs`: Desired lease time in milliseconds for the write lock.
    3.  `writerId`: Current thread/writer's unique lock name (e.g., `getLockName(threadId)` from client). This is used as the field name for the writer's reentrancy count in `KEYS[1]`.
    4.  `responseCacheTTLSeconds`: (Used if `KEYS[2]` is provided) TTL for the response cache.
    5.  `modeFieldNameInMainHash`: The field name in `KEYS[1]` that stores the mode (e.g., "mode").
*   **Logic (Adapted from Redisson `tryLockInnerAsync` for write locks, using responseCacheKey)**:
    ```lua
    -- KEYS[1] mainLockKey (hash)
    -- KEYS[2] responseCacheKey (optional)
    
    -- ARGV[1] requestUuid
    -- ARGV[2] leaseTimeMs
    -- ARGV[3] writerId (current thread's write lock name)
    -- ARGV[4] responseCacheTTLSeconds (optional)
    -- ARGV[5] modeFieldNameInMainHash

    if ARGV[4] ~= nil and KEYS[2] ~= nil and redis.call('exists', KEYS[2]) == 1 then
        local cachedValue = redis.call('get', KEYS[2]);
        if cachedValue ~= false then
          if cachedValue == 'nil' then return nil else return tonumber(cachedValue) end;
        end;
    end;

    local mode = redis.call('hget', KEYS[1], ARGV[5]);
    if (mode == false) then -- No lock exists, acquire write lock
        redis.call('hset', KEYS[1], ARGV[5], 'write');
        redis.call('hset', KEYS[1], ARGV[3], 1); -- Store writerId as field, count as value
        redis.call('pexpire', KEYS[1], ARGV[2]);
        if ARGV[4] ~= nil and KEYS[2] ~= nil then redis.call('set', KEYS[2], 'nil', 'px', ARGV[4]); end;
        return nil; -- Lock acquired
    end;
    
    if (mode == 'write') then
        if (redis.call('hexists', KEYS[1], ARGV[3]) == 1) then -- Reentrant write lock by same writer
            redis.call('hincrby', KEYS[1], ARGV[3], 1);
            local currentExpire = redis.call('pttl', KEYS[1]);
            if currentExpire < 0 then currentExpire = 0 end; -- Treat -1 (no expire) or -2 (no key) as 0 for addition safety, though key should exist.
            redis.call('pexpire', KEYS[1], currentExpire + ARGV[2]); -- Extend lease by adding new leaseTime
            if ARGV[4] ~= nil and KEYS[2] ~= nil then redis.call('set', KEYS[2], 'nil', 'px', ARGV[4]); end;
            return nil; -- Lock acquired reentrantly
        end;
    end;
    
    -- Lock is in 'read' mode, or 'write' mode by another writer
    local pttl = redis.call('pttl', KEYS[1]);
    if ARGV[4] ~= nil and KEYS[2] ~= nil then redis.call('set', KEYS[2], pttl, 'px', ARGV[4]); end;
    return pttl;
    ```
*   **Returns**: `nil` if the write lock was acquired (or re-acquired by the same writer). The remaining PTTL (in milliseconds, as a number) of the main lock key if it's held in read mode by any readers or in write mode by a different writer. Returns cached response if available.

### 4.4. `unlock_write_lock.lua` (Adapted from Redisson Logic)

*   **Purpose**: Atomically releases an exclusive write lock. Supports reentrancy. If this is the final release, it checks for remaining readers and may change the lock mode to 'read' or delete the main lock hash if no readers exist. Publishes an unlock message.
*   **KEYS**:
    1.  `mainLockKey`: The main Redis Hash key for the `RedisReadWriteLock`.
    2.  `unlockChannelBaseName`: Base for the Pub/Sub channel name.
    3.  `responseCacheKey`: (Optional) Key for script idempotency.
    4.  `generalTimeoutKeyPrefixForAllReaders`: The prefix used to iterate *all* individual read lock timeout keys for *all* readers of this `RedisReadWriteLock` (e.g., `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}`). Used when transitioning from write to read mode.
*   **ARGV**:
    1.  `requestUuid`: (Used if `KEYS[3]` is provided) Unique ID for request idempotency.
    2.  `internalLockLeaseTime`: Default lease time for the lock, used if transitioning to read mode and readers exist.
    3.  `writerId`: Current thread/writer's unique lock name.
    4.  `publishCommand`: The Redis command for publish.
    5.  `unlockMessagePayload`: The `UnlockType` string to publish (e.g., "RW_WRITE_RELEASED_WAKEN_ALL").
    6.  `responseCacheTTLSeconds`: (Used if `KEYS[3]` is provided) TTL for the response cache.
    7.  `modeFieldNameInMainHash`: The field name in `KEYS[1]` that stores the mode.
    8.  `writerHolderFieldNameInMainHash`: The field name in `KEYS[1]` used to store the writer's ID (which is `writerId` itself).
*   **Logic (Adapted from Redisson `unlockInnerAsync` for write locks, using responseCacheKey)**:
    ```lua
    -- KEYS[1] mainLockKey
    -- KEYS[2] unlockChannelBaseName
    -- KEYS[3] responseCacheKey (optional)
    -- KEYS[4] generalTimeoutKeyPrefixForAllReaders
    
    -- ARGV[1] requestUuid
    -- ARGV[2] internalLockLeaseTime (used if transitioning to read mode)
    -- ARGV[3] writerId
    -- ARGV[4] publishCommand
    -- ARGV[5] unlockMessagePayload
    -- ARGV[6] responseCacheTTLSeconds (optional)
    -- ARGV[7] modeFieldNameInMainHash
    -- ARGV[8] writerHolderFieldNameInMainHash (this is ARGV[3] itself)

    if ARGV[6] ~= nil and KEYS[3] ~= nil and redis.call('exists', KEYS[3]) == 1 then
        local cachedValue = redis.call('get', KEYS[3]);
        if cachedValue ~= false then return tonumber(cachedValue); end
    end;

    local mode = redis.call('hget', KEYS[1], ARGV[7]);
    if (mode == false or mode ~= 'write' or redis.call('hexists', KEYS[1], ARGV[3]) == 0) then
        if ARGV[6] ~= nil and KEYS[3] ~= nil then redis.call('set', KEYS[3], '0', 'px', ARGV[6]); end;
        return 0; -- Not in write mode or not held by this writerId
    end;
    
    local newReentrantCount = redis.call('hincrby', KEYS[1], ARGV[3], -1);
    if (newReentrantCount > 0) then
        -- Still held reentrantly, just extend lease by internalLockLeaseTime
        local currentExpire = redis.call('pttl', KEYS[1]);
        if currentExpire < 0 then currentExpire = 0 end;
        redis.call('pexpire', KEYS[1], currentExpire + ARGV[2]);
        if ARGV[6] ~= nil and KEYS[3] ~= nil then redis.call('set', KEYS[3], '0', 'px', ARGV[6]); end;
        return 0; -- Partially released
    end;
    
    -- Fully releasing the write lock
    redis.call('hdel', KEYS[1], ARGV[3]); -- Remove writerId field
    
    -- Check for remaining readers to determine next state of mainLockKey
    local maxRemainTime = -3;
    local activeReaders = false;
    local allFields = redis.call('hkeys', KEYS[1]);
    for i, fieldName in ipairs(allFields) do
        if fieldName ~= ARGV[7] /*modeFieldName*/ and fieldName ~= ARGV[3] /*this writerId*/ then
            local readerCount = tonumber(redis.call('hget', KEYS[1], fieldName));
            if readerCount ~= nil and readerCount > 0 then
                activeReaders = true;
                for k=1, readerCount do
                    local individualTimeoutKey = KEYS[4] .. ':' .. fieldName .. ':' .. k;
                    local pttl = redis.call('pttl', individualTimeoutKey);
                    if pttl > maxRemainTime then
                        maxRemainTime = pttl;
                    end;
                end;
            end;
        end;
    end;
    
    if activeReaders and maxRemainTime > 0 then
        redis.call('hset', KEYS[1], ARGV[7], 'read'); -- Change mode to read
        redis.call('pexpire', KEYS[1], maxRemainTime);
    else
        redis.call('del', KEYS[1]); -- No readers left, delete main key
    end;
    
    -- Publish unlock message
    local key1_parts_pub = {}
    for part_pub in string.gmatch(KEYS[1], "[^:]+") do table.insert(key1_parts_pub, part_pub) end
    local lockNameSuffix_pub = key1_parts_pub[#key1_parts_pub]
    redis.call(ARGV[4], KEYS[2] .. ':' .. lockNameSuffix_pub, ARGV[5]);
    
    if ARGV[6] ~= nil and KEYS[3] ~= nil then redis.call('set', KEYS[3], '1', 'px', ARGV[6]); end;
    return 1; -- Fully released
    ```
*   **Returns**: "1" if the write lock was fully released. "0" if it was a partial reentrant release or if the lock was not held by the caller. Returns cached response if available.

## 5. StampedLock Scripts

These scripts manage the versioned locking mechanism for `RedisStampedLock`, supporting write, read, optimistic read modes, and conversions. They operate on a Redis Hash associated with `lockName` that stores fields like `version`, `write_owner_id`, `write_reentrancy_count`, and `read_holders` (e.g., a counter or set).

### 5.1. `try_stamped_lock.lua`

*   **Purpose**: Atomically attempts to acquire a lock in a specific mode ("read", "write", "optimistic") or retrieve the current stamp for optimistic reads.
*   **KEYS**:
    1.  `lockName`: The base key for the stamped lock (e.g., `prefix:bucket:__locks__:{myStampedLock}:stamped_data`).
    2.  `responseCacheKey`: (Optional) The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Unique ID for request idempotency.
    2.  `leaseTimeMs`: Lock expiration time (relevant for "read" / "write" modes).
    3.  `lockOwnerId`: Identifier of the client.
    4.  `mode`: String "read", "write", or "optimistic".
    5.  `responseCacheTTLSeconds`: (Optional) TTL for response cache.
*   **Logic (Conceptual)**:
    *   **Optimistic Mode**:
        1.  Read current version counter from `lockName` Hash.
        2.  Return version string (e.g., "versionNumber").
    *   **Read Mode**:
        1.  Check if a write lock is active (e.g., a `write_owner_id` field is set).
        2.  If write lock active by another, return its TTL (e.g., "-TTL").
        3.  If no write lock, increment read counter / add reader. Update lease (`leaseTimeMs`) if this is the first reader or based on policy.
        4.  Return current version string (e.g., "versionNumber") indicating successful read lock.
    *   **Write Mode**:
        1.  Check if any read locks are active (e.g., `read_count` > 0) OR if a write lock is held by another owner.
        2.  If conflicting lock active, return its TTL or error (e.g., "-TTL" or "-READERS_EXIST").
        3.  If free, or reentrant write by same `lockOwnerId`:
            *   Set `write_owner_id` to `lockOwnerId`.
            *   Increment write reentrancy count.
            *   Increment master version counter.
            *   Set lease (`leaseTimeMs`).
            *   Return new version string.
*   **Returns**:
    *   Optimistic: Current version string (e.g., "42").
    *   Read/Write (Success): New or current version string (e.g., "43").
    *   Read/Write (Failure): Negative string indicating TTL of conflicting lock (e.g., "-50000") or specific error code (e.g., "-READERS_EXIST").

### 5.2. `unlock_stamped_lock.lua`

*   **Purpose**: Atomically releases a read or write lock based on the provided stamp information and mode.
*   **KEYS**:
    1.  `lockName`: The base key for the stamped lock.
    2.  `unlockChannelBaseName`: Base part of the unlock channel name.
    3.  `responseCacheKey`: (Optional) The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `stampPayload`: Information extracted from the stamp (e.g., version, owner for write). This might not be the full "W:owner:uuid" but rather the "owner:uuid" part.
    3.  `lockOwnerId`: Identifier of the client performing the unlock.
    4.  `mode`: String "read" or "write".
    5.  `responseCacheTTLSeconds`: (Optional) TTL for response cache.
*   **Logic (Conceptual)**:
    *   **Read Mode**:
        1.  Decrement read counter associated with `lockName`.
        2.  If read count becomes zero (or based on other conditions indicating it's appropriate to notify), publish a message containing *only* the `UnlockType` "STAMPED_READ_RELEASED" to the specific lock's unlock channel. The channel is constructed by appending the lock identifier (extracted from `KEYS[1]`) to `KEYS[2]` (the `unlockChannelBaseName`). Example constructed channel: `<prefix>:<bucketName>:__unlock_channels__:{myStampedLock}`.
        3.  Return `1` for success.
    *   **Write Mode**:
        1.  Verify `lockOwnerId` matches the stored `write_owner_id` in `lockName` Hash.
        2.  If matches, decrement write reentrancy count.
        3.  If count becomes zero, clear `write_owner_id` and `write_reentrancy_count`. Publish a message containing *only* the `UnlockType` "STAMPED_WRITE_RELEASED" to the specific lock's unlock channel. The channel is constructed by appending the lock identifier (extracted from `KEYS[1]`) to `KEYS[2]` (the `unlockChannelBaseName`). Example constructed channel: `<prefix>:<bucketName>:__unlock_channels__:{myStampedLock}`.
        4.  Return `1` for success.
    *   If ownership/stamp validation fails, return `0`.
*   **Returns**: `1` on successful full release. `0` on failure (e.g., owner mismatch, not held) or partial reentrant release.

### 5.3. `validate_stamp.lua`

*   **Purpose**: Atomically validates an optimistic read stamp against the current lock version and write status.
*   **KEYS**:
    1.  `lockName`: The base key for the stamped lock.
    2.  `responseCacheKey`: (Optional) The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `expectedVersion`: The version string from the optimistic read stamp (e.g., "42").
    3.  `lockOwnerId`: Identifier of the client (for context/logging).
    4.  `responseCacheTTLSeconds`: (Optional) TTL for response cache.
*   **Logic (Conceptual)**:
    1.  Read current version counter and write lock status (e.g., `write_owner_id`) from `lockName` Hash.
    2.  Return `1` if `expectedVersion` matches current version AND no write lock is held.
    3.  Otherwise, return `0`.
*   **Returns**: `1` if stamp is valid. `0` if invalid (version changed or write lock acquired since stamp was issued).

### 5.4. `convert_to_write_lock.lua`

*   **Purpose**: Atomically attempts to upgrade a read lock (validated by `lockOwnerId` and optionally `readStampVersion`) to an exclusive write lock. Fails if other readers exist.
*   **KEYS**:
    1.  `lockName`: The base key for the stamped lock.
    2.  `responseCacheKey`: (Optional) The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `readStampVersion`: The version string from the read stamp being converted (e.g., "42").
    3.  `lockOwnerId`: Identifier of the client holding the read lock and attempting conversion.
    4.  `leaseTimeMs`: New lease time for the write lock.
    5.  `responseCacheTTLSeconds`: (Optional) TTL for response cache.
*   **Logic (Conceptual)**:
    1.  Verify that `lockOwnerId` holds a read lock (e.g., is in readers set or read_count > 0 for this owner) AND that the current version matches `readStampVersion`.
    2.  Check if total read count is 1 (only this owner holds a read lock).
    3.  If conditions are met:
        *   Decrement read count / remove from readers.
        *   Set `write_owner_id` to `lockOwnerId`, set write reentrancy count to 1.
        *   Increment master version counter.
        *   Set new lease (`leaseTimeMs`).
        *   Return new (write) version string.
    4.  Else, return an error code or `nil`.
*   **Returns**: New write version string on success. `nil` or error string on failure.

### 5.5. `convert_to_read_lock.lua`

*   **Purpose**: Atomically downgrades an exclusive write lock (held by `lockOwnerId`) to a read lock.
*   **KEYS**:
    1.  `lockName`: The base key for the stamped lock.
    2.  `responseCacheKey`: (Optional) The fully constructed key for the response cache, passed by the client.
*   **ARGV**:
    1.  `requestUuid`: Idempotency ID.
    2.  `writeStampInfo`: Payload from the write stamp (e.g., "ownerId:uuid" part, or just version if sufficient).
    3.  `lockOwnerId`: Identifier of the client holding the write lock.
    4.  `leaseTimeMs`: New lease time for the read lock.
    5.  `responseCacheTTLSeconds`: (Optional) TTL for response cache.
*   **Logic (Conceptual)**:
    1.  Verify `lockOwnerId` holds the write lock (matches `write_owner_id`).
    2.  If matches and write reentrancy count is 1 (or if full downgrade is intended regardless of reentrancy):
        *   Clear `write_owner_id` and `write_reentrancy_count`.
        *   Increment read counter / add `lockOwnerId` to readers.
        *   (Optional: version counter might not change on downgrade, or it might).
        *   Set new lease (`leaseTimeMs`).
        *   Return current version string (now as a read lock).
    3.  Else (not owner, or still reentrantly held if script respects that), return error or `nil`.
*   **Returns**: New read version string on success. `nil` or error string on failure.

## 6. Other Scripts (Conceptual Overview)

*   **`extend_locks_batch.lua`**: Extends TTL for multiple locks atomically, verifying ownership for each. Used for optimizing watchdog renewals.
    *   `KEYS`: Array of lock keys.
    *   `ARGV`: `leaseTimeMs`, then one `lockOwnerId` for each key.
    *   Returns: Array of 1s (success) or 0s (failure) per lock.
*   **Read/Write Lock Scripts** (`try_read_lock.lua`, `try_write_lock.lua`, `unlock_read_lock.lua`, `unlock_write_lock.lua`): Manage shared read access and exclusive write access, likely using Redis Hashes or Sets for counters/owner tracking.
*   **Stamped Lock Scripts** (`try_stamped_lock.lua`, `validate_stamp.lua`, `convert_to_write_lock.lua`, `unlock_stamped_lock.lua`): Implement optimistic read and pessimistic write locking semantics using stamps/versions.
*   **`get_or_initialize_state.lua`**: Retrieves a state key; if it doesn't exist, initializes it with a default value and TTL.

## 7. Script Loading and Execution

*   **Loading**: `ScriptLoader` bean loads `.lua` files from classpath at startup, caching them.
*   **Execution**: `RedisLockOperationsImpl` retrieves cached scripts and executes them via `ClusterCommandExecutor.executeScript(...)` or similar, handling `EVALSHA`/`EVAL` fallback.