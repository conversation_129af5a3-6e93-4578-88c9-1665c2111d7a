# Builder Pattern and Configuration Flow

```mermaid
classDiagram
    class LockBucketRegistry {
        +LockComponentRegistry componentRegistry
        +RedisLockProperties globalProperties
        +RedisCoreProperties redisCoreProperties
        +LockBucketBuilder builder(String bucketName)
        +LockBucketBuilder builder(String bucketName, String applicationInstanceId)
    }
    note for LockBucketRegistry "Entry point - Spring Bean"

    class LockBucketBuilder {
        +LockComponentRegistry componentRegistry
        +LockBucketConfig bucketConfig
        +LockBucketBuilder withScope(String scope)
        +LockBucketBuilder withLockOwnerSupplier(LockOwnerSupplier supplier)
        +LockBucketBuilder withDefaultLeaseTime(Duration leaseTime)
        +LockBucketBuilder withDefaultRetryInterval(Duration retryInterval)
        +LockBucketBuilder withDefaultStateKeyExpiration(Duration expiration)
        +LockBucketBuilder withDefaultPubSubWaitTimeout(Duration timeout)
        +LockConfigBuilder lock()
    }

    class LockConfigBuilder {
        +LockComponentRegistry componentRegistry
        +LockBucketConfig bucketConfig
        +ReentrantLockConfigBuilder reentrant(String lockName)
        +StateLockConfigBuilder state(String lockName, String expectedState)
        +ReadWriteLockConfigBuilder readWrite(String lockName)
        +StampedLockConfigBuilder stamped(String lockName)
    }

    class AbstractLockTypeConfigBuilder {
        <<Abstract>>
        +LockComponentRegistry componentRegistry
        +LockBucketConfig bucketConfig
        +String lockName
        +Duration instanceLeaseTime
        +Duration instanceRetryInterval
        +AbstractLockTypeConfigBuilder withLeaseTime(Duration leaseTime)
        +AbstractLockTypeConfigBuilder withRetryInterval(Duration retryInterval)
        +abstract T build()
    }

    class ReentrantLockConfigBuilder {
        +ReentrantLockConfigBuilder(LockComponentRegistry registry, LockBucketConfig config, String lockName)
        +RedisReentrantLock build()
    }
    ReentrantLockConfigBuilder --|> AbstractLockTypeConfigBuilder

    class StateLockConfigBuilder {
        +String expectedState
        +StateLockConfigBuilder(LockComponentRegistry registry, LockBucketConfig config, String lockName, String expectedState)
        +StateLockConfigBuilder withInitIfNotExist(boolean initIfNotExist)
        +RedisStateLock build()
    }
    StateLockConfigBuilder --|> AbstractLockTypeConfigBuilder

    class ReadWriteLockConfigBuilder {
        +ReadWriteLockConfigBuilder(LockComponentRegistry registry, LockBucketConfig config, String lockName)
        +RedisReadWriteLock build()
    }
    ReadWriteLockConfigBuilder --|> AbstractLockTypeConfigBuilder

    class StampedLockConfigBuilder {
        +StampedLockConfigBuilder(LockComponentRegistry registry, LockBucketConfig config, String lockName)
        +RedisStampedLock build()
    }
    StampedLockConfigBuilder --|> AbstractLockTypeConfigBuilder

    class LockBucketConfig {
        +String bucketName
        +String scope
        +Duration leaseTime
        +Duration retryInterval
        +Duration stateKeyExpiration
        +Duration pubSubWaitTimeout
        +LockOwnerSupplier lockOwnerSupplier
        +boolean unlockMessageListenerEnabled
        +LockBucketConfig copy()
        +LockBucketConfig withLeaseTime(Duration leaseTime)
        +LockBucketConfig withRetryInterval(Duration retryInterval)
        +Duration getEffectiveLeaseTime(Duration instanceOverride)
        +Duration getEffectiveRetryInterval(Duration instanceOverride)
    }

    class RedisLockProperties {
        +boolean enabled
        +Duration leaseTime
        +Duration retryInterval
        +boolean unlockMessageListenerEnabled
        +Duration stateKeyExpiration
        +Duration pubSubWaitTimeout
        +WatchdogProperties watchdog
        +String lockOwnerIdValidationRegex
        +int maxLockNameLength
        +int maxBucketNameLength
        +int maxScopeLength
    }

    class RedisCoreProperties {
        <<from redis-core>>
        +KeyspacePrefixes keyspacePrefixes
    }

    class LockComponentRegistry {
        +ScriptLoader scriptLoader
        +UnlockMessageListenerManager unlockMessageListenerManager
        +LockWatchdog lockWatchdog
        +RedisLockOperations redisLockOperations
        +DefaultLockOwnerSupplier lockOwnerSupplier
        +RedisLockErrorHandler errorHandler
        +ObjectProvider<LockMonitor> lockMonitorProvider
    }

    LockBucketRegistry o-- LockComponentRegistry : injects
    LockBucketRegistry o-- RedisLockProperties : uses for defaults
    LockBucketRegistry o-- RedisCoreProperties : uses for key prefixes
    LockBucketRegistry --> LockBucketBuilder : creates

    LockBucketBuilder o-- LockComponentRegistry : passes to next builder
    LockBucketBuilder o-- LockBucketConfig : configures and passes
    LockBucketBuilder --> LockConfigBuilder : creates

    LockConfigBuilder o-- LockComponentRegistry : passes to lock builders
    LockConfigBuilder o-- LockBucketConfig : passes to lock builders
    LockConfigBuilder --> ReentrantLockConfigBuilder : creates
    LockConfigBuilder --> StateLockConfigBuilder : creates
    LockConfigBuilder --> ReadWriteLockConfigBuilder : creates
    LockConfigBuilder --> StampedLockConfigBuilder : creates

    AbstractLockTypeConfigBuilder o-- LockComponentRegistry : uses for lock creation
    AbstractLockTypeConfigBuilder o-- LockBucketConfig : uses for configuration resolution

    LockBucketConfig --> RedisLockProperties : initialized from global defaults
    LockBucketConfig --> RedisCoreProperties : gets key prefixes
```

## Configuration Override Precedence Flow

```mermaid
flowchart TD
    A[YAML Global Properties<br/>destilink.fw.locking.redis.*] --> B[RedisLockProperties<br/>Bean Initialization]
    A1[YAML Redis Core Properties<br/>destilink.fw.redis.core.*] --> B1[RedisCoreProperties<br/>Bean Initialization]
    
    B --> C[LockBucketRegistry<br/>Creates Default LockBucketConfig]
    B1 --> C
    
    C --> D[LockBucketBuilder<br/>Bucket-Level Overrides]
    D --> E{Bucket Override<br/>Methods Called?}
    E -->|Yes| F[Modified LockBucketConfig<br/>with Bucket Defaults]
    E -->|No| G[Original LockBucketConfig<br/>with Global Defaults]
    
    F --> H[LockConfigBuilder]
    G --> H
    
    H --> I[AbstractLockTypeConfigBuilder<br/>Instance-Level Overrides]
    I --> J{Instance Override<br/>Methods Called?}
    J -->|Yes| K[Final Effective Configuration<br/>Instance > Bucket > Global]
    J -->|No| L[Final Effective Configuration<br/>Bucket > Global]
    
    K --> M[Lock Instance Creation<br/>with Final Config]
    L --> M
    
    style A fill:#ffcccc
    style A1 fill:#ffcccc
    style B fill:#ccffcc
    style B1 fill:#ccffcc
    style C fill:#ffffcc
    style D fill:#ccccff
    style F fill:#ccccff
    style G fill:#ccccff
    style I fill:#ffccff
    style K fill:#ccffff
    style L fill:#ccffff
    style M fill:#ffccff
```
