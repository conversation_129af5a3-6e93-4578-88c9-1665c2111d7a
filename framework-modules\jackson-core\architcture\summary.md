# Redis Locking Module Architecture - Summary

This document provides a high-level summary of the Redis locking module architecture as documented in the various diagrams.

## Overview

The Redis locking module provides distributed locking capabilities using Redis as the underlying storage mechanism. It offers various lock types (reentrant, read-write, stamped, state) with both synchronous and asynchronous APIs. The module is designed to be highly configurable, performant, and resilient in distributed environments.

## Key Components

### Core Components

- **RedisLockFactory**: The primary entry point for client applications, responsible for creating various lock instances.
- **AsyncLock**: The core interface that all lock implementations implement, providing asynchronous locking operations.
- **AbstractRedisLock**: Base class for all Redis lock implementations, handling common functionality.
- **Lock Implementations**: Various specialized lock types:
  - **RedisReentrantLock**: Standard reentrant lock
  - **RedisReadWriteLock**: Read-write lock with shared/exclusive access
  - **RedisStampedLock**: Optimistic/pessimistic lock with versioning
  - **RedisStateLock**: Lock with state transitions

### Supporting Services

- **RedisLockOperations**: Core service for Redis operations
- **<PERSON><PERSON><PERSON><PERSON><PERSON>der**: Manages Lua scripts for atomic operations
- **LockWatchdog**: Extends lock leases automatically
- **UnlockMessageListener**: Provides non-polling unlock notifications
- **LockSemaphoreHolder**: Manages semaphores for waiting threads
- **LockMonitor**: Collects metrics on lock operations
- **RedisKeyBuilder**: Constructs Redis keys with proper structure

## Architecture Highlights

### Asynchronous First Design

The module is built with an asynchronous-first approach, using CompletableFuture for all core operations. Synchronous methods are provided as blocking wrappers around the asynchronous API. This design enables high throughput and efficient resource utilization.

### Redis Lua Scripts

Atomic operations are implemented using Redis Lua scripts, ensuring consistency and reducing network overhead. Scripts are loaded once and executed using their SHA1 hash for efficiency.

### Non-Polling Unlock Notifications

Instead of polling for lock releases, the module uses Redis Pub/Sub for immediate unlock notifications. This significantly reduces network traffic and CPU usage in high-contention scenarios.

### Redis Cluster Compatibility

The module is fully compatible with Redis Cluster, using hash tags for key co-location and handling cluster redirections transparently.

### Comprehensive Metrics

Detailed metrics are collected for lock acquisition, release, lease extension, and errors, enabling effective monitoring and troubleshooting.

## Configuration System

The configuration system is hierarchical, with multiple levels:

1. **Global Configuration**: Default settings for all locks
2. **Bucket Configuration**: Settings for groups of related locks
3. **Instance Configuration**: Per-lock instance overrides

Key configuration options include:
- Lock timeout and lease duration
- Retry parameters
- Watchdog settings
- Owner identification
- Redis key prefixes and buckets

## Exception Handling

The module uses a structured exception hierarchy:
- **AbstractRedisLockException**: Base exception class
- **LockAcquisitionException**: Failed to acquire lock
- **LockConversionException**: Failed to convert lock type
- **StampValidationException**: Invalid stamp for stamped lock

## Integration Points

The module integrates with:
- **Spring Boot**: Auto-configuration and conditional beans
- **Micrometer**: Metrics collection
- **Spring Actuator**: Health indicators
- **Redis Core**: Shared Redis functionality

## Future Roadmap

Planned enhancements include:
- Java 21 virtual threads support
- Structured concurrency integration
- Enhanced metrics and observability
- Performance optimizations for high-throughput scenarios
- Additional lock types and features

## Conclusion

The Redis locking module provides a robust, flexible, and efficient distributed locking solution. Its architecture balances performance, reliability, and usability, making it suitable for a wide range of distributed application scenarios.

For detailed information on specific aspects of the architecture, refer to the individual diagram files in the `architcture` directory.