package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

public class LockBucketBuilder {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private LockBucketConfig bucketConfig;

    public LockBucketBuilder(LockComponentRegistry componentRegistry, String bucketName,
            LockBucketConfig bucketConfig) {
        this.componentRegistry = componentRegistry;
        this.bucketName = bucketName;
        this.bucketConfig = bucketConfig;
    }

    public LockBucketBuilder withDefaultLeaseTime(Duration leaseTime) {
        this.bucketConfig = LockBucketConfig.builder()
                .leaseTime(leaseTime)
                .retryInterval(bucketConfig.retryInterval())
                .stateKeyExpiration(bucketConfig.stateKeyExpiration())
                .build();
        return this;
    }

    public LockBucketBuilder withDefaultRetryInterval(Duration retryInterval) {
        this.bucketConfig = LockBucketConfig.builder()
                .leaseTime(bucketConfig.leaseTime())
                .retryInterval(retryInterval)
                .stateKeyExpiration(bucketConfig.stateKeyExpiration())
                .build();
        return this;
    }

    // Add other with... methods for bucket config properties

    public LockConfigBuilder lock() {
        return new LockConfigBuilder(componentRegistry, bucketName, bucketConfig);
    }
}
