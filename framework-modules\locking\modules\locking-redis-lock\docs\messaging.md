# Redis Locking Module: Final Unlock Messaging Mechanism

## 1. Introduction

This document describes the messaging mechanism used by the `locking-redis-lock` module, specifically utilizing Redis Publish/Subscribe (Pub/Sub) for unlock notifications. This is a core component of the non-polling lock acquisition strategy, designed to efficiently signal waiting threads when a lock becomes available. This consolidated plan draws from the strengths of previous proposals.

## 2. Redis Publish/Subscribe (Pub/Sub) Overview

Redis Pub/Sub allows publishers to send messages to channels, and subscribers to listen to these channels without direct knowledge of each other.

*   **Publishers**: Threads or processes that successfully release a lock.
*   **Channels**: The `UnlockMessageListener` subscribes to a channel pattern like `<prefix>:<bucketName>:__unlock_channels__:*`. The specific lock instance (e.g., `{order123}`) is part of the actual channel name to which messages are published (e.g., `<prefix>:<bucketName>:__unlock_channels__:{order123}`).
*   **Message Payload**: The message published by Lua scripts to a specific lock's Pub/Sub channel contains *only* an `UnlockType` string (e.g., "REGULAR_RELEASE", "RW_WRITE_RELEASED"). The `UnlockMessageListener` derives the `lockName` from the channel name it received the message on.
*   **Subscribers**: `UnlockMessageListener` instances, one per bucket, managed by `UnlockMessageListenerManager`.

## 3. Unlock Notification Mechanism

1.  **Publish on Unlock**: When a lock is successfully released (typically via an atomic Lua script like `unlock.lua` or `unlock_state_lock.lua`), the script publishes a message containing *only* an `UnlockType` string to the specific lock's Pub/Sub channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`).
    *   The `UnlockType` (e.g., "REGULAR_RELEASE", "RW_WRITE_RELEASED") indicates the nature of the unlock event.
2.  **Subscription and Listening**:
    *   When a thread attempts to acquire a lock and finds it held, it registers its interest in receiving an unlock notification for that specific lock instance (identified by its `lockName` component like `{<lockName>}`).
    *   The `UnlockMessageListenerManager` ensures that an `UnlockMessageListener` for the relevant `bucketName` is active. This listener subscribes to a pattern for its bucket-specific Pub/Sub channels (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`).
3.  **Signal Waiting Threads**:
    *   When an `UnlockMessageListener` receives a message:
        *   It derives the specific `lockName` (e.g., `{order123}`) from the *channel name* on which the message was received (e.g., from `<prefix>:<bucketName>:__unlock_channels__:{order123}`).
        *   It parses the `UnlockType` from the message payload.
        *   It retrieves the associated `LockSemaphoreHolder` for the derived `lockName` (from an internal map within the listener).
        *   Based on the `UnlockType` and potentially the type of lock/waiters, it intelligently signals the `Semaphore` within the `LockSemaphoreHolder` (e.g., releasing one permit for exclusive locks, or multiple for shared read locks if applicable).
4.  **Wake Up and Retry**: Signalling the semaphore wakes up one or more threads that were passively waiting on it for that specific lock. These awakened threads then re-attempt to acquire the lock.

## 4. Key Components

*   **`UnlockMessageListenerManager` (Bean)**:
    *   Manages the lifecycle of `UnlockMessageListener` instances, typically ensuring one listener per configured lock bucket.
    *   Handles graceful shutdown of listeners (`@PreDestroy`).
*   **`UnlockMessageListener` (Per Bucket, Managed by Manager)**:
    *   Implements `org.springframework.data.redis.connection.MessageListener`.
    *   Subscribes to a Redis Pub/Sub channel pattern specific to its `bucketName` (e.g., `<prefix>:<bucketName>:__unlock_channels__:*`). Internally manages a map of `LockSemaphoreHolder` instances, keyed by `lockKey`, for locks within its bucket.
    *   The `onMessage()` method is invoked by the Redis client library when a message arrives on its specific subscribed channel.
    *   Processes messages by deriving `lockName` from the channel and parsing `UnlockType` from the payload, using an optimized executor.
    *   Signals the `LockSemaphoreHolder` associated with the derived `lockName` based on the `UnlockType`.
*   **`LockSemaphoreHolder` (Non-Spring managed, per lock name component, e.g., `{<lockName>}` )**:
    *   Encapsulates a `java.util.concurrent.Semaphore` (typically initialized with 0 permits, fair mode).
    *   Tracks the number of waiting threads for its specific lock key.
    *   Provides `waitForUnlock(timeout)` and `signal()` methods.

## 5. Unlock Channel Key Schema

As defined in the [Redis Key Schema](redis_key_schema.md) document:
The `UnlockMessageListener` subscribes to a channel pattern such as `<prefix>:<bucketName>:__unlock_channels__:*`.
The message published by Lua scripts to a specific lock's channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`) will *only* contain an `UnlockType` string (e.g., "REGULAR_RELEASE"). The `UnlockMessageListener` derives the specific `lockName` (e.g., `{<lockName>}`) from the channel name itself.
Example: Listener subscribes to a pattern like `myApp:__lock_buckets__:orders:__unlock_channels__:*`. A lock release for `{order123}` would publish the `UnlockType` (e.g., "REGULAR_RELEASE") to the channel `myApp:__lock_buckets__:orders:__unlock_channels__:{order123}`.

## 5.A. Defined `UnlockType` Values and Listener Logic

The `UnlockType` string published by Lua scripts provides crucial information to the `UnlockMessageListener` for optimized waking of waiting threads. The listener derives the `lockName` (e.g., `{order123}`) from the channel name (e.g., `<prefix>:<bucketName>:__unlock_channels__:{order123}`) and uses the `UnlockType` from the message payload to decide its signaling strategy.

The following `UnlockType` values are defined:

*   **`REENTRANT_FULLY_RELEASED`**:
    *   **Published By**: `unlock.lua` when a reentrant lock is fully released (reentrancy count becomes zero).
    *   **Listener Logic**: Signals one permit on the `LockSemaphoreHolder`. This is suitable for exclusive locks where only one waiter should attempt acquisition next.

*   **`NON_REENTRANT_RELEASED`**:
    *   **Published By**: `unlock.lua` (or equivalent for non-reentrant types) when a non-reentrant exclusive lock is released.
    *   **Listener Logic**: Signals one permit on the `LockSemaphoreHolder`.

*   **`STATE_LOCK_RELEASED_STATE_UNCHANGED`**:
    *   **Published By**: `unlock_state_lock.lua` when a state lock is released, and the associated state value was *not* changed during the unlock operation.
    *   **Listener Logic**: Signals one permit. Waiters for this `StateLock` will re-evaluate the state upon attempting acquisition.

*   **`STATE_LOCK_RELEASED_STATE_UPDATED`**:
    *   **Published By**: `unlock_state_lock.lua` when a state lock is released, and the associated state value *was* changed during the unlock operation.
    *   **Listener Logic**: Signals one permit. This `UnlockType` can inform waiting threads that the state they might have previously observed could be different, prompting a fresh state check.

*   **`RW_READ_RELEASED_WAKEN_READERS`**:
    *   **Published By**: `unlock_read_lock.lua` (for `RedisReadWriteLock`) when a read lock is released, and the listener should prioritize waking up other waiting read lock requestors.
    *   **Listener Logic**: May signal multiple permits (e.g., `semaphore.release(numberOfWaitingReadLockThreads)`) if it can identify multiple threads waiting specifically for a read lock. This allows concurrent readers to proceed.

*   **`RW_READ_RELEASED_WAKEN_SINGLE_WRITER`**:
    *   **Published By**: `unlock_read_lock.lua` when a read lock is released (potentially the last reader), and there are writers waiting.
    *   **Listener Logic**: Signals one permit. This gives a waiting writer a chance to acquire.

*   **`RW_WRITE_RELEASED_WAKEN_ALL`**:
    *   **Published By**: `unlock_write_lock.lua` (for `RedisReadWriteLock`) when a write lock is released.
    *   **Listener Logic**: May signal a larger number of permits or use a broadcast-like mechanism if the semaphore supports it, or signal multiple times. This allows both waiting readers and one waiting writer to attempt acquisition. The Lua script for write lock acquisition will ensure only one writer succeeds.

*   **`STAMPED_READ_RELEASED`**:
    *   **Published By**: `unlock_stamped_lock.lua` when a read stamp is released.
    *   **Listener Logic**: Similar to `RW_READ_RELEASED_WAKEN_READERS`, potentially waking multiple optimistic or pessimistic read waiters.

*   **`STAMPED_WRITE_RELEASED`**:
    *   **Published By**: `unlock_stamped_lock.lua` when a write stamp is released.
    *   **Listener Logic**: Similar to `RW_WRITE_RELEASED_WAKEN_ALL`, potentially waking any type of waiter.

*   **`STAMPED_CONVERTED_TO_READ`**:
    *   **Published By**: `convert_to_read_lock.lua` (for `RedisStampedLock`) when a write lock is successfully converted to a read lock.
    *   **Listener Logic**: May signal multiple permits if other readers were waiting, as the lock is now available for shared reading.

*   **`STAMPED_CONVERTED_TO_WRITE`**:
    *   **Published By**: `convert_to_write_lock.lua` (for `RedisStampedLock`) when a read lock is successfully upgraded to a write lock.
    *   **Listener Logic**: No explicit signal is typically needed as the current thread has acquired the write lock. However, if the conversion itself involved a release-then-acquire pattern internally that published, this `UnlockType` would indicate the lock remains held.

The `UnlockMessageListener`'s implementation should be sophisticated enough to use these `UnlockType` values to optimize waking strategies, reducing unnecessary contention (the "thundering herd" problem) and improving overall throughput, especially for `RedisReadWriteLock` and `RedisStampedLock`. The listener should also consider the number of current waiters on the `LockSemaphoreHolder` when deciding how many permits to release.
## 6. Reliability and Considerations

*   **Message Delivery**: Redis Pub/Sub is a "fire-and-forget" system; message delivery is not guaranteed (e.g., if a subscriber disconnects and reconnects).
*   **Fallback Mechanism**: The lock acquisition logic includes a fallback mechanism. Waiting threads also have a timeout based on the current lock holder's TTL (obtained from the acquisition attempt) or a configured `retryInterval`. If an unlock notification is missed (or the listener is processing a message for a different lock within the same bucket), the waiting thread will eventually time out its wait on the semaphore and re-attempt the lock acquisition, ensuring liveness.
*   **Message Content & Channel**: The message payload published by Lua contains *only* the `UnlockType`. The `UnlockMessageListener` derives the specific `lockName` (e.g. `{order123}`) from the channel name it received the message on (e.g. `myApp:__lock_buckets__:orders:__unlock_channels__:{order123}`). This information is crucial for the listener to correctly identify and signal the appropriate `LockSemaphoreHolder` and apply performance-optimized waking strategies.
*   **Executor for Listeners**: Using a dedicated, appropriately sized thread pool (or virtual threads if applicable to the project's Java version) for `UnlockMessageListener`'s `onMessage` processing is critical. This prevents blocking the Redis client's message dispatching threads, which could otherwise impact all Pub/Sub operations for the application instance.
*   **Stale Permits**: The `LockSemaphoreHolder.waitForUnlock()` method must drain any stale permits from its semaphore before a thread begins to wait. This prevents a thread from immediately acquiring a permit that was released for a previous, now-timed-out waiter.
*   **Pub/Sub Overhead**: The design of one `UnlockMessageListener` per bucket means that a single listener handles notifications for all locks within that bucket. While simpler to manage, this could become a bottleneck if a single bucket has extremely high lock/unlock churn across many different lock keys. Message parsing within the listener to route signals to the correct `LockSemaphoreHolder` is crucial.

This messaging system, coupled with the semaphore-based waiting and TTL-based fallbacks, provides an efficient and robust mechanism for non-polling distributed lock acquisition.