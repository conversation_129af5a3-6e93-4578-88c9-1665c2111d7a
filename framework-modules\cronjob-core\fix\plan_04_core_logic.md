# Plan: Redis Lock - Step 4: Core Asynchronous Logic and Redis Operations

This step is critical for aligning the module with its core architectural principles: Async-First design and mandatory usage of `redis-core`.

### Step 4.1: Refactor `RedisLockOperationsImpl`

The implementation must use `ClusterCommandExecutor` for all Redis interactions.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/impl/RedisLockOperationsImpl.java`

**Action:**
Replace the current `StringRedisTemplate`-based implementation with one that uses `ClusterCommandExecutor`. This class will be responsible for preparing keys and arguments and calling the executor.

```java
package com.tui.destilink.framework.locking.redis.service.impl;

import com.tui.destilink.framework.locking.redis.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.service.ScriptLoader;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.script.RedisScript;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RequiredArgsConstructor
public class RedisLockOperationsImpl implements RedisLockOperations {
    private final ClusterCommandExecutor commandExecutor;
    private final ScriptLoader scriptLoader;

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration leaseTime) {
        RedisScript<Long> script = scriptLoader.getTryLockScript();
        List<String> keys = Collections.singletonList(lockKey);
        return commandExecutor.executeScript(
                script, 
                keys, 
                ownerId, 
                String.valueOf(leaseTime.toMillis())
        ).thenApply(result -> Long.valueOf(1L).equals(result));
    }

    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String ownerId) {
        RedisScript<Long> script = scriptLoader.getReleaseLockScript();
        List<String> keys = Collections.singletonList(lockKey);
        return commandExecutor.executeScript(
            script,
            keys,
            ownerId
        ).thenApply(result -> Long.valueOf(1L).equals(result));
    }

    // ... implement all other methods from RedisLockOperations interface using commandExecutor
}
```

### Step 4.2: Refactor `AbstractRedisLock`

This base class will contain the core async-first logic and synchronous wrappers.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/AbstractRedisLock.java`

**Action:**
Ensure the class implements `AsyncLock` and that its synchronous methods call the asynchronous ones and block for the result.

```java
package com.tui.destilink.framework.locking.redis;

// ... other imports
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;

public abstract class AbstractRedisLock implements AsyncLock {
    
    // ... fields and constructor ...

    // --- Synchronous Wrappers ---

    @Override
    public void lock() {
        lockAsync().join();
    }

    @Override
    public void lockInterruptibly() throws InterruptedException {
        try {
            lockAsync().get();
        } catch (Exception e) {
            if (e.getCause() instanceof InterruptedException) {
                throw (InterruptedException) e.getCause();
            }
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean tryLock() {
        return tryLockAsync().join();
    }
    
    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        try {
            return tryLockAsync(time, unit).get(time, unit);
        } catch (Exception e) {
             if (e.getCause() instanceof InterruptedException) {
                throw (InterruptedException) e.getCause();
            }
            throw new RuntimeException(e);
        }
    }

    @Override
    public void unlock() {
        unlockAsync().join();
    }

    @Override
    public Condition newCondition() {
        throw new UnsupportedOperationException("Conditions are not supported by Redis locks.");
    }

    // --- Asynchronous API (to be implemented by subclasses) ---

    @Override
    public abstract CompletableFuture<Void> lockAsync();
    
    // ... other abstract async methods ...
}
```

### Step 4.3: Align Concrete Lock Implementations

Review each lock implementation (e.g., `RedisReentrantLock`) to ensure it correctly overrides the abstract async methods from `AbstractRedisLock`.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/impl/RedisReentrantLock.java`

**Action:**
The implementation should contain the logic for reentrancy (checking local thread-local, then calling Redis) and must be fully asynchronous.

```java
package com.tui.destilink.framework.locking.redis.impl;

// ... other imports
import java.util.concurrent.CompletableFuture;

public class RedisReentrantLock extends AbstractRedisLock {

    // ... constructor and fields ...

    @Override
    public CompletableFuture<Void> lockAsync() {
        // Implement reentrant lock logic asynchronously
        // 1. Check thread-local for existing hold.
        // 2. If held, increment and return completed future.
        // 3. If not held, call redisLockOperations.tryLock(...)
        // 4. If acquired, update thread-local and complete future.
        // 5. If not acquired, use UnlockMessageListener/LockSemaphoreHolder to wait asynchronously.
        return new CompletableFuture<>(); // Placeholder
    }
    
    // ... implement other abstract async methods ...
}
```

---
