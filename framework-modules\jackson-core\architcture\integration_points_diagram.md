# Integration Points

This diagram illustrates how the Redis locking module integrates with other systems and components within the Destilink Framework and external applications.

```mermaid
classDiagram
    class RedisLockAutoConfiguration {
        +redisLockOperations() RedisLockOperations
        +redisLockFactory() RedisLockFactory
        +lockWatchdog() LockWatchdog
        +unlockMessageListenerManager() UnlockMessageListenerManager
    }

    class RedisLockFactory {
        +createLock(bucket, name) AsyncLock
        +createReentrantLock(bucket, name) RedisReentrantLock
        +createReadWriteLock(bucket, name) RedisReadWriteLock
        +createStampedLock(bucket, name) RedisStampedLock
        +createStateLock(bucket, name, states) RedisStateLock
    }

    class AsyncLock {
        <<Interface>>
        +tryLock(timeout) CompletableFuture~Boolean~
        +unlock() CompletableFuture~Void~
        +isLocked() CompletableFuture~Boolean~
    }

    class RedisTemplate {
        +opsForValue() ValueOperations
        +opsForHash() HashOperations
        +execute(RedisCallback) Object
        +executePipelined(RedisCallback) List
    }

    class ClusterCommandExecutor {
        +executeCommand(RedisConnection, String, Object[]) Object
        +executeScript(RedisConnection, String, List~RedisKey~, Object[]) Object
    }

    class RedisConnectionFactory {
        +getConnection() RedisConnection
        +getClusterConnection() RedisClusterConnection
    }

    class MicroserviceApplication {
        -RedisLockFactory lockFactory
        +performBusinessOperation() void
    }

    class CronjobApplication {
        -RedisLockFactory lockFactory
        +executeScheduledTask() void
    }

    class DistributedWorkflow {
        -RedisLockFactory lockFactory
        +processWorkItem(id) void
        +coordinateDistributedExecution() void
    }

    class RedisLockHealthIndicator {
        +health() Health
        -checkLockOperations() void
    }

    class RedisLockMetricsCollector {
        +collectMetrics() void
        +registerGauges() void
    }

    class SpringActuator {
        +health() HealthEndpoint
        +metrics() MetricsEndpoint
    }

    class RedisLockProperties {
        +String keyPrefix
        +Map~String, LockBucketConfig~ buckets
    }

    class RedisLockOperations {
        -ClusterCommandExecutor commandExecutor
        -ScriptLoader scriptLoader
        -RedisKeyBuilder keyBuilder
        +tryLock(bucket, name, owner, leaseTime) CompletableFuture~Boolean~
        +unlock(bucket, name, owner) CompletableFuture~UnlockType~
    }

    class LockContextDecorator {
        +withLockContext(bucket, name, owner) Closeable
        +createClosableScope(bucket, name, owner) Closeable
    }

    class RedisCore {
        +ClusterCommandExecutor commandExecutor
        +RedisKeyBuilder keyBuilder
    }

    %% Relationships
    RedisLockAutoConfiguration --> RedisLockFactory : creates
    RedisLockAutoConfiguration --> RedisLockOperations : creates
    RedisLockAutoConfiguration --> LockWatchdog : creates
    RedisLockFactory --> AsyncLock : creates implementations of
    RedisLockOperations --> ClusterCommandExecutor : uses
    ClusterCommandExecutor --> RedisConnectionFactory : uses
    ClusterCommandExecutor --> RedisTemplate : uses
    MicroserviceApplication --> RedisLockFactory : uses
    CronjobApplication --> RedisLockFactory : uses
    DistributedWorkflow --> RedisLockFactory : uses
    RedisLockHealthIndicator --> RedisLockOperations : monitors
    RedisLockMetricsCollector --> RedisLockOperations : monitors
    SpringActuator --> RedisLockHealthIndicator : exposes
    SpringActuator --> RedisLockMetricsCollector : exposes
    RedisLockAutoConfiguration --> RedisLockProperties : uses
    RedisCore --> ClusterCommandExecutor : provides
    RedisCore --> RedisConnectionFactory : provides

    %% Notes
    class RedisLockAutoConfiguration {
        <<Configuration>>
        Primary integration point
    }
    class RedisLockFactory {
        <<Service>>
        Main API for client applications
    }
    class AsyncLock {
        <<Interface>>
        Core abstraction for all locks
    }
    class RedisCore {
        <<Module>>
        Dependency on redis-core module
    }
    class MicroserviceApplication {
        <<Client>>
        Example microservice integration
    }
    class CronjobApplication {
        <<Client>>
        Example cronjob integration
    }
    class SpringActuator {
        <<Integration>>
        Monitoring and health checks
    }
    class LockContextDecorator {
        <<Service>>
        MDC logging integration
    }
```

## Key Integration Points

1. **Client Application Integration**
   - `RedisLockFactory` is the primary entry point for client applications
   - Factory methods create various lock types (reentrant, read-write, stamped, state)
   - `AsyncLock` interface provides a common abstraction for all lock types
   - Applications inject `RedisLockFactory` to create and use locks

2. **Spring Boot Integration**
   - `RedisLockAutoConfiguration` provides auto-configuration for Spring Boot applications
   - Conditional activation based on presence of Redis dependencies
   - Configuration properties via `RedisLockProperties`
   - Registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`

3. **Redis Core Module Integration**
   - Depends on `redis-core` module for Redis operations
   - Uses `ClusterCommandExecutor` for Redis Cluster compatibility
   - Leverages `RedisKeyBuilder` for standardized key construction

4. **Spring Actuator Integration**
   - `RedisLockHealthIndicator` provides health check information
   - `RedisLockMetricsCollector` exposes metrics via Micrometer
   - Integration with Spring Boot Actuator endpoints

5. **Logging Integration**
   - `LockContextDecorator` provides MDC context for structured logging
   - Integration with Logback and Logstash Encoder
   - Markers for monitoring and alerting

6. **Common Use Cases**
   - Microservices: Distributed coordination and resource protection
   - Cronjobs: Preventing duplicate execution across instances
   - Workflows: Coordinating distributed processing steps
   - Caching: Protecting shared resources during updates

7. **Extension Points**
   - Custom lock owner identification via `LockOwnerSupplier`
   - Custom error handling via `RedisLockErrorHandler`
   - Configuration customization via properties