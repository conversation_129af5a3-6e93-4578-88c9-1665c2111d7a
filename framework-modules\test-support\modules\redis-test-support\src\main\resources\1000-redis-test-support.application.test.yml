# Redis Test Support Configuration
# This file configures Redis for cluster mode during testing
spring:
  data:
    redis:
      # Enable cluster mode for testing
      cluster:
        # Set cluster nodes to trigger cluster mode - will be overridden by TestSupportRedisConnectionDetails
        nodes: 
          - "localhost:6378"
          - "localhost:6378"  
          - "localhost:6378"
        max-redirects: 3
      # Configure the connection timeout
      timeout: 2000ms
      # Use Lettuce as the Redis client
      client-type: lettuce
      lettuce:
        # Connection pool settings for testing
        pool:
          enabled: true
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: 1000ms

# Destilink framework Redis configuration
destilink:
  fw:
    redis-core:
      # Enable redis-core for testing
      enabled: true
      # Connection pool configuration for cluster mode
      cluster:
        connection-pool:
          blocking:
            max-total: 8
            max-idle: 8
            min-idle: 0
            max-wait-millis: 1000
          cycle:
            max-total: 8
            max-idle: 8
            min-idle: 0
            max-wait-millis: 1000
