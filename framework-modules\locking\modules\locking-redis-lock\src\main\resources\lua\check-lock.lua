-- Script to check if a lock exists and is held by the expected owner
-- KEYS[1] = Lock key
-- ARGV[1] = Expected lock value (owner ID)

local lockKey = KEYS[1]
local expectedValue = ARGV[1]

-- Get the current value of the lock
local currentValue = redis.call("GET", lockKey)

-- If the lock doesn't exist, return 0
if currentValue == false then
    return 0
end

-- If the lock exists and is held by the expected owner, return 1
if currentValue == expectedValue then
    return 1
end

-- Lock exists but is held by someone else, return 2
return 2