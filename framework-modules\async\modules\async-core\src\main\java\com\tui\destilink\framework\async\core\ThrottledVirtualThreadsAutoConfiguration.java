package com.tui.destilink.framework.async.core;

import com.tui.destilink.framework.core.async.ThrottledVirtualThreadsExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnThreading;
import org.springframework.boot.autoconfigure.thread.Threading;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.core.task.support.TaskExecutorAdapter;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.Arrays;
import java.util.concurrent.Executor;

import static com.tui.destilink.framework.async.core.AsyncConfigurationProperties.PREFIX;

@RequiredArgsConstructor
@AutoConfiguration
@EnableAsync(proxyTargetClass = true)
@EnableConfigurationProperties({AsyncConfigurationProperties.class})
@Slf4j
@ConditionalOnProperty(prefix = PREFIX, name = "enabled", havingValue = "true", matchIfMissing = true)
@ConditionalOnThreading(Threading.VIRTUAL)
public class ThrottledVirtualThreadsAutoConfiguration implements AsyncConfigurer {

    private final AsyncConfigurationProperties config;

    @Override
    public Executor getAsyncExecutor() {
        return new TaskExecutorAdapter(new ThrottledVirtualThreadsExecutor(this.config.getMaxThreads(), config.isFair()));
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        // note: params can be empty but is annotated as @nonnull - see this test where we don't have params
        return (throwable, method, params) -> log.error("Async exception parameters: {}", Arrays.asList(params), throwable);
    }
}