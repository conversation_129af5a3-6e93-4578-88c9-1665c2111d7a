<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>framework-dependencies-parent</artifactId>
    <version>1.0.26-dli-6231-SNAPSHOT</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.tui.destilink.framework</groupId>
        <artifactId>destilink-framework</artifactId>
        <version>1.0.26-dli-6231-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <java.version>21</java.version>

        <!-- Spring Dependencies -->
        <spring-boot.version>3.4.5</spring-boot.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>

        <!-- Spring AWS io -->
        <spring-cloud-aws-dependencies.version>3.3.0</spring-cloud-aws-dependencies.version>

        <!-- Lombok -->
        <lombok.version>1.18.36</lombok.version>

        <!-- Mapstruct -->
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <mapstruct-extensions-spring.version>1.1.2</mapstruct-extensions-spring.version>

        <!-- Annotation processors -->
        <auto-service.version>1.1.1</auto-service.version>

        <!-- Resilience4j -->
        <resilience4j.version>2.3.0</resilience4j.version>

        <!-- Logging -->
        <logbook.version>3.11.0</logbook.version>
        <context-propagation.version>1.1.2</context-propagation.version>

        <!-- CloudEvents -->
        <io.cloudevents.version>4.0.1</io.cloudevents.version>

        <!-- Datadog -->
        <datadog.version>1.49.0</datadog.version>
        <opentracing.version>0.33.0</opentracing.version>

        <!-- Utility -->
        <guava.version>33.4.0-jre</guava.version>
        <commons-io.version>2.18.0</commons-io.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <java-uuid-generator.version>5.1.0</java-uuid-generator.version>
        <jackson-databind-nullable.version>0.2.6</jackson-databind-nullable.version>
        <io.swagger.core.v3.version>2.2.28</io.swagger.core.v3.version>

        <!-- Testing -->
        <mockserver.version>5.15.0</mockserver.version>
        <bouncycastle.version>1.80</bouncycastle.version>
        <com.google.jimfs.version>1.3.0</com.google.jimfs.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>

        <!-- Plugins -->
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <git-commit-id-maven-plugin.version>9.0.1</git-commit-id-maven-plugin.version>
        <cyclonedx-maven-plugin.version>2.9.1</cyclonedx-maven-plugin.version>
        <openapi-generator-maven-plugin.version>7.12.0</openapi-generator-maven-plugin.version>

        <!-- Java Agents -->
        <java-agents.path.dir>${project.build.directory}/java-agents</java-agents.path.dir>
        <!-- Datadog Agent -->
        <java-agents.datadog-agent.download.skip>false</java-agents.datadog-agent.download.skip>
        <java-agents.datadog-agent.download.filename>dd-java-agent.jar</java-agents.datadog-agent.download.filename>
        <java-agents.datadog-agent.jvm-args>
            -javaagent:${java-agents.path.dir}/${java-agents.datadog-agent.download.filename}
            -Ddatadog.slf4j.simpleLogger.defaultLogLevel=error
        </java-agents.datadog-agent.jvm-args>

        <!-- Surefire Arguments -->
        <unitTests.jvmArgs>-XX:+EnableDynamicAgentLoading -Djdk.attach.allowAttachSelf=true -Xshare:off
        </unitTests.jvmArgs>
        <integrationTests.jvmArgs>-XX:+EnableDynamicAgentLoading -Djdk.attach.allowAttachSelf=true -Xshare:off
        </integrationTests.jvmArgs>
        <integrationTests.additionalAgentArgs>${java-agents.datadog-agent.jvm-args}
        </integrationTests.additionalAgentArgs>

        <!-- Vulnerability management -->
        <xmlunit-core.version>2.10.0</xmlunit-core.version>
        <protobuf-java.version>3.25.7</protobuf-java.version>
        <lucene.version>9.12.1</lucene.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Vulnerability management -->
            <!-- Must be on top of spring-boot-dependencies -->
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-core</artifactId>
                <version>${xmlunit-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <!-- There is no BOM for lucene -->
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-analysis-common</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-backward-codecs</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-grouping</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-highlighter</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-join</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-misc</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-memory</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-queries</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-queryparser</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-sandbox</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-spatial-extras</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-spatial3d</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-suggest</artifactId>
                <version>${lucene.version}</version>
            </dependency>

            <!-- Resilience4j -->
            <dependency>
                <!-- Must be on top of spring-cloud -->
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-bom</artifactId>
                <version>${resilience4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring AWS io -->
            <dependency>
                <groupId>io.awspring.cloud</groupId>
                <artifactId>spring-cloud-aws-dependencies</artifactId>
                <version>${spring-cloud-aws-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- TODO -->
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>1.49.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Apache Client 4/5.x -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.14</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>5.4.3</version>
            </dependency>

            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>3.49.3</version>
            </dependency>

            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>2.38.0</version>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>2.13.16</version>
            </dependency>
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-reflect</artifactId>
                <version>2.13.16</version>
            </dependency>
            <dependency>
                <groupId>org.apiguardian</groupId>
                <artifactId>apiguardian-api</artifactId>
                <version>1.1.2</version>
            </dependency>
            <!-- TODO -->

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>

            <!-- Mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct.extensions.spring</groupId>
                <artifactId>mapstruct-spring-annotations</artifactId>
                <version>${mapstruct-extensions-spring.version}</version>
                <optional>true</optional>
            </dependency>

            <!-- Logging -->
            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>logbook-bom</artifactId>
                <version>${logbook.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>context-propagation</artifactId>
                <version>${context-propagation.version}</version>
            </dependency>

            <!-- CloudEvents -->
            <dependency>
                <groupId>io.cloudevents</groupId>
                <artifactId>cloudevents-bom</artifactId>
                <version>${io.cloudevents.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Datadog -->
            <dependency>
                <groupId>com.datadoghq</groupId>
                <artifactId>dd-trace-api</artifactId>
                <version>${datadog.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <!-- utility -->
            <dependency>
                <groupId>com.google.auto.service</groupId>
                <artifactId>auto-service-annotations</artifactId>
                <version>${auto-service.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.uuid</groupId>
                <artifactId>java-uuid-generator</artifactId>
                <version>${java-uuid-generator.version}</version>
            </dependency>

            <!-- openapi generator -->
            <dependency>
                <groupId>org.openapitools</groupId>
                <artifactId>jackson-databind-nullable</artifactId>
                <version>${jackson-databind-nullable.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-core-jakarta</artifactId>
                <version>${io.swagger.core.v3.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models-jakarta</artifactId>
                <version>${io.swagger.core.v3.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${io.swagger.core.v3.version}</version>
            </dependency>

            <!-- testing -->
            <dependency>
                <groupId>org.mapstruct.extensions.spring</groupId>
                <artifactId>mapstruct-spring-test-extensions</artifactId>
                <version>${mapstruct-spring-extensions.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-spring-test-listener</artifactId>
                <version>${mockserver.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.google.jimfs</groupId>
                <artifactId>jimfs</artifactId>
                <version>${com.google.jimfs.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct.extensions.spring</groupId>
            <artifactId>mapstruct-spring-annotations</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.google.auto.service</groupId>
            <artifactId>auto-service-annotations</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/application*.yml</include>
                    <include>**/application*.yaml</include>
                    <include>**/application*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <excludes>
                    <exclude>**/application*.yml</exclude>
                    <exclude>**/application*.yaml</exclude>
                    <exclude>**/application*.properties</exclude>
                </excludes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <parameters>true</parameters>
                        <showDeprecation>true</showDeprecation>
                        <annotationProcessorPaths combine.children="append">
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${lombok-mapstruct-binding.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct.extensions.spring</groupId>
                                <artifactId>mapstruct-spring-extensions</artifactId>
                                <version>${mapstruct-extensions-spring.version}</version>
                            </path>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                            </path>
                            <path>
                                <groupId>com.google.auto.service</groupId>
                                <artifactId>auto-service</artifactId>
                                <version>${auto-service.version}</version>
                            </path>
                        </annotationProcessorPaths>
                        <compilerArgs>
                            <compilerArg>-Amapstruct.defaultComponentModel=spring</compilerArg>
                            <compilerArg>-Amapstruct.unmappedTargetPolicy=WARN</compilerArg>
                            <compilerArg>-Amapstruct.defaultInjectionStrategy=constructor</compilerArg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <configuration>
                        <propertiesEncoding>${project.build.sourceEncoding}</propertiesEncoding>
                        <delimiters>
                            <delimiter>@</delimiter>
                        </delimiters>
                        <useDefaultDelimiters>false</useDefaultDelimiters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>io.github.git-commit-id</groupId>
                    <artifactId>git-commit-id-maven-plugin</artifactId>
                    <version>${git-commit-id-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <verbose>true</verbose>
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties
                        </generateGitPropertiesFilename>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.cyclonedx</groupId>
                    <artifactId>cyclonedx-maven-plugin</artifactId>
                    <version>${cyclonedx-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>makeBom</goal>
                            </goals>
                            <configuration>
                                <projectType>application</projectType>
                                <outputDirectory>${project.build.outputDirectory}/META-INF/sbom</outputDirectory>
                                <outputFormat>json</outputFormat>
                                <outputName>application.cdx</outputName>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <id>build-info</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>build-info</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>repackage</id>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <additionalProperties>
                            <destilink.framework.version>${project.version}</destilink.framework.version>
                        </additionalProperties>
                        <excludeGroupIds>com.h2database</excludeGroupIds>
                        <excludes>
                            <exclude>commons-logging:commons-logging</exclude>
                            <exclude>org.projectlombok:lombok</exclude>
                            <exclude>com.google.auto.service:auto-service-annotations</exclude>
                        </excludes>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                        <agents>
                            <!--agent>${java-agents.path.dir}/${java-agents.aspectjweaver.download.filename}</agent-->
                            <agent>${java-agents.path.dir}/${java-agents.datadog-agent.download.filename}</agent>
                        </agents>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.openapitools</groupId>
                    <artifactId>openapi-generator-maven-plugin</artifactId>
                    <version>${openapi-generator-maven-plugin.version}</version>
                    <configuration>
                        <generatorName>spring</generatorName>
                        <generateApis>true</generateApis>
                        <generateModels>true</generateModels>
                        <generateApiTests>false</generateApiTests>
                        <generateModelTests>false</generateModelTests>
                        <generateSupportingFiles>false</generateSupportingFiles>
                        <typeMappings combine.children="append">
                            <typeMapping>SpringProblemDetail=org.springframework.http.ProblemDetail</typeMapping>
                        </typeMappings>
                        <skipIfSpecIsUnchanged>false</skipIfSpecIsUnchanged>
                        <skipOverwrite>false</skipOverwrite>
                        <configOptions>
                            <library>spring-boot</library>
                            <openApiNullable>false</openApiNullable>
                            <dateLibrary>java8</dateLibrary>
                            <useBeanValidation>true</useBeanValidation>
                            <useSpringBoot3>true</useSpringBoot3>
                            <interfaceOnly>true</interfaceOnly>
                            <skipDefaultInterface>true</skipDefaultInterface>
                        </configOptions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>download-java-agent-datadog-agent</id>
                            <phase>initialize</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <skip>${java-agents.datadog-agent.download.skip}</skip>
                                <artifactItems>
                                    <artifactItem>
                                        <groupId>com.datadoghq</groupId>
                                        <artifactId>dd-java-agent</artifactId>
                                        <version>${datadog.version}</version>
                                        <outputDirectory>${java-agents.path.dir}</outputDirectory>
                                        <destFileName>${java-agents.datadog-agent.download.filename}</destFileName>
                                    </artifactItem>
                                </artifactItems>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>analyze</goal>
                            <goal>analyze-only</goal>
                            <goal>analyze-dep-mgt</goal>
                            <goal>analyze-duplicate</goal>
                            <goal>analyze-report</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                            <failOnWarning>false</failOnWarning>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <configuration>
<skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>enforce-dependency-convergence</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <fail>true</fail>
                            <rules>
                                <dependencyConvergence/>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>enforce-banned-dependencies-warn</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <fail>false</fail>
                            <rules>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>io.swagger.core.v3:swagger-annotations</exclude>
                                        <exclude>io.swagger.core.v3:swagger-core</exclude>
                                        <exclude>io.swagger.core.v3:swagger-models</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>enforce-banned-dependencies-fail</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <fail>true</fail>
                            <rules>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>commons-logging:commons-logging</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>jar-only</id>
            <activation>
                <property>
                    <name>packaging</name>
                    <value>!jar</value>
                </property>
            </activation>
            <properties>
                <java-agents.datadog-agent.download.skip>true</java-agents.datadog-agent.download.skip>
                <git-commit-id-maven-plugin.skip>true</git-commit-id-maven-plugin.skip>
                <cyclonedx-maven-plugin.skip>true</cyclonedx-maven-plugin.skip>
            </properties>
        </profile>
        <profile>
            <id>gitlab-ci-only</id>
            <activation>
                <property>
                    <name>env.CI</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <java-agents.datadog-agent.download.skip>${skipTests}</java-agents.datadog-agent.download.skip>
            </properties>
            <build>
            </build>
        </profile>
    </profiles>
</project>
