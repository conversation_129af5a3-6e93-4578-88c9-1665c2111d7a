# Modernized Redis Locking Module Documentation

Welcome to the documentation for the modernized Redis Locking Module. This module provides a robust and efficient distributed locking mechanism built on top of Redis. This documentation details the architecture, implementation, and usage of the module, reflecting the significant improvements made during the recent refactoring effort.

**The content of this documentation is mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md` and implemented in the module.**

## Overview

The modernized Redis Locking Module addresses limitations in the previous implementation by adopting a non-polling lock acquisition strategy, leveraging Redis Lua scripts for atomic operations, and providing a clear and consistent API with comprehensive exception handling.

## Key Features and Improvements

*   **Non-Polling Lock Acquisition**: Replaces inefficient polling with a notification-based mechanism using Redis Pub/Sub and semaphores, significantly reducing Redis load and client-side resource consumption.
*   **Atomic Operations with Lua Scripts**: Utilizes Lua scripts for critical operations like lock acquisition, release, and extension to ensure atomicity and prevent race conditions.
*   **Comprehensive Exception Handling**: Defines a clear exception hierarchy and uses a dedicated error handler (`RedisLockErrorHandler`) to translate low-level Redis errors into meaningful module-specific exceptions.
*   **Reliable Messaging**: Employs Redis Pub/Sub for efficient unlock notifications, supported by a fallback mechanism to handle potential missed messages.
*   **Well-Defined Redis Key Schema**: Uses a structured key schema with prefixes, namespaces, bucket names, and hash tags (including Redis HashMaps for reentrant lock data) to organize lock data in Redis and ensure compatibility with Redis Cluster.
*   **Lock Watchdog**: Includes a background mechanism to automatically extend lock leases, preventing premature expiration of locks held by active clients.
*   **Metrics**: Exposes detailed metrics via Micrometer for monitoring lock usage, contention, and performance.
*   **Flexible Configuration**: Allows extensive configuration through Spring Boot's `@ConfigurationProperties` (managed by the `RedisLockProperties` class) and lock buckets.
*   **Blocking `lock()` Method**: The `lock()` method adheres to the `java.util.concurrent.Lock` interface by blocking indefinitely until the lock is acquired or interrupted. The `defaultTimeout` property is not used in this method.

## Documentation Structure

This documentation is organized into the following sections:

*   [Architecture Overview](architecture_overview.md): High-level view of the module's structure and key principles.
*   [Configuration](configuration.md): Details on how to configure the module using `RedisLockProperties` and lock buckets.
*   [Exception Handling](exception_handling.md): Explanation of the exception hierarchy and handling strategy.
*   [Implementation Details](implementation_details.md): In-depth look at the core components and mechanisms.
*   [Lock Acquisition Mechanism](lock_acquisition.md): Detailed description of the non-polling acquisition process and flowcharts.
*   [Lua Scripts](lua_scripts.md): Documentation for the key Lua scripts used for atomic operations.
*   [Messaging](messaging.md): Explanation of the Redis Pub/Sub mechanism for unlock notifications.
*   [Redis Key Schema](redis_key_schema.md): Details on the structure and usage of Redis keys, including HashMaps for reentrancy.
*   [Watchdog](watchdog.md): Documentation for the lock lease extension watchdog.
*   [Metrics](metrics.md): Details on the metrics exposed by the module.
*   [Testing Strategy](testing_strategy.md): Overview of the testing approach for the module.
*   [Migration Notes](migration_notes.md): Guidance for migrating from the previous implementation.
*   [Modernization Assessment](modernization_assessment.md): The initial assessment that drove the refactoring effort.
*   [Performance Considerations](performance_considerations.md): Analysis of performance characteristics and optimization tips.

We encourage you to explore these documents to gain a comprehensive understanding of the modernized Redis Locking Module.