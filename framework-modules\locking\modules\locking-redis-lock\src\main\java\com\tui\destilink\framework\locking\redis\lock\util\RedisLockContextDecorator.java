package com.tui.destilink.framework.locking.redis.lock.util;

import com.tui.destilink.framework.core.logging.context.AbstractContextMapDecorator;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * Context decorator for Redis lock operations that extends the framework's
 * AbstractContextMapDecorator pattern. This decorator manages MDC context
 * specific to Redis lock operations and integrates with Virtual Thread execution.
 * 
 * <p>This decorator follows the established framework pattern for context management
 * and provides lock-specific context keys for better traceability in logs.</p>
 * 
 * <p>Usage example:</p>
 * <pre>{@code
 * try (var scope = RedisLockContextDecorator.create()
 *         .lockKey("user:123:profile")
 *         .lockOperation("acquire")
 *         .createClosableScope()) {
 *     // Lock operation code here
 *     // MDC context will be automatically managed
 * }
 * }</pre>
 * 
 * @since 1.0.0
 */
public class RedisLockContextDecorator extends AbstractContextMapDecorator<RedisLockContextDecorator> {

    private static final String LOCK_KEY_SUFFIX = "lock.key";
    private static final String LOCK_OPERATION_SUFFIX = "lock.operation";
    private static final String LOCK_OWNER_SUFFIX = "lock.owner";
    private static final String LOCK_BUCKET_SUFFIX = "lock.bucket";
    private static final String LOCK_TTL_SUFFIX = "lock.ttl";
    private static final String LOCK_RETRY_COUNT_SUFFIX = "lock.retryCount";
    private static final String LOCK_EXECUTION_TIME_SUFFIX = "lock.executionTime";

    // Property instances for fluent API
    private final Property<String> lockKey;
    private final Property<String> lockOperation;
    private final Property<String> lockOwner;
    private final Property<String> lockBucket;
    private final Property<Long> lockTtl;
    private final Property<Integer> lockRetryCount;
    private final Property<Long> lockExecutionTime;

    // Set of all keys managed by this decorator
    private final Set<String> keys;

    /**
     * Private constructor - use factory methods to create instances.
     */
    private RedisLockContextDecorator() {
        super("lock.");
        
        // Initialize properties
        this.lockKey = buildStringProperty(LOCK_KEY_SUFFIX);
        this.lockOperation = buildStringProperty(LOCK_OPERATION_SUFFIX);
        this.lockOwner = buildStringProperty(LOCK_OWNER_SUFFIX);
        this.lockBucket = buildStringProperty(LOCK_BUCKET_SUFFIX);
        this.lockTtl = buildLongProperty(LOCK_TTL_SUFFIX);
        this.lockRetryCount = buildIntegerProperty(LOCK_RETRY_COUNT_SUFFIX);
        this.lockExecutionTime = buildLongProperty(LOCK_EXECUTION_TIME_SUFFIX);
        
        // Define all keys managed by this decorator
        this.keys = Set.of(
            lockKey.getKey(),
            lockOperation.getKey(),
            lockOwner.getKey(),
            lockBucket.getKey(),
            lockTtl.getKey(),
            lockRetryCount.getKey(),
            lockExecutionTime.getKey()
        );
    }

    /**
     * Factory method to create a new RedisLockContextDecorator instance.
     * 
     * @return a new RedisLockContextDecorator instance
     */
    public static RedisLockContextDecorator create() {
        return new RedisLockContextDecorator();
    }

    /**
     * Sets the lock key in the MDC context.
     * 
     * @param key the Redis lock key
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockKey(String key) {
        lockKey.put(key);
        return this;
    }

    /**
     * Sets the lock operation in the MDC context.
     * 
     * @param operation the lock operation (e.g., "acquire", "release", "extend")
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockOperation(String operation) {
        lockOperation.put(operation);
        return this;
    }

    /**
     * Sets the lock owner in the MDC context.
     * 
     * @param owner the lock owner identifier
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockOwner(String owner) {
        lockOwner.put(owner);
        return this;
    }

    /**
     * Sets the lock bucket in the MDC context.
     * 
     * @param bucket the lock bucket name
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockBucket(String bucket) {
        lockBucket.put(bucket);
        return this;
    }

    /**
     * Sets the lock TTL in the MDC context.
     * 
     * @param ttl the lock time-to-live in milliseconds
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockTtl(Long ttl) {
        lockTtl.put(ttl);
        return this;
    }

    /**
     * Sets the lock retry count in the MDC context.
     * 
     * @param retryCount the number of retry attempts
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockRetryCount(Integer retryCount) {
        lockRetryCount.put(retryCount);
        return this;
    }

    /**
     * Sets the lock execution time in the MDC context.
     * 
     * @param executionTime the execution time in milliseconds
     * @return this decorator for method chaining
     */
    public RedisLockContextDecorator lockExecutionTime(Long executionTime) {
        lockExecutionTime.put(executionTime);
        return this;
    }

    /**
     * Gets the current lock key from MDC context.
     * 
     * @return the current lock key, or null if not set
     */
    public String getLockKey() {
        return lockKey.get();
    }

    /**
     * Gets the current lock operation from MDC context.
     * 
     * @return the current lock operation, or null if not set
     */
    public String getLockOperation() {
        return lockOperation.get();
    }

    /**
     * Gets the current lock owner from MDC context.
     * 
     * @return the current lock owner, or null if not set
     */
    public String getLockOwner() {
        return lockOwner.get();
    }

    /**
     * Gets the current lock bucket from MDC context.
     * 
     * @return the current lock bucket, or null if not set
     */
    public String getLockBucket() {
        return lockBucket.get();
    }

    /**
     * Gets the current lock TTL from MDC context.
     * 
     * @return the current lock TTL, or null if not set
     */
    public Long getLockTtl() {
        return lockTtl.get();
    }

    /**
     * Gets the current lock retry count from MDC context.
     * 
     * @return the current lock retry count, or null if not set
     */
    public Integer getLockRetryCount() {
        return lockRetryCount.get();
    }

    /**
     * Gets the current lock execution time from MDC context.
     * 
     * @return the current lock execution time, or null if not set
     */
    public Long getLockExecutionTime() {
        return lockExecutionTime.get();
    }

    @Override
    protected Set<String> getKeys() {
        return keys;
    }

    /**
     * Utility class for common Redis lock context operations.
     */
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Operations {
        
        public static final String ACQUIRE = "acquire";
        public static final String RELEASE = "release";
        public static final String EXTEND = "extend";
        public static final String REFRESH = "refresh";
        public static final String CHECK = "check";
        public static final String FORCE_RELEASE = "force_release";
        
        /**
         * Creates a context decorator for lock acquisition operations.
         * 
         * @param key the lock key
         * @param owner the lock owner
         * @param bucket the lock bucket
         * @return configured context decorator
         */
        public static RedisLockContextDecorator forAcquire(String key, String owner, String bucket) {
            return create()
                .lockKey(key)
                .lockOperation(ACQUIRE)
                .lockOwner(owner)
                .lockBucket(bucket);
        }
        
        /**
         * Creates a context decorator for lock release operations.
         * 
         * @param key the lock key
         * @param owner the lock owner
         * @param bucket the lock bucket
         * @return configured context decorator
         */
        public static RedisLockContextDecorator forRelease(String key, String owner, String bucket) {
            return create()
                .lockKey(key)
                .lockOperation(RELEASE)
                .lockOwner(owner)
                .lockBucket(bucket);
        }
        
        /**
         * Creates a context decorator for lock extension operations.
         * 
         * @param key the lock key
         * @param owner the lock owner
         * @param bucket the lock bucket
         * @param ttl the new TTL
         * @return configured context decorator
         */
        public static RedisLockContextDecorator forExtend(String key, String owner, String bucket, Long ttl) {
            return create()
                .lockKey(key)
                .lockOperation(EXTEND)
                .lockOwner(owner)
                .lockBucket(bucket)
                .lockTtl(ttl);
        }
    }
}