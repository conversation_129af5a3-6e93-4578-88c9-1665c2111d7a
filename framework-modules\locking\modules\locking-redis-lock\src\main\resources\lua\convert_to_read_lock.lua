-- KEYS[1] stampedLockDataKey (e.g., prefix:bucket:__locks__:{myStampedLock}:stamped)
-- KEYS[2] responseCacheKey
-- ARGV[1] requestUuid
-- ARGV[2] writeStampPayload (e.g., ownerId:uuid from the write stamp being converted)
-- ARGV[3] lockOwnerId
-- ARGV[4] newLeaseTimeMs
-- ARGV[5] responseCacheTTLSeconds

local cachedResult = redis.call('get', KEYS[2]);
if cachedResult ~= false then
    if cachedResult == 'nil' then return nil else return cachedResult end;
end;

local writeOwnerId = redis.call('hget', KEYS[1], 'write_owner_id');
local writeReentrancyCount = tonumber(redis.call('hget', KEYS[1], 'write_reentrancy_count'));

if (writeOwnerId == false or writeOwnerId ~= ARGV[3]) then
    -- Not held by this owner, or no write lock
    redis.call('set', KEYS[2], '0', 'px', ARGV[5]); -- Cache 0 for failure
    return '0';
end;

if (writeReentrancyCount == nil or writeReentrancyCount <= 1) then
    -- Fully releasing write lock (or last reentrant hold)
    redis.call('hdel', KEYS[1], 'write_owner_id', 'write_reentrancy_count');
    redis.call('hincrby', KEYS[1], 'read_holders', 1); -- Increment read holders
    -- Version does not change on downgrade to read lock
    redis.call('pexpire', KEYS[1], ARGV[4]); -- Set new lease for read lock

    local currentVersion = redis.call('hget', KEYS[1], 'version');
    if (currentVersion == false) then currentVersion = 0 end; -- Default to 0 if not set
    local newStamp = 'R:' .. currentVersion;
    redis.call('set', KEYS[2], newStamp, 'px', ARGV[5]);
    return newStamp;
else
    -- Still held reentrantly by writer, cannot downgrade fully
    redis.call('set', KEYS[2], '0', 'px', ARGV[5]); -- Cache 0 for failure
    return '0';
end;