-- Redis Lock Acquisition Script
-- This script attempts to acquire a lock with a given key, value, and TTL
-- KEYS[1] - The lock key
-- ARGV[1] - The lock value (typically a unique identifier for the lock holder)
-- ARGV[2] - The lock TTL in milliseconds

-- Check if the key exists and has the same value
local current = redis.call('get', KEYS[1])
if current == ARGV[1] then
    -- Lock refresh - extend TTL
    redis.call('pexpire', KEYS[1], ARGV[2])
    return 1
elseif current == false then
    -- Lock acquisition - set key with TTL
    redis.call('set', KEYS[1], ARGV[1], 'PX', ARGV[2])
    return 1
else
    -- Lock is held by someone else
    return 0
end