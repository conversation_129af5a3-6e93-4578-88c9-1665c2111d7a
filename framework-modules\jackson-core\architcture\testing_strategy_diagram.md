# Redis Locking Module: Testing Strategy

This diagram illustrates the testing strategy for the `locking-redis-lock` module.

```mermaid
classDiagram
    %% Test Types
    class UnitTests {
        <<Test Category>>
        +Focus: Individual components in isolation
        +Dependencies: Mocked
        +Naming: *Test
        +Location: src/test/java
    }
    
    class IntegrationTests {
        <<Test Category>>
        +Focus: Component interactions
        +Dependencies: Real Redis
        +Naming: *IT
        +Location: src/test/java
    }
    
    class ApplicationTests {
        <<Test Category>>
        +Focus: End-to-end scenarios
        +Dependencies: Real Redis + Application Context
        +Naming: *ApplicationTest
        +Location: framework-test-applications/
    }
    
    %% Test Support
    class RedisTestSupport {
        <<@TestSupport>>
        +configureRedisForTest(): void
        +cleanupRedisAfterTest(): void
    }
    
    class TestRedisConfiguration {
        <<@Configuration>>
        +redisConnectionFactory(): RedisConnectionFactory
        +redisTemplate(): RedisTemplate
    }
    
    class TestUtils {
        <<@UtilityClass>>
        +generateTestClassId(): String
        +generateReproducibleUUID(): UUID
        +waitForCondition(condition: Supplier~Boolean~, timeoutMs: long): boolean
    }
    
    %% Test Fixtures
    class LockTestFixtures {
        <<@UtilityClass>>
        +DEFAULT_LOCK_NAME: String
        +DEFAULT_BUCKET: String
        +DEFAULT_OWNER_ID: String
        +createTestLockKey(name: String, bucket: String): String
        +createTestLockConfig(): LockBucketConfig
    }
    
    %% Test Base Classes
    class AbstractRedisLockTest {
        <<Abstract>>
        #redisTemplate: RedisTemplate
        #lockComponentRegistry: LockComponentRegistry
        #redisLockOperations: RedisLockOperations
        #lockFactory: RedisLockFactory
        +setUp(): void
        +tearDown(): void
        #createTestLock(name: String): RedisReentrantLock
        #assertLockExists(lockKey: String): void
        #assertLockDoesNotExist(lockKey: String): void
        #assertLockOwnedBy(lockKey: String, ownerId: String): void
    }
    
    class AbstractRedisReadWriteLockTest {
        <<Abstract>>
        #redisTemplate: RedisTemplate
        #lockComponentRegistry: LockComponentRegistry
        #redisLockOperations: RedisLockOperations
        #lockFactory: RedisLockFactory
        +setUp(): void
        +tearDown(): void
        #createTestReadWriteLock(name: String): RedisReadWriteLock
        #assertReadLockExists(lockKey: String, readerId: String): void
        #assertWriteLockExists(lockKey: String, writerId: String): void
        #assertReadLockCount(lockKey: String, count: int): void
    }
    
    class AbstractRedisStampedLockTest {
        <<Abstract>>
        #redisTemplate: RedisTemplate
        #lockComponentRegistry: LockComponentRegistry
        #redisLockOperations: RedisLockOperations
        #lockFactory: RedisLockFactory
        +setUp(): void
        +tearDown(): void
        #createTestStampedLock(name: String): RedisStampedLock
        #assertStampValid(lockKey: String, stamp: String): void
        #assertStampInvalid(lockKey: String, stamp: String): void
        #assertStampType(stamp: String, type: StampType): void
    }
    
    %% Specific Test Classes
    class RedisReentrantLockTest {
        +testLockUnlock(): void
        +testReentrancy(): void
        +testTryLockWithTimeout(): void
        +testLockInterruptibly(): void
        +testConcurrentLockAttempts(): void
    }
    
    class RedisReadWriteLockTest {
        +testReadLockUnlock(): void
        +testWriteLockUnlock(): void
        +testMultipleReaders(): void
        +testReaderBlockedByWriter(): void
        +testWriterBlockedByReader(): void
        +testReadReentrancy(): void
        +testWriteReentrancy(): void
    }
    
    class RedisStampedLockTest {
        +testWriteLock(): void
        +testReadLock(): void
        +testOptimisticRead(): void
        +testConvertReadToWrite(): void
        +testConvertWriteToRead(): void
        +testStampValidation(): void
    }
    
    class RedisStateLockTest {
        +testLockWithState(): void
        +testGetState(): void
        +testStateTransition(): void
        +testInvalidStateTransition(): void
    }
    
    class LockWatchdogTest {
        +testLeaseExtension(): void
        +testLeaseExpiration(): void
        +testUnregisterLock(): void
    }
    
    class UnlockMessageListenerTest {
        +testUnlockNotification(): void
        +testMultipleListeners(): void
    }
    
    %% Integration Tests
    class RedisLockIT {
        +testLockAcquisitionAndRelease(): void
        +testConcurrentLockAttempts(): void
        +testLockExpiration(): void
        +testWatchdogExtension(): void
    }
    
    class RedisReadWriteLockIT {
        +testConcurrentReaders(): void
        +testReaderWriterExclusion(): void
        +testReadLockExpiration(): void
        +testWriteLockExpiration(): void
    }
    
    class RedisStampedLockIT {
        +testOptimisticReadWithNoConflicts(): void
        +testOptimisticReadWithConflicts(): void
        +testLockModeConversions(): void
    }
    
    %% Application Tests
    class LockingApplicationTest {
        +testDistributedLockingAcrossInstances(): void
        +testReadWriteLockAcrossInstances(): void
        +testStampedLockAcrossInstances(): void
        +testLockRecoveryAfterCrash(): void
    }
    
    %% Test Environment
    class RedisTestContainer {
        <<Docker>>
        +Redis Server
        +Exposed Port: 6379
    }
    
    class TestApplication {
        <<Spring Boot>>
        +RedisLockAutoConfiguration
        +TestConfiguration
    }
    
    %% Relationships
    UnitTests <|-- RedisReentrantLockTest
    UnitTests <|-- RedisReadWriteLockTest
    UnitTests <|-- RedisStampedLockTest
    UnitTests <|-- RedisStateLockTest
    UnitTests <|-- LockWatchdogTest
    UnitTests <|-- UnlockMessageListenerTest
    
    IntegrationTests <|-- RedisLockIT
    IntegrationTests <|-- RedisReadWriteLockIT
    IntegrationTests <|-- RedisStampedLockIT
    
    ApplicationTests <|-- LockingApplicationTest
    
    AbstractRedisLockTest <|-- RedisReentrantLockTest
    AbstractRedisReadWriteLockTest <|-- RedisReadWriteLockTest
    AbstractRedisStampedLockTest <|-- RedisStampedLockTest
    AbstractRedisLockTest <|-- RedisStateLockTest
    
    RedisTestSupport --> TestRedisConfiguration : uses
    RedisTestSupport --> TestUtils : uses
    
    AbstractRedisLockTest --> LockTestFixtures : uses
    AbstractRedisReadWriteLockTest --> LockTestFixtures : uses
    AbstractRedisStampedLockTest --> LockTestFixtures : uses
    
    RedisLockIT --> RedisTestSupport : uses
    RedisReadWriteLockIT --> RedisTestSupport : uses
    RedisStampedLockIT --> RedisTestSupport : uses
    
    LockingApplicationTest --> TestApplication : uses
    TestApplication --> RedisTestContainer : connects to
    
    %% Notes
    note for RedisTestSupport "Provides Redis test environment setup"
    note for AbstractRedisLockTest "Base class for lock unit tests"
    note for LockTestFixtures "Common test data and utilities"
    note for RedisLockIT "Tests with real Redis instance"
```

## Testing Approach

The Redis Locking module follows a comprehensive testing strategy with multiple layers:

### 1. Unit Tests

Unit tests focus on testing individual components in isolation with mocked dependencies:

- **Scope**: Individual classes and methods
- **Dependencies**: Mocked using Mockito
- **Naming Convention**: `*Test` suffix
- **Location**: `src/test/java` mirroring the main package structure
- **Framework**: JUnit 5, Mockito, AssertJ

Key unit test classes:

- `RedisReentrantLockTest`: Tests for `RedisReentrantLock`
- `RedisReadWriteLockTest`: Tests for `RedisReadWriteLock`
- `RedisStampedLockTest`: Tests for `RedisStampedLock`
- `RedisStateLockTest`: Tests for `RedisStateLock`
- `LockWatchdogTest`: Tests for `LockWatchdog`
- `UnlockMessageListenerTest`: Tests for `UnlockMessageListener`
- `RedisLockOperationsTest`: Tests for `RedisLockOperations`
- `ScriptLoaderTest`: Tests for `ScriptLoader`

### 2. Integration Tests

Integration tests verify the interaction between components with a real Redis instance:

- **Scope**: Component interactions
- **Dependencies**: Real Redis instance (via test-support)
- **Naming Convention**: `*IT` suffix
- **Location**: `src/test/java` mirroring the main package structure
- **Framework**: JUnit 5, Spring Test, Redis Test Support

Key integration test classes:

- `RedisLockIT`: Integration tests for `RedisReentrantLock`
- `RedisReadWriteLockIT`: Integration tests for `RedisReadWriteLock`
- `RedisStampedLockIT`: Integration tests for `RedisStampedLock`
- `RedisStateLockIT`: Integration tests for `RedisStateLock`
- `LockWatchdogIT`: Integration tests for `LockWatchdog`

### 3. Application Tests

Application tests verify end-to-end scenarios with a complete application context:

- **Scope**: End-to-end functionality
- **Dependencies**: Real Redis instance, complete Spring context
- **Naming Convention**: `*ApplicationTest` suffix
- **Location**: `framework-test-applications/`
- **Framework**: Spring Boot Test, Redis Test Support

Key application test classes:

- `LockingApplicationTest`: Tests distributed locking across multiple application instances
- `ConcurrencyApplicationTest`: Tests concurrent access patterns
- `FailureRecoveryApplicationTest`: Tests recovery from failures

## Test Support

### RedisTestSupport

The `RedisTestSupport` annotation provides Redis test environment setup:

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@TestSupport
@Import(TestRedisConfiguration.class)
public @interface RedisTestSupport {
    String host() default "localhost";
    int port() default 6379;
    boolean cleanupAfterTest() default true;
}
```

Usage:

```java
@RedisTestSupport
@SpringBootTest
public class RedisLockIT {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RedisLockFactory lockFactory;
    
    // Tests using real Redis
}
```

### TestRedisConfiguration

The `TestRedisConfiguration` class provides Redis beans for testing:

```java
@Configuration
public class TestRedisConfiguration {
    @Bean
    public RedisConnectionFactory redisConnectionFactory(
            @Value("${spring.redis.host:localhost}") String host,
            @Value("${spring.redis.port:6379}") int port) {
        LettuceConnectionFactory factory = new LettuceConnectionFactory(host, port);
        factory.afterPropertiesSet();
        return factory;
    }
    
    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }
}
```

### TestUtils

The `TestUtils` class provides common test utilities:

```java
@UtilityClass
public class TestUtils {
    public String generateTestClassId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
    
    public UUID generateReproducibleUUID(String seed) {
        return UUID.nameUUIDFromBytes(seed.getBytes(StandardCharsets.UTF_8));
    }
    
    public boolean waitForCondition(Supplier<Boolean> condition, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (condition.get()) {
                return true;
            }
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }
}
```

## Test Patterns

### 1. Base Test Classes

Abstract base classes provide common functionality for lock tests:

```java
public abstract class AbstractRedisLockTest {
    protected RedisTemplate<String, String> redisTemplate;
    protected LockComponentRegistry lockComponentRegistry;
    protected RedisLockOperations redisLockOperations;
    protected RedisLockFactory lockFactory;
    
    @BeforeEach
    public void setUp() {
        // Setup common test environment
    }
    
    @AfterEach
    public void tearDown() {
        // Clean up after tests
    }
    
    protected RedisReentrantLock createTestLock(String name) {
        return lockFactory.createReentrantLock(name);
    }
    
    protected void assertLockExists(String lockKey) {
        assertThat(redisTemplate.hasKey(lockKey)).isTrue();
    }
    
    protected void assertLockDoesNotExist(String lockKey) {
        assertThat(redisTemplate.hasKey(lockKey)).isFalse();
    }
    
    protected void assertLockOwnedBy(String lockKey, String ownerId) {
        assertThat(redisTemplate.opsForValue().get(lockKey)).isEqualTo(ownerId);
    }
}
```

### 2. Test Fixtures

Test fixtures provide common test data:

```java
@UtilityClass
public class LockTestFixtures {
    public static final String DEFAULT_LOCK_NAME = "test-lock";
    public static final String DEFAULT_BUCKET = "test-bucket";
    public static final String DEFAULT_OWNER_ID = "test-owner";
    
    public String createTestLockKey(String name, String bucket) {
        return "lock:" + bucket + ":" + name;
    }
    
    public LockBucketConfig createTestLockConfig() {
        LockBucketConfig config = new LockBucketConfig();
        config.setLockTimeoutMs(1000);
        config.setLockLeaseTimeMs(2000);
        config.setRetryCount(3);
        config.setRetryDelayMs(100);
        return config;
    }
}
```

### 3. Concurrent Testing

Concurrent tests verify behavior under concurrent access:

```java
@Test
public void testConcurrentLockAttempts() throws Exception {
    RedisReentrantLock lock = createTestLock(DEFAULT_LOCK_NAME);
    int threadCount = 10;
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch endLatch = new CountDownLatch(threadCount);
    AtomicInteger successCount = new AtomicInteger(0);
    
    for (int i = 0; i < threadCount; i++) {
        new Thread(() -> {
            try {
                startLatch.await();
                if (lock.tryLock(500, TimeUnit.MILLISECONDS)) {
                    try {
                        successCount.incrementAndGet();
                    } finally {
                        lock.unlock();
                    }
                }
            } catch (Exception e) {
                // Handle exception
            } finally {
                endLatch.countDown();
            }
        }).start();
    }
    
    startLatch.countDown();
    endLatch.await(5, TimeUnit.SECONDS);
    
    assertThat(successCount.get()).isEqualTo(1);
}
```

### 4. Timeout Testing

Timeout tests verify behavior with timeouts:

```java
@Test
public void testLockTimeout() {
    RedisReentrantLock lock1 = createTestLock(DEFAULT_LOCK_NAME);
    RedisReentrantLock lock2 = createTestLock(DEFAULT_LOCK_NAME);
    
    lock1.lock();
    try {
        boolean acquired = lock2.tryLock(500, TimeUnit.MILLISECONDS);
        assertThat(acquired).isFalse();
    } finally {
        lock1.unlock();
    }
}
```

### 5. Failure Testing

Failure tests verify behavior under failure conditions:

```java
@Test
public void testLockAcquisitionFailure() {
    RedisReentrantLock lock = createTestLock(DEFAULT_LOCK_NAME);
    
    // Simulate Redis failure
    RedisConnectionFactory mockFactory = mock(RedisConnectionFactory.class);
    when(mockFactory.getConnection()).thenThrow(new RedisConnectionFailureException("Simulated failure"));
    
    RedisTemplate<String, String> failingTemplate = new RedisTemplate<>();
    failingTemplate.setConnectionFactory(mockFactory);
    failingTemplate.afterPropertiesSet();
    
    // Replace with failing template
    ReflectionTestUtils.setField(redisLockOperations, "redisTemplate", failingTemplate);
    
    assertThatThrownBy(() -> lock.lock())
        .isInstanceOf(LockConnectionException.class)
        .hasMessageContaining("Simulated failure");
}
```

## Test Environment

### 1. Local Development

For local development, tests use:

- Embedded Redis server (via test-support)
- Local Redis instance (via Docker)
- Redis Test Support module

### 2. CI/CD Pipeline

In the CI/CD pipeline, tests use:

- Redis container provided by the test infrastructure
- Redis Test Support module
- Automated test execution

### 3. Test Data Isolation

Tests ensure data isolation through:

- Unique key prefixes for each test class
- Cleanup after tests
- Separate Redis databases for different test types

## Test Coverage

The testing strategy ensures comprehensive coverage:

1. **Functional Coverage**:
   - All lock types (Reentrant, Read-Write, Stamped, State)
   - All lock operations (acquire, release, check)
   - All lock modes (exclusive, shared, optimistic)

2. **Non-Functional Coverage**:
   - Performance under load
   - Behavior under failure conditions
   - Concurrent access patterns
   - Resource cleanup

3. **Code Coverage**:
   - Target: >90% line coverage
   - Target: >85% branch coverage
   - Target: 100% coverage of critical paths

## Test Best Practices

1. **Test Independence**:
   - Each test should be independent and self-contained
   - Tests should not depend on the order of execution
   - Tests should clean up after themselves

2. **Test Readability**:
   - Use descriptive test names
   - Follow the Arrange-Act-Assert pattern
   - Use helper methods for common operations

3. **Test Performance**:
   - Keep tests fast
   - Use appropriate timeouts
   - Minimize external dependencies

4. **Test Maintainability**:
   - Use base classes for common functionality
   - Use fixtures for common test data
   - Keep tests focused on a single aspect