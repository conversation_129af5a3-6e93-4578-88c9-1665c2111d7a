# Unlock Messaging (Pub/Sub) Mechanism

```mermaid
sequenceDiagram
    participant ReleasingClientApp
    participant RedisLockService (Release)
    participant RedisLockOperationsImpl (Release)
    participant Sc<PERSON>t<PERSON>oader (Release)
    participant RedisConnection (Publish)
    participant UnlockMessageListenerManager
    participant UnlockM<PERSON>ageListener (Subscribe)
    participant WaitingClientApp
    participant RedisLockService (Acquire Attempt)

    ReleasingClientApp->>+RedisLockService (Release): release(lockInfo)
    RedisLockService (Release)->>+RedisLockOperationsImpl (Release): executeReleaseScript(lockKey, clientId)
    RedisLockOperationsImpl (Release)->>+ScriptLoader (Release): getScript("release.lua")
    ScriptLoader (Release)->>+RedisConnection (Publish): EVALSHA/EVAL (release.lua)
    RedisConnection (Publish)-->>-ScriptLoader (Release): Script Result (includes unlock_message_sent flag)
    ScriptLoader (Release)-->>-RedisLockOperationsImpl (Release): Script Result
    RedisLockOperationsImpl (Release)-->>-RedisLockService (Release): Release Status
    RedisLockService (Release)->>RedisLockRegistry: unregisterLock(lockInfo)
    opt Watchdog Enabled
        RedisLockService (Release)->>LockWatchdog: cancelHeartbeat(lockInfo)
    end
    RedisLockService (Release)-->>-ReleasingClientApp: Release Successful

    Note over RedisConnection (Publish), RedisMessageListener (Subscribe): Redis Pub/Sub Channel (e.g., destilink:fw:lock:unlock:myLockName)

    RedisConnection (Publish)-->>UnlockMessageListenerManager: Publishes Unlock Message for 'myLockName'
    UnlockMessageListenerManager->>UnlockMessageListener (Subscribe): Dispatches notification
    UnlockMessageListener (Subscribe)->>+WaitingClientApp: Unlock Notification for 'myLockName'
    WaitingClientApp->>+RedisLockService (Acquire Attempt): Retry acquire(myLockName, ...)
    Note over RedisLockService (Acquire Attempt): Proceeds with lock acquisition attempt (as in 3_lock_acquisition_flow.md)
    RedisLockService (Acquire Attempt)-->>-WaitingClientApp: LockInfo (success or failure)


classDiagram
    class UnlockMessageListener {
        +LettuceMessageListenerContainer listenerContainer
        +RedisLockService redisLockService
        +String unlockChannelPattern
        +void onMessage(Message message, byte[] pattern)
    }
    note for UnlockMessageListener "Listens to 'destilink:fw:lock:unlock:*' by default"

    class RedisLockService {
        +void subscribeToUnlock(String lockName)
        +void notifyUnlock(String lockName)
    }
    UnlockMessageListener --> RedisLockService : notifies

    class LettuceMessageListenerContainer
    class Message

```
