# RedisStateLock Activity Diagram

RedisStateLock is a conditional lock implementation that associates a state value with the lock. The lock can only be acquired if the state stored in Redis matches an expected value.

## Key Features

- Conditional Acquisition: Lock acquisition depends on the current state matching the expected state
- State Management: Provides methods to get, initialize, and update the associated state
- Atomic State Update on Release: The state can be atomically updated when the lock is released
- State TTL: The Redis key storing the state has its own configurable time-to-live

## Activity Diagram

```mermaid
flowchart TD
    %% RedisStateLock Main Operations
    start([Start]) --> lockMethod["lock()"]
    lockMethod --> checkInterrupted{"Thread\ninterrupted?"}
    checkInterrupted -- Yes --> throwInterrupted[Throw InterruptedException]
    checkInterrupted -- No --> tryAcquire["tryAcquireLock(requestUuid, lockOwnerId)"]
    
    tryAcquire --> checkResult{"Result\nnull?"}
    checkResult -- Yes --> registerWatchdog["Register with LockWatchdog"]
    checkResult -- No --> checkStateMismatch{"Result\n== -1?"}
    
    checkStateMismatch -- Yes --> throwStateMismatch[Throw StateMismatchException]
    checkStateMismatch -- No --> checkStateNotFound{"Result\n== -2?"}
    
    checkStateNotFound -- Yes --> throwStateNotFound[Throw StateNotFoundException]
    checkStateNotFound -- No --> waitAndRetry["Wait for retry interval"]
    waitAndRetry --> checkTimeout{"Timeout\nreached?"}
    checkTimeout -- Yes --> throwTimeout[Throw LockTimeoutException]
    checkTimeout -- No --> tryAcquire
    
    %% Unlock flow
    unlockMethod["unlock()"] --> checkHeldByThread{"Lock held by\ncurrent thread?"}
    checkHeldByThread -- No --> throwIllegalMonitor[Throw IllegalMonitorStateException]
    checkHeldByThread -- Yes --> releaseLock["releaseLock(requestUuid, lockOwnerId)"]
    releaseLock --> unregisterWatchdog["Unregister from LockWatchdog"]
    
    %% Unlock with state flow
    unlockWithState["unlock(newState)"] --> validateState{"newState\n!= null?"}
    validateState -- No --> throwIllegalArg[Throw IllegalArgumentException]
    validateState -- Yes --> checkHeldByThread2{"Lock held by\ncurrent thread?"}
    checkHeldByThread2 -- No --> throwIllegalMonitor2[Throw IllegalMonitorStateException]
    checkHeldByThread2 -- Yes --> releaseLockWithState["releaseLockInternal(requestUuid, lockOwnerId, newState)"]
    releaseLockWithState --> checkReleaseResult{"Result\n== 1?"}
    checkReleaseResult -- Yes --> unregisterWatchdog2["Unregister from LockWatchdog"]
    checkReleaseResult -- No --> throwLockRelease[Throw LockReleaseException]
    
    %% State operations
    getState["getState()"] --> executeGetState["Execute 'manage_state' Lua script"]
    executeGetState --> returnState[Return current state]
    
    updateState["updateState(newState)"] --> validateUpdateState{"newState\n!= null?"}
    validateUpdateState -- No --> throwIllegalArg2[Throw IllegalArgumentException]
    validateUpdateState -- Yes --> executeUpdateState["Execute 'update_state' Lua script"]
    executeUpdateState --> checkUpdateResult{"Result\n== 1?"}
    checkUpdateResult -- Yes --> returnSuccess[Return]
    checkUpdateResult -- No --> throwLockAcq[Throw LockAcquisitionException]
    
    getOrInitState["getOrInitializeState(initialState)"] --> validateInitState{"initialState\n!= null?"}
    validateInitState -- No --> throwIllegalArg3[Throw IllegalArgumentException]
    validateInitState -- Yes --> executeGetOrInit["Execute 'get_or_initialize_state' Lua script"]
    executeGetOrInit --> returnInitState[Return state]