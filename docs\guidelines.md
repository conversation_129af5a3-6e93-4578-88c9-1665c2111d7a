# Destilink Framework Development Guidelines

## Introduction

The Destilink Framework is an **opinionated** Spring Boot abstraction designed to standardize and accelerate the development of microservices and cronjobs within the TUI environment. Its core philosophy is "plug-n-play": providing pre-configured, tested, and opinionated solutions for common cross-cutting concerns and integrations, allowing application developers to focus on business logic.

These guidelines are for developers contributing to the *framework itself*. They provide a practical, step-by-step approach to developing new framework modules, extending existing ones, managing releases, and writing tests. Consistency, maintainability, testability, operational excellence, and adherence to the framework's opinions are paramount.

If anything is unclear, *ask* the framework team!

## 1. Project Structure

The framework is a multi-module Maven project structured to promote modularity, reusability, and independent versioning of functional areas.

```
destilink-framework/
├── pom.xml                           (Top-level Parent POM - DO NOT MODIFY)
├── framework-dependencies-parent/    (Central Dependency Versions/Exclusions - MODIFY CAREFULLY)
│   └── pom.xml
├── framework-bom/                    (Bill of Materials - Recommended Dependencies - MODIFY CAREFULLY)
│   └── pom.xml
├── framework-build-parent/           (Parent POM for common build config/plugins - DO NOT MODIFY DIRECTLY)
│   ├── pom.xml
│   └── root/pom.xml
├── framework-build-parent-ms/        (Parent POM for Microservice builds - DO NOT MODIFY DIRECTLY)
│   ├── pom.xml
│   ├── ms-parent/pom.xml             (Recommended parent for MS applications)
│   └── ms-parent-legacy/pom.xml      (Legacy parent for MS applications)
├── framework-build-parent-cronjob/   (Parent POM for Cronjob builds - DO NOT MODIFY DIRECTLY)
│   ├── pom.xml
│   ├── cronjob-parent/pom.xml        (Recommended parent for Cronjob applications)
│   └── cronjob-parent-legacy/pom.xml (Legacy parent for Cronjob applications)
├── framework-modules/              (Contains all functional framework code)
│   ├── module-name/                  (e.g., core, web, aws, caching)
│   │   ├── modules/                  (Optional: for sub-modules within a functional area)
│   │   │   ├── sub-module-name/      (e.g., web-core, aws-sqs, locking-redis-lock)
│   │   │   │   ├── src/main/java/...
│   │   │   │   ├── src/main/resources/...
│   │   │   │   └── pom.xml
│   │   │   └── pom.xml               (Parent POM for sub-modules)
│   │   ├── src/main/java/...         (For modules without sub-modules)
│   │   ├── src/main/resources/...
│   │   └── pom.xml                   (Parent POM for the functional area or module)
│   └── ...
├── framework-test-applications/     (Integration test applications)
│   ├── test-app-name/               (e.g., core-tests, aws-localstack-messaging-tests)
│   │   ├── src/main/java/...        (Minimal application setup)
│   │   ├── src/test/java/...        (Integration tests)
│   │   ├── src/test/resources/...   (Test-specific configuration)
│   │   └── pom.xml                  (Inherits from test-build-parent-ms/cronjob)
│   └── ...
├── framework-aggregated-report/      (Aggregates reports, primarily for CI - DO NOT MODIFY)
│   └── pom.xml
└── docs/                             (Framework-level documentation)
    └── guidelines.md
    └── collisions.md
    └── ...
└── utils/                            (Supporting infrastructure, e.g., Docker Compose for test services)
    └── ...
└── .gitlab-ci.yml                    (CI Pipeline definition)
└── lombok.config                     (Lombok configuration)
└── .mvn/                             (Maven wrapper configuration)
```

*   **Top-level POMs (`destilink-framework/pom.xml`, `framework-build-parent/pom.xml`, `framework-build-parent-ms/pom.xml`, `framework-build-parent-cronjob/pom.xml`, `framework-aggregated-report/pom.xml`):** These control project-wide configuration, dependency management imports, and build profiles. **Avoid modifying these directly.** Contact the framework team if changes are needed.
*   **`framework-dependencies-parent/pom.xml`:** This is where **centralized version management** for third-party and Spring dependencies occurs. If you need to introduce a new external dependency or update an existing one's version, this is the primary place to add/update it, ensuring it's managed for all framework modules.
*   **`framework-bom/pom.xml`:** This is the Bill of Materials. It lists the *recommended* framework modules and other dependencies with their versions *inherited* from `framework-dependencies-parent`. Applications should import this BOM to get consistent dependency versions. Add new framework modules here.
*   **`framework-modules/`:** Contains the core logic of the framework. Each direct child directory usually represents a functional area (e.g., `aws`, `web`, `locking`). Some areas are further broken down into sub-modules (`modules/`) for finer-grained dependency management and optionality (e.g., `web-core`, `web-client`, `web-server` under `web/modules`).
*   **`framework-test-applications/`:** These are dedicated, minimal Spring Boot applications used *solely* for integration tests that involve interactions between *multiple* framework modules. They depend on the specific framework modules being tested. They are *not* for unit tests or module-internal integration tests.
*   **`utils/`:** Contains helper files for setting up local development/testing environments, such as Docker Compose files for services like Redis, PostgreSQL, Keycloak, LocalStack, and OpenTelemetry Collector. These are part of the framework's test infrastructure.
*   **`docs/`:** Contains framework-level documentation, including these guidelines.

**Module Naming:**

*   Modules use lowercase and hyphens (e.g., `web-core`, `aws-sqs`, `locking-redis-lock`).
*   Functional area parent modules are typically named after the area (e.g., `web`, `aws`, `locking`).
*   Sub-modules within a functional area are named `area-subarea` (e.g., `web-core`, `aws-sqs`).

**Package Structure:**

Within a module, use this consistent package structure:

```
com.tui.destilink.framework.<module>[.<submodule>]
├── config/          (Configuration classes and properties)
├── service/         (Business logic and service implementations)
├── exception/       (Custom exception classes)
├── util/            (Utility classes and helper methods)
├── model/           (Data transfer objects, entities - avoid complex domain models)
├── event/           (Event classes)
├── client/          (Client implementations, if applicable)
├── annotations/     (Custom annotations)
├── impl/            (Internal implementation details, not intended for direct use by application developers)
└── ...              (Other domain-specific packages)
```

*   **`config`:** `@ConfigurationProperties` classes and Spring `@Configuration` classes for auto-configuration.
*   **`impl`:** Internal implementation details, not intended for direct use by applications using the framework.

**Adding a New Module:**

1.  Determine the appropriate functional area under `framework-modules/`. If it's a new area, create a new directory (e.g., `framework-modules/new-area/`).
2.  Create a `pom.xml` for the new module/sub-module. It should inherit from the parent POM of its functional area (e.g., `framework-modules/new-area/pom.xml` or `framework-modules/existing-area/modules/new-sub-module/pom.xml`). The parent POMs (`framework-modules`, `framework-modules/existing-area/`) define common dependencies and build settings for their children.
3.  Add the new module/sub-module to the `<modules>` section of its parent `pom.xml`.
4.  Add the new module/sub-module to the `<modules>` section of `framework-modules/pom.xml` if it's a top-level module, or to the parent module's `pom.xml` if it's a sub-module.
5.  Add the new module/sub-module to `framework-bom/pom.xml` with its version (inherited from its parent) and `<scope>compile</scope>`.
6.  Create the standard directory structure (`src/main/java`, `src/main/resources`, `src/test/java`, `src/test/resources`).
7.  Create a `README.md` in the module's root directory, explaining its purpose, key features, configuration, and basic usage.

## 2. Core Concepts and Usage

### 2.1 Configuration

The framework is highly configurable through externalized properties, following a strict naming convention and leveraging Spring Boot's features.

*   **Property Naming:** All framework properties **must** start with `destilink.fw`. The structure `destilink.fw.<functional-area>.<module>[.<sub-module>].<property-name>` is used (e.g., `destilink.fw.web.core.error-responses.use-problem-details`, `destilink.fw.aws.sqs.logging.enabled`). Use kebab-case for property names (`my-property-name`).
*   **Configuration Classes:**
    *   Use `@ConfigurationProperties(prefix = "...")` to bind properties to Java classes.
    *   Use `@Validated` and Jakarta Validation annotations (`@NotNull`, `@NotBlank`, `@Pattern`, `@Min`, `@Max`, `@Valid`) for property validation.
    *   Provide sensible default values in the `@ConfigurationProperties` classes whenever possible.
    *   Use nested static classes for hierarchical properties.
    *   Add Javadoc to explain each property.
*   **Default Configuration Files:**
    *   Each module should provide sensible default configurations in `src/main/resources/`.
    *   Use the naming convention `NNNN-<module>-<optional-descriptor>.application.yml`, where `NNNN` is a 4-digit number indicating load order. Framework defaults typically use `1000-`. Properties intended for test environments use `.test.yml`. Properties specific to MS or CRONJOB app types use `.ms.yml` or `.cronjob.yml`.
    *   These files should only contain properties relevant to the module and their default values.
*   **Auto-Configuration:**
    *   Create a Spring `@Configuration` class, annotated with `@AutoConfiguration`, to define beans and configurations for your module.
    *   Use `@EnableConfigurationProperties` to enable binding for your configuration classes.
    *   Use `@ConditionalOn...` annotations (`@ConditionalOnClass`, `@ConditionalOnProperty`, `@ConditionalOnBean`, `@ConditionalOnMissingBean`) to control when your auto-configuration is applied. This makes modules optional.
    *   Register your auto-configuration class by adding its fully qualified name to `src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`. This is the standard Spring Boot mechanism for auto-configuration discovery (since 2.7).
*   **Configuration Metadata:** Create `src/main/resources/META-INF/additional-spring-configuration-metadata.json` to provide metadata about your configuration properties. This enables IDE auto-completion and documentation generation.

### 2.2 Logging

The framework enforces structured logging using SLF4J, Logback, and the Logstash Encoder, with custom context propagation and markers.

*   **Use SLF4J:** Always use `org.slf4j.Logger` for logging. **Do not** use Logback classes directly in application code or framework public APIs.
*   **Structured Logging (JSON):** The framework configures Logback to output JSON logs. Use MDC and markers to add structured data.
*   **MDC (Mapped Diagnostic Context):**
    *   Use `com.tui.destilink.framework.core.logging.context.decorator.AbstractContextDecorator` to create custom context decorators for adding contextual information (e.g., business process ID, event ID, lock name) to the MDC.
    *   Framework-provided decorators include `TripsContextDecorator` (for common TRIPS fields) and `LockContextDecorator` (for locking).
    *   **Always** use `try-with-resources` with `createClosableScope()` to manage the MDC scope and ensure values are cleared.
*   **Markers:**
    *   Use `net.logstash.logback.marker.Markers` to create markers.
    *   Use `com.tui.destilink.framework.core.logging.monitoring.MonitoringMarkerUtils` for Datadog monitoring markers (`monitoring` field).
    *   Use `com.tui.destilink.framework.core.logging.marker.ConstraintViolationMarker` for Jakarta Validation violations.
    *   Use `com.tui.destilink.framework.aws.sqs.logging.marker.SqsMessageMarker` for SQS message details.
    *   Use `com.tui.destilink.framework.aws.sns.logging.marker.SnsPublishMarker` for SNS publish details.
    *   Use `com.tui.destilink.framework.cloudevents.logging.marker.CloudEventsMarker` for CloudEvent details.
*   **Custom JSON Providers:** Extend `com.tui.destilink.framework.core.logging.customize.jsonprovider.AbstractLogstashMarkersJsonProvider` and register it via `LogbackCustomizer` (using `@AutoService`) if you need to customize the JSON output format.
*   **Exception Handling in Logs:**
    *   Use `com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException` or `MarkerNestedException` for custom exceptions to automatically include monitoring markers in logs.
    *   Use `com.tui.destilink.framework.core.logging.filter.mute.LogMutingException` and `LogMutingMarker` to selectively suppress logging for specific exceptions.
*   **Message Size Limiting:** The `LongMessagesConverter` (configured in `logback-spring.xml`) limits the size of log messages to prevent flooding. Use `LoggingUtils.limitToBytes()` if you need to manually truncate messages before logging.

### 2.3 Metrics

The framework integrates with Micrometer for metrics collection, pre-configuring StatsD and OpenTelemetry registries.

*   **Use Micrometer:** Inject and use the `io.micrometer.core.instrument.MeterRegistry` bean (provided by Spring Boot) to create and register meters (counters, gauges, timers, etc.).
*   **Common Tags:** Framework-level metrics and metrics you create should include common tags (`env`, `service`, `version`, `pod_name` in Kubernetes). These are automatically added by the framework's `MeterRegistryCustomizer` beans.
*   **StatsD Filtering:** The `statsd-filter` module allows filtering metrics sent to StatsD based on a whitelist (`destilink.fw.core.metrics.statsd.non-filtered-metrics`).

### 2.4 Tracing

The framework integrates with Datadog for distributed tracing using OpenTracing API and Micrometer Context Propagation.

*   **Automatic Tracing:** Tracing is largely automatic for common components (HTTP, messaging) via instrumentation libraries and framework-provided aspects. You should generally *not* need to interact directly with the tracing API (`io.opentracing.*`).
*   **Context Propagation:** Context (including tracing context and MDC) is automatically propagated across thread boundaries using Micrometer's `context-propagation`. This works out-of-the-box for common asynchronous patterns (CompletableFuture, Reactor). Custom context propagation can be integrated by extending `AbstractContextDecorator`.
*   **Adding Trace Spans:** If necessary, you can manually create and manage spans using `io.opentracing.util.GlobalTracer.get()`.
*   **Adding Error to Spans:** Use `com.tui.destilink.framework.core.tracing.TracingUtils.setErrorOnSpan()` to mark a span as erroneous and attach exception details.

### 2.5 Error Handling (Web Server)

The framework provides standardized error handling for REST APIs using Spring's `@ControllerAdvice` and Problem Details.

*   **Problem Details:** Use Spring's `org.springframework.http.ProblemDetail` for consistent, machine-readable error responses (RFC 7807). The framework's `ProblemDetailsExceptionHandler` and `SecurityProblemDetailsExceptionHandler` handle common exceptions and map them to Problem Details responses.
*   **Custom Exception Handlers:** Create `@ControllerAdvice` beans to handle specific exceptions. If using Problem Details, return `ResponseEntity<ProblemDetail>`. Ensure your advice has the correct `@Order` if you need it to run before or after framework handlers.
*   **Security Exception Handling:** `SecurityProblemDetailsExceptionHandler` provides default handling for `AuthenticationException` and `AccessDeniedException`, returning 401 and 403 Problem Details responses.

### 2.6 Validation

Use Jakarta Validation (`jakarta.validation.*`) annotations (`@NotNull`, `@NotBlank`, `@Valid`, etc.) for configuration validation (`@Validated` on `@ConfigurationProperties`) and general bean validation.

*   Framework components use `com.tui.destilink.framework.core.util.ValidationUtils.validateObjectThrowing()` for programmatic validation where needed.
*   Custom constraints can be defined and used.

## 3. Module-Specific Guidelines

*   **`core`:** Provides fundamental utilities, base classes, configuration loading (`EnvironmentPostProcessor`), logging infrastructure, metrics, and tracing setup. Foundational module.
*   **`jackson-core`:** Configures the `ObjectMapper` with common modules (JDK 8, JSR 310, Money, Blackbird) and default serialization/deserialization settings. Ensures consistent JSON handling.
*   **`web` (and sub-modules):** Provides HTTP client and server capabilities.
    *   **`web-core`:** Common web utilities, security defaults, Logbook integration setup.
    *   **`web-client`:** Configures `RestTemplate` (Apache HttpClient) and `WebClient` (Netty) with connection pooling, timeouts, logging, and tracing. Includes interceptors for propagating TRIPS/Bookhub headers.
    *   **`web-server`:** Configures the embedded web server, error handling, and filters. Includes `LoggingContextFilter` for setting MDC context for incoming requests.
    *   **`web-security-oauth2-server`:** Provides OAuth2 resource server configuration, JWT token validation, authority extraction (from `resource_access`), and user info retrieval (from UserInfo endpoint, with caching and fallback).
    *   **`web-openapi`:** Integrates Springdoc OpenAPI for API documentation generation (`/api-docs`) and Swagger UI (`/swagger-ui.html`). Can load metadata from a spec file and includes OAuth2 security scheme configuration.
*   **`async` (and sub-modules):** Provides asynchronous processing capabilities.
    *   **`async-core`:** Configures `ThrottledVirtualThreadsExecutor` for managing virtual threads with a concurrency limit, usable as Spring's `@Async` executor.
*   **`cloudevents`:** Provides support for CloudEvents v1.0, including serialization/deserialization, validation, and integration with AWS messaging (SNS/SQS) and S3 for Data References. Supports TUI-specific extensions (Business, PD, Generic Map).
*   **`aws` (and sub-modules):** Provides integration with various AWS services.
    *   **`aws-core`:** Basic AWS client configuration, region/credentials providers, and AWS Caller Identity information.
    *   **`aws-sns`:** Configures `SnsClient` and provides `SnsSender` for publishing messages to SNS topics. Includes logging and handling for FIFO topics and CloudEvents Data References.
    *   **`aws-sqs`:** Configures `SqsAsyncClient` and message listeners (`@SqsListener`). Includes custom message source/sink implementations for improved error handling, retryable messages, DLQ forwarding, FIFO ordering, logging, and tracing. Provides resolvers for queue URLs, ARNs, and tags.
    *   **`aws-s3`:** Configures `S3Client` and provides an `S3AssumingRoleCrtClientFactory` for creating S3 clients that assume a specific IAM role (useful for cross-account access). Integrates with `file-storage` for S3 resource handling.
    *   **`aws-elasticache-redis`:** Configures `RedisConnectionFactory` for AWS ElastiCache Redis clusters, including IAM authentication support. Integrates with `redis-core`.
    *   **`aws-opensearch`:** Configures OpenSearch client (`RestHighLevelClient`) with AWS SigV4 signing for authentication with AWS OpenSearch domains. Integrates with Spring Data OpenSearch.
    *   **`aws-msk-auth-iam`:** Provides configuration for Kafka clients to authenticate with AWS MSK using IAM (SASL/AWS_MSK_IAM). Configures Kafka properties based on AWS credentials and region.
    *   **`aws-aurora-postgresql`:** Configures DataSource for AWS Aurora PostgreSQL using the AWS JDBC Driver Wrapper for enhanced features like failover and IAM authentication. Integrates with JPA/Hibernate and Flyway.
*   **`file` (and sub-modules):** Provides abstractions for file storage and download.
    *   **`file-storage`:** Provides `StorageService` and `VersionedStorageService` interfaces and implementations for different backends (filesystem, S3, SFTP). Uses Spring `Resource` abstraction. Includes custom `ResourcePatternResolver` and `ResourceLoader` for SFTP.
    *   **`file-download`:** Provides `FileDownloadService` and `DownloadService` interfaces and implementations for different protocols (FILE/filesystem, FTP, FTPS, SFTP, DUMMY). Integrates with `file-storage` for storing downloaded files. Includes specific logic for Atcomres downloads.
*   **`locking` (and sub-modules):** Provides distributed locking mechanisms.
    *   **`locking-redis-lock`:** Provides distributed locks (`Lock`, `ReadWriteLock`, `StampedLock`, `RedisStateLock`) based on Redis. Uses Lua scripts for atomicity. Integrates with `redis-core` for connections, Resilience4j for retry/circuit breaker, and includes a watchdog for lease extension and Pub/Sub for unlock notifications. Uses a fluent builder API (`LockBucketRegistry.builder()`).
    *   **`locking-shedlock`:** Provides distributed scheduling locks using ShedLock with Redis as the backend. Integrates with `redis-core`.
*   **`redis-core`:** Configures the Lettuce Redis client, including connection pools (blocking, cycle), command timeouts, socket options, Lettuce client options, and keyspace prefixes. Provides `ClusterCommandExecutor` for executing commands and scripts on cluster nodes. Includes a `TopologyRefreshSynchronizer`. Provides utilities for Redis key formatting (`RedisKey`, `RedisKeyPrefix`).
*   **`resilience` (and sub-modules):** Provides integration with Resilience4j for common resilience patterns.
    *   **`resilience-retry`:** Configures Resilience4j Retry and integrates with Spring Boot's retry annotations. Includes a `CompositeRetryPredicate` allowing different modules to contribute retryable exception predicates.
    *   **`resilience-circuitbreaker`:** Configures Resilience4j Circuit Breaker and integrates with Spring Boot's annotations.

## 4. Testing

The `test-support` modules provide utilities and infrastructure for writing various types of tests, aiming to reduce reliance on manual setup of external dependencies within individual test classes.

### 4.1 Testing Environment Architecture

**IMPORTANT: CI/CD Compatibility Constraints**

* **`utils/` Directory:** Provides the **standard static development and CI testing environment** using Docker Compose. Services defined here (Redis, PostgreSQL, Keycloak, LocalStack, etc.) are the **primary target for integration tests** requiring external dependencies due to CI/CD compatibility. These are long-running services that persist between test runs.

* **Dynamic Testcontainers:** The use of dynamic `@Testcontainers` is **generally forbidden** for tests intended to run in the CI/CD pipeline due to compatibility issues. Do not use the Testcontainers library directly in your tests unless you have explicit approval for a specific local-only testing scenario.

* **Test-Support Modules:** These modules provide abstractions that connect to the static services in the `utils/` environment (or equivalent pre-provisioned services in CI). They handle connection details, isolation, and other cross-cutting concerns to simplify test setup.

### 4.2 Test Types and Best Practices

*   **Unit Tests:**
    *   Use JUnit 5, Mockito, AssertJ.
    *   Place in `src/test/java`, mirroring main package structure.
    *   Name classes `*Test`.
    *   Focus on testing individual units of code in isolation, mocking dependencies.
*   **Integration Tests (Module Internal):**
    *   Use `@SpringBootTest` to load a minimal Spring context for the module.
    *   **DO NOT use `@Testcontainers`** - instead, either:
        * Use a dedicated test-support module to connect to the static `utils/` environment (preferred)
        * Mock the external dependency if no test-support module exists
        * Create a new test-support module that integrates with the static `utils/` environment
    *   Place in `src/test/java`, mirroring main package structure.
    *   Name classes `*IT`.
*   **Integration Tests (Framework Test Applications):**
    *   These are full Spring Boot applications (`framework-test-applications/`) used to test interactions between *multiple* framework modules or complex scenarios requiring more setup.
    *   Depend on the specific framework modules required for the scenario.
    *   **Always use the `utils/` Docker Compose setup** for external services (Redis, PG, Keycloak, LocalStack).
    *   Leverage dedicated test-support modules to connect to these static services.
    *   Place tests in `src/test/java` within the test application module.
    *   Name classes `*IT`.
*   **Dedicated Test-Support Modules (`framework-modules/test-support/modules/`):**
    *   **`test-core`:** Provides core test utilities, including `@TestClassId` (injects unique ID for test class, useful for naming isolated resources like queues, buckets, databases) and utilities for managing test properties (`TestSupportPropertiesUtils`).
    *   **`redis-test-support`:** Provides `@RedisTestSupport` annotation to configure the Spring context to connect to the Redis instance from the static `utils/` environment. Handles unique keyspace prefixes and user/permissions for isolation.
    *   **`s-ftp-test-support`:** Provides `@FakeSFTPSupport` and `FakeSFTPServer` for testing SFTP interactions without a real server.
    *   **`keycloak-test-support`:** Provides `@KeycloakTestSupport` annotation to automatically set up a Keycloak realm, clients, and users (using `utils/keycloak` Docker Compose). Provides `KeycloakAdminService` for interacting with the test realm.
    *   **`kafka-test-support`:** Provides `@EmbeddedKafka` support (Spring Kafka) and context customization.
    *   **`postgresql-test-support`:** Provides `@EnablePostgresTestSupport` to automatically provision a dedicated PostgreSQL database and user per test class using the admin connection details (configured via environment variables/system properties, pointing to `utils/postgres` Docker Compose). Ensures test isolation and integrates with Spring Boot's DataSource and migration tools.
*   **Test Utilities:**
    *   `@TestClassId`: Inject into test classes to get a unique string derived from the class name, ideal for naming test-specific resources (e.g., `sqs-queue-${testClassId}`).
    *   `TestUtils.generateTestClassId()`: Programmatic access to generate the same ID.
    *   `TestUtils.generateReproducibleUUID()`: Create UUIDs deterministically from a string source.
    *   `NonContextTestEnvUtils`: Load and bind properties outside of a full Spring context.
    *   `RedisTestUtils`: Helpers for interacting with the Redis instance provided by `redis-test-support`.

### 4.3 When No Test-Support Module Exists

If your test requires an external dependency for which no test-support module exists, you have two options:

1. **Mock the dependency** - This is the preferred approach for simpler dependencies or when isolation is less critical.
2. **Create a new test-support module** - For complex dependencies or those used across multiple tests, create a new module that integrates with the static `utils/` environment.

**DO NOT** use the Testcontainers library directly as a fallback, as this will cause tests to fail in the CI/CD pipeline.

### 5. Code Style

Consistency and readability are key.

*   Follow standard Java naming conventions.
*   Use Lombok annotations (`@Data`, `@Builder`, `@RequiredArgsConstructor`, `@Slf4j`) to reduce boilerplate. Configure Lombok via `lombok.config`.
*   Use Jakarta Validation annotations for validation.
*   Add Javadoc to all public classes, methods, and fields.
*   Keep methods short and focused.
*   Use descriptive variable and method names.
*   Use structured logging (`MDCPrefixWrapper`, Markers).
*   Use try-with-resources for all closable objects.
*   Use `final` for all fields that are not reassigned.
*   Do not use wildcard imports.
*   Use constructor injection with `final` fields instead of `@Autowired` field injection.
*   Use fluent APIs where appropriate (e.g., builders).
*   Adhere to the configured code formatter (`formatter.xml`).

### 6. Documentation

Comprehensive documentation is crucial for framework adoption and maintenance.

*   Maintain detailed Javadoc for all public API elements, explaining purpose, parameters, return values, and exceptions.
*   Keep `README.md` files in each module up-to-date, explaining purpose, key features, configuration (with examples), and basic usage.
*   Update the `docs/` directory with architectural changes, new features, and conceptual guides.
*   Add a release note in `docs/releases/` for each new version.
*   Document any breaking changes clearly.

## 7. Contribution Guidelines

1.  **Fork and Branch:** Fork the repository and create a new branch from `main`. Use a descriptive branch name following conventional patterns (e.g., `feat/<feature-name>`, `fix/<bug-description>`, `chore/<task>`).
2.  **Implement Changes:** Write code following the established conventions and guidelines.
3.  **Add/Update Tests:** Write unit tests and integration tests to cover your changes. Ensure existing tests pass. Leverage test-support modules for external dependencies.
4.  **Update Documentation:** Update Javadoc, READMEs, and relevant documents in `docs/`.
5.  **Write Conventional Commits:** Use clear, concise commit messages following the Conventional Commits specification (e.g., `feat(module): add new feature`, `fix(module): resolve bug`). Include a body explaining the change and a footer referencing the Jira issue.
6.  **Create Pull Request:** Create a pull request to the `main` branch. Ensure your branch is up-to-date with `main`.
7.  **Code Review:** Participate in code reviews and address feedback promptly.
8.  **Merge:** Once approved and CI pipelines pass, your changes will be merged.

## 8. Version Handling

The framework uses a single version for all modules managed in the top-level `pom.xml`.

*   **Versioning Scheme:** `major.minor.patch-SNAPSHOT`
    *   `major`: Increment for backward-incompatible API changes.
    *   `minor`: Increment for new features or significant non-breaking changes.
    *   `patch`: Increment for bug fixes, minor updates, or dependency-only updates (including vulnerability fixes).
    *   `-SNAPSHOT`: Indicates an ongoing development build.
*   **Releases:** Releases are triggered by creating a Git tag in the format `v<major>_<minor>_<patch>`. The CI pipeline automatically builds and publishes release artifacts based on this tag.

**Scenarios:**

1.  **Develop a new module:** Follow steps in Section 1. Increment `minor` version if it adds a new functional area or significant capability. Increment `patch` if it's a smaller, optional utility.
2.  **Extend an existing module with new features:** Increment `minor` version.
3.  **Creating a regular release:** Ensure all desired features/fixes are merged into `main`. Create a Git tag `vX_Y_Z` where X.Y.Z is the next version based on the changes merged since the last release. Update the version in `pom.xml` to the next `X.Y.(Z+1)-SNAPSHOT`.
4.  **Creating a bug-fix release:** Ensure the bug fix is merged into `main`. Create a Git tag `vX_Y_Z` where Z is incremented for the patch version. Update the version in `pom.xml` to the next `X.Y.(Z+1)-SNAPSHOT`.
5.  **Creating a release with dependency updates only:** Increment `patch` version. Follow the bug-fix release process.
6.  **Develop another framework that uses the destilink-framework as parent:** This is not the intended use case. Other frameworks should *depend* on specific destilink-framework modules via the BOM, not inherit from its parent POMs. The build parent POMs are specifically for applications (MS/Cronjob) inheriting build configurations.
7.  **Develop a library with the destilink-framework as parent:** Similar to #6, libraries should depend on specific modules via the BOM.
8.  **Develop a microservice with the destilink-framework as parent:** Inherit from `framework-build-parent-ms/ms-parent`. Depend on specific framework modules via `framework-bom`.
9.  **Develop a cronjob with the destilink-framework as parent:** Inherit from `framework-build-parent-cronjob/cronjob-parent`. Depend on specific framework modules via `framework-bom`.
10. **Implementation of tests:** Follow guidelines in Section 4. Unit tests and module-internal ITs go within the module's `src/test`. Multi-module ITs go in `framework-test-applications/`. Leverage test-support modules.

## 9. FAQ/Troubleshooting

*   **Q: How do I add a new dependency?**
    **A:** If it's a third-party library or a Spring dependency, add it to `framework-dependencies-parent/pom.xml` with its version. If it's a new framework module you created, add it to `framework-bom/pom.xml`. Only add dependencies directly to a module's `pom.xml` if it's *only* needed by that module and should *not* be part of the BOM or centrally managed.
*   **Q: My tests are failing because of missing beans.**
    **A:** Check `@ConditionalOn...` annotations on the relevant auto-configuration classes and ensure conditions are met in your test context. Use `@SpringBootTest` profiles or properties to activate necessary configurations. If a specific bean is needed for your test class setup but shouldn't be in the main context, use `@MockBean` or a `@TestConfiguration` to provide it.
*   **Q: How do I use LocalStack/Redis/Keycloak for testing?**
    **A:** Do not set up Testcontainers manually in your test class if a dedicated test-support module exists. Use the provided annotations (`@LocalStack`, `@RedisTestSupport`, `@KeycloakTestSupport`) on your test class. These modules handle container lifecycle and Spring context configuration automatically, often relying on the `utils/` Docker Compose setup.
*   **Q: How do I add a custom header to outgoing HTTP requests?**
    **A:** For `WebClient`, create an `ExchangeFilterFunction`. For `RestTemplate`, create a `ClientHttpRequestInterceptor`. Register these as beans. The framework's auto-configurations will pick them up.
*   **Q: How do I handle a specific exception in my REST API?**
    **A:** Create a `@ControllerAdvice` and an `@ExceptionHandler` method for your exception. Return a `ResponseEntity<ProblemDetail>` for consistency.
*   **Q: How do I add custom data to logs?**
    **A:** Use `MDCPrefixWrapper.put()` or create a custom `AbstractContextDecorator` and use its `Property.put()`. For more complex JSON fields, create a custom `AbstractLogstashMarkersJsonProvider` and register it via `@AutoService(LogbackCustomizer.class)`.
