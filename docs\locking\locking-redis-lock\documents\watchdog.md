# Redis Locking Module: Watchdog Mechanism

## 1. Introduction

The `LockWatchdog` is a critical component in the `locking-redis-lock` module, responsible for preventing distributed locks from expiring prematurely while they are still actively held by an application instance. This mechanism is essential for long-running operations that might exceed the initial Time-To-Live (TTL) set on a Redis lock key.

## 2. Purpose and Design

The primary purpose of the `LockWatchdog` is to implement the "lease extension pattern":

*   Locks are initially acquired with a defined `leaseTime`.
*   If the `leaseTime` is greater than the globally configured `watchdogMaxTtl` and the lock is application-instance-bound, the watchdog takes over managing its lifecycle in Redis.
*   The actual TTL set on the Redis key is initially `watchdogMaxTtl`.
*   The `LockWatchdog` periodically extends this TTL (by resetting it to `watchdogMaxTtl`) as long as the application instance that acquired the lock is alive and the lock remains registered with the watchdog.
*   This renewal continues until the total intended `leaseTime` is approached or the lock is explicitly released and unregistered.
*   If the application instance crashes or the lock is unregistered, the watchdog stops renewing, and the lock will eventually expire in Redis based on its last set TTL (`watchdogMaxTtl`), allowing other processes to acquire it.

This design allows for relatively short physical TTLs on Redis keys (improving system resilience if an instance dies without releasing a lock) while supporting operations that logically require a much longer lock duration.

## 3. Core Components and Configuration

*   **`LockWatchdog.java` (Bean)**:
    *   A Spring-managed singleton bean, instantiated by [`RedisLockAutoConfiguration`](../src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockAutoConfiguration.java:1).
    *   Dependencies: [`RedisLockOperations`](../src/main/java/com/tui/destilink/framework/locking/redis/core/RedisLockOperations.java:1) (to execute scripts) and [`ScriptLoader`](../src/main/java/com/tui/destilink/framework/locking/redis/core/ScriptLoader.java:1) (to get the "extend\_lock" script).
    *   Maintains an internal `ConcurrentHashMap<String, LockInfo>` called `monitoredLocks` to track active locks.
*   **`LockWatchdog.LockInfo` (Inner Class)**:
    *   Stores `lockOwnerId`, the original `leaseTimeMillis` of the lock, the calculated `extensionIntervalMillis`, and the `nextExtensionTime`.
*   **Configuration from `RedisLockProperties.java`**:
    *   **`watchdogMaxTtl`**: A global internal property (e.g., default `PT60S`). This is the maximum TTL value the watchdog sets on a Redis key at each renewal interval.
    *   **`watchdogRenewalMultiplier`**: A global internal property (e.g., default `3`). This determines the actual renewal frequency. The `extensionIntervalMillis` for a `LockInfo` object is `watchdogMaxTtl (milliseconds) / watchdogRenewalMultiplier`. For example, if `watchdogMaxTtl` is 60s and `multiplier` is 3, the watchdog attempts renewal approximately every 20 seconds, resetting the TTL to 60s each time.
*   **Bucket-Level Policy (`use-watchdog`)**:
    *   Defined in `RedisLockProperties.BucketConfig` (resolved into `LockBucketConfig`).
    *   If a bucket has `use-watchdog: false`, the watchdog mechanism is entirely bypassed for all locks created for that bucket, regardless of their `leaseTime`.
*   **Instance-Level Activation**: For a specific lock instance, the watchdog becomes active if:
    1.  The lock's bucket configuration has `use-watchdog: true`.
    2.  The lock is "application-instance-bound." This means its `LockOwnerSupplier` generates an owner ID that uniquely identifies the current application instance (e.g., includes a pod name or a unique instance UUID). This is typically achieved when using `LockBucketBuilder.applicationScope(String appInstanceId)`.
    3.  The lock's effective `leaseTime` (potentially overridden at the instance level via builder) is greater than the global `watchdogMaxTtl`. If `leaseTime` <= `watchdogMaxTtl`, the watchdog is not used for that specific instance, and its TTL in Redis is set directly to its `leaseTime`.

## 4. Operational Flow

### 4.1. Lock Registration

1.  When an application successfully acquires a lock (e.g., via `AbstractRedisLock.lock()` or `tryLock(time, unit)`), and the conditions for watchdog activation (see above) are met.
2.  The `AbstractRedisLock` calls `lockWatchdog.registerLock(lockKey, lockOwnerId, effectiveLeaseTimeMillis)`.
3.  The `LockWatchdog`:
    *   Validates parameters.
    *   Calculates `extensionIntervalMillis` based on the global `watchdogMaxTtl` and `watchdogRenewalMultiplier` (i.e., `watchdogMaxTtlMillis / watchdogRenewalMultiplier`).
    *   Calculates the `nextExtensionTime` (current time + `extensionIntervalMillis`).
    *   Stores a new `LockInfo` object in the `monitoredLocks` map.

### 4.2. Scheduled Lease Extension (`extendLocks()` method)

1.  The `extendLocks()` method is annotated with `@Scheduled(fixedRate = ...)` (e.g., 1 second, or a fraction of the smallest possible `extensionIntervalMillis`).
2.  It iterates through all entries in the `monitoredLocks` map.
3.  For each `LockInfo`, if `System.currentTimeMillis() >= lockInfo.nextExtensionTime`:
    *   It retrieves the "extend\_lock" Lua script using `scriptLoader.getScript("extend_lock", Long.class)`.
    *   It executes this script via `lockOperations.executeScriptAsync(...)`, passing the `lockKey`, `lockInfo.lockOwnerId`, and the **global `watchdogMaxTtl` (in milliseconds)** as the new lease time argument for the script.
    *   **Atomic Ownership Check**: The "extend\_lock" Lua script **must** atomically check if the `lockKey` in Redis is still held by the `lockInfo.lockOwnerId` before extending its TTL to `watchdogMaxTtl`.
    *   **Result Handling**:
        *   If the script returns success (e.g., `1L`), indicating the lock was successfully extended:
            *   The `lockInfo.nextExtensionTime` is updated (current time + `extensionIntervalMillis`).
            *   A debug log is typically generated.
        *   If the script returns failure (e.g., `0L` or `null`), indicating the lock was not found, or owner mismatch, or an error occurred:
            *   The `lockKey` is removed from `monitoredLocks`.
            *   A warning log is generated, as the lock might have been released, expired, or taken by another process.
    *   Exceptions during script execution are caught and logged, typically resulting in the lock being removed from monitoring for that cycle.

### 4.3. Lock Unregistration

1.  When an application explicitly releases a lock using `AbstractRedisLock.unlock()`.
2.  The `unlock()` method calls `lockWatchdog.unregisterLock(lockKey, lockOwnerId)`.
3.  The `LockWatchdog`:
    *   Retrieves the `LockInfo` for the `lockKey`.
    *   If found and the `lockOwnerId` matches, it removes the entry from `monitoredLocks`.
    *   This stops further lease extensions for this lock.

## 5. Key Considerations and Lua Script ("extend_lock")

*   **Atomicity**: The "extend\_lock" Lua script is critical. It must perform the ownership check and the `PEXPIRE` (or `PSETEX`) command atomically to prevent race conditions where a lock expires just before being extended by its rightful owner.
    A typical "extend\_lock" script would:
    ```lua
    -- KEYS[1] = lockKey
    -- ARGV[1] = expectedOwnerId
    -- ARGV[2] = newLeaseTimeMillis (this MUST be watchdogMaxTtl in milliseconds)
    if redis.call("GET", KEYS[1]) == ARGV[1] then
      return redis.call("PEXPIRE", KEYS[1], ARGV[2]) -- Returns 1 if successful, 0 if key doesn't exist
    else
      return 0 -- Not owner or key doesn't exist
    end
    ```
*   **Configuration Alignment**:
    *   The `extensionIntervalMillis` in `LockInfo` **must** be calculated based on the global `watchdogMaxTtl` and `watchdogRenewalMultiplier` (e.g., `watchdogMaxTtlMillis / watchdogRenewalMultiplier`).
    *   When calling the "extend\_lock" script, the new lease time argument (`ARGV[2]`) **must** be the global `watchdogMaxTtl` (in milliseconds), not the original full `leaseTimeMillis` of the lock instance.
*   **Clock Skew**: Distributed systems can have clock skew. While Redis operations are atomic, the watchdog's timing relies on local system clocks. The buffer provided by renewing at `watchdogMaxTtl / watchdogRenewalMultiplier` (e.g., 1/3 or 1/2 of `watchdogMaxTtl`) helps mitigate minor skews.
*   **Network Latency**: Delays in communicating with Redis can impact the timeliness of extensions. The chosen `watchdogMaxTtl` and `watchdogRenewalMultiplier` should account for expected network latencies.

The `LockWatchdog` is a vital mechanism for ensuring the liveness and safety of distributed locks that need to be held for periods longer than a short, fixed TTL.