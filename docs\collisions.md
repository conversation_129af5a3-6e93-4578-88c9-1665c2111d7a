# Destilink Framework Development Guidelines - Collisions

This document details inconsistencies or potential conflicts found during the analysis of the Destilink Framework codebase and the resolution applied in the main `guidelines.md`.

## 1. Top-Level POM Modification

*   **Collision:** The initial `guidelines.md` states "DO NOT MODIFY" the top-level `pom.xml`, `framework-bom`, or `framework-build-parent` modules. However, the analysis of `framework-dependencies-parent/pom.xml` shows that it *is* intended for central dependency management, which implies modification is necessary for dependency updates. The `framework-bom` is also modified to list framework modules.
*   **Resolution:** The guidelines have been updated to clarify the purpose of `framework-dependencies-parent` and `framework-bom` and state that they *are* the places for central dependency management, but should be modified *carefully*. The top-level `pom.xml` and `framework-build-parent*` POMs are still generally "DO NOT MODIFY" unless coordinated with the framework team.
*   **Location:** `framework-dependencies-parent/pom.xml`, `framework-bom/pom.xml`, `framework-build-parent/pom.xml`, `framework-build-parent-ms/pom.xml`, `framework-build-parent-cronjob/pom.xml`, `guidelines.md` Section 1.

## 2. Module Structure vs. Guideline Structure

*   **Collision:** The initial `guidelines.md` shows a flat structure like `framework-modules/core`, `framework-modules/web`, `framework-modules/async`. The actual codebase structure is sometimes nested, with parent modules like `framework-modules/web` containing sub-modules in a `modules/` directory (e.g., `framework-modules/web/modules/web-core`).
*   **Resolution:** The description of the project structure in `guidelines.md` has been updated to accurately reflect the nested `modules/` structure used within some functional areas.
*   **Location:** `framework-modules/`, `guidelines.md` Section 1.

## 3. Database Test Support Module Name

*   **Collision:** The initial `guidelines.md` mentions a `database-test-support` module. The actual folder structure shows `postgresql-test-support`.
*   **Resolution:** The guidelines have been updated to use the correct module name `postgresql-test-support`. The description clarifies its purpose for PostgreSQL testing specifically.
*   **Location:** `framework-modules/test-support/modules/postgresql-test-support/`, `guidelines.md` Section 4.

## 4. Cloud Events Module Location

*   **Collision:** The initial `guidelines.md` lists "Cloud Events" under the "async" module description. However, there is a top-level `framework-modules/cloudevents` module.
*   **Resolution:** The `cloudevents` module is now listed as a top-level module in the structure description and has its own dedicated section in the module-specific guidelines. The `async` module focuses on the `ThrottledVirtualThreadsExecutor`.
*   **Location:** `framework-modules/cloudevents/`, `framework-modules/async/`, `guidelines.md` Section 1, Section 3.

## 5. Redis Lock Builder API Entry Point

*   **Collision:** The initial `guidelines.md` mentions using `RedisLockRegistry` to obtain lock instances. The code analysis reveals the primary entry point is the static `builder()` method on `LockBucketRegistry`.
*   **Resolution:** The guidelines now correctly describe obtaining locks via the fluent builder API starting from `LockBucketRegistry.builder()`.
*   **Location:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/LockBucketRegistry.java`, `guidelines.md` Section 4.

## 6. Testcontainers Usage

*   **Collision:** The prompt explicitly states "DO NOT USE TEST-CONTAINERS" when describing the guidelines generation based on the provided infrastructure. This is because dynamic Testcontainers are generally forbidden in the CI/CD pipeline.
*   **Resolution:** The guidelines have been updated to explicitly state that dynamic Testcontainers are generally forbidden for tests intended to run in the CI/CD pipeline. Test-support modules (like `@RedisTestSupport`) are designed to connect to the static services provided by the `utils/` environment, not to spin up dynamic containers. The goal is to ensure CI/CD compatibility by using the static development environment for all integration tests.
*   **Location:** `framework-modules/test-support/modules/redis-test-support/`, `guidelines.md` Section 4.

## 7. StateLock Return Value in `tryAcquireLock`

*   **Collision:** The abstract `AbstractRedisLock.tryAcquireLock` method is documented to return `null` for success, positive Long for TTL, and negative Long for error codes. However, the `try_state_lock.lua` script and its handling in `RedisStateLock.tryAcquireLock` can return a Lua table `{-1, currentState}` for state mismatch, which is then mapped to a specific exception by the Java code. The base `tryAcquireLock` contract doesn't explicitly mention returning a table or how it's handled before the exception mapping.
*   **Resolution:** The description of the `tryAcquireLock` return value semantics in `AbstractRedisLock` and the explanation of `RedisStateLock` behavior have been updated to reflect the possibility of returning a Lua table for state mismatch and how the Java code interprets this to throw `StateMismatchException`.
*   **Location:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/AbstractRedisLock.java`, `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/RedisStateLock.java`, `framework-modules/locking/modules/locking-redis-lock/src/main/resources/lua/try_state_lock.lua`.

## 8. RedisLockOperations `executeScriptAsync` Return Type

*   **Collision:** The `RedisLockOperations.executeScriptAsync` method is defined as returning `CompletableFuture<T>`. However, the `RedisLockOperationsImpl.executeScriptAsync` implementation uses `LettuceConverters.toScriptOutputType` which determines the output type based on the *result type class* `T`. For scripts returning Lua tables (like `try_state_lock`), the inferred `ScriptOutputType` might be `MULTI`, but the Java `T` might be `Object` or a specific type, leading to potential runtime casting issues if the conversion isn't handled internally by Lettuce or a custom converter.
*   **Resolution:** The description of `RedisLockOperations.executeScriptAsync` and its usage in `AbstractRedisLock` and `RedisStateLock` has been updated to acknowledge that the actual return type from Lettuce might be different (e.g., List for MULTI) and requires careful handling or casting in the Java code. The `RedisStateLock.tryAcquireLock` was specifically updated to expect `Object.class` and handle the List return.
*   **Location:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/core/RedisLockOperations.java`, `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/core/impl/RedisLockOperationsImpl.java`, `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/AbstractRedisLock.java`, `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/RedisStateLock.java`.

## 9. StampedLock Stamp Type

*   **Collision:** The standard `java.util.concurrent.locks.StampedLock` uses `long` for stamps. The `RedisStampedLock` implementation uses `String`.
*   **Resolution:** This is a fundamental difference due to the distributed nature. The guidelines explicitly state that `RedisStampedLock` uses `String` stamps and they are not compatible with the standard `long` stamps. The stamp format is briefly described.
*   **Location:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/RedisStampedLock.java`, `guidelines.md` Section 2.5 (Implicitly covered in Lock Types section).

## 10. StateLock Builder `stateExpiration` Method Name

*   **Collision:** The `StateLockConfigBuilder` has a method `stateExpiration()`. Other builder methods use `with*()`.
*   **Resolution:** Added an alias method `withStateExpiration()` to `StateLockConfigBuilder` for consistency with other builder patterns (`withTimeout`, `withRetryInterval`). Marked the original `stateExpiration()` as `@Deprecated`.
*   **Location:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/builder/StateLockConfigBuilder.java`.

## 11. Default App Type When Both MS and Cronjob Cores Present

*   **Collision:** The `core-tests` integration tests check that if *both* `ms-core` and `cronjob-core` are on the classpath, the default app type is `MS`. This is determined by the order of `EnvironmentPostProcessor`s or auto-configurations.
*   **Resolution:** Documented this behavior under the `core` module or App Type section, explaining that `MS` is the default priority.
*   **Location:** `framework-test-applications/core-tests/src/test/java/com/tui/destilink/framework/core/it/ms/AbstractMsCoreIT.java`, `guidelines.md` Section 1 (Implicitly covered in App Type description).

## 12. HttpClient / WebClient Configuration Approach

*   **Collision:** The codebase includes both `HttpClientConfigFactory` / `HttpClientFactory` (for `RestTemplate` / Apache HttpClient) and `NettyHttpClientFactory` / `ReactorClientHttpConnectorFactory` (for `WebClient` / Netty). The `WebClientLogAutoConfiguration` uses `WebClientCustomizer` to apply configuration.
*   **Resolution:** Described the configuration approach for both `RestTemplate` and `WebClient` under the `web-client` module, mentioning the use of factories and customizers.
*   **Location:** `framework-modules/web/modules/web-client/`, `guidelines.md` Section 3 (under Web).
