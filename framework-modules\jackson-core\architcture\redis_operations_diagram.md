# Redis Locking Module: Redis Operations

This diagram illustrates the Redis operations and Lua scripts used in the `locking-redis-lock` module.

```mermaid
classDiagram
    %% Core Redis Operations
    class RedisLockOperations {
        -scriptLoader: ScriptLoader
        -clusterCommandExecutor: ClusterCommandExecutor
        -lockErrorHandler: RedisLockErrorHandler
        -unlockMessageListenerManager: UnlockMessageListenerManager
        +tryLock(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
        +isLocked(lockKey: String): CompletableFuture~Boolean~
        +getLockOwner(lockKey: String): CompletableFuture~String~
        +tryReadLock(lockKey: String, readerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlockReadLock(lockKey: String, readerId: String): CompletableFuture~Boolean~
        +tryWriteLock(lockKey: String, writerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlockWriteLock(lockKey: String, writerId: String): CompletableFuture~Boolean~
        +tryStampedLock(lockKey: String, ownerId: String, stampType: String, leaseTimeMs: long): CompletableFuture~String~
        +validateStamp(lockKey: String, stamp: String): CompletableFuture~Boolean~
        +unlockStampedLock(lockKey: String, stamp: String): CompletableFuture~Boolean~
        +tryStateLock(lockKey: String, ownerId: String, stateKey: String, expectedState: String, newState: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +getState(lockKey: String, stateKey: String): CompletableFuture~String~
    }
    
    %% Script Loading and Execution
    class ScriptLoader {
        -redisTemplate: RedisTemplate
        -scriptCache: Map~String, String~
        +loadScript(scriptName: String): String
        +getScriptSha(scriptName: String): String
        +executeScript~T~(scriptName: String, keys: List~String~, args: List~Object~): CompletableFuture~T~
    }
    
    class ClusterCommandExecutor {
        -redisTemplate: RedisTemplate
        -redisConnectionFactory: RedisConnectionFactory
        +executeCommand~T~(key: String, command: Function): CompletableFuture~T~
        +executeScriptCommand~T~(keys: List~String~, scriptSha: String, args: List~Object~): CompletableFuture~T~
        +executeMultiKeyCommand~T~(keys: List~String~, command: Function): CompletableFuture~T~
    }
    
    %% Redis Key Management
    class RedisKeyBuilder {
        +buildLockKey(prefix: String, name: String, bucket: String): String
        +buildStateKey(lockKey: String, stateKey: String): String
        +buildReadLockTimeoutKey(lockKey: String, readerId: String, count: int): String
        +buildPubSubChannelKey(channelPrefix: String, bucket: String): String
        +applyHashTag(key: String): String
    }
    
    %% Pub/Sub for Non-Blocking Wait
    class UnlockMessageListenerManager {
        -redisTemplate: RedisTemplate
        -listenerContainers: Map~String, RedisMessageListenerContainer~
        -messageListeners: Map~String, UnlockMessageListener~
        +registerListener(channelName: String, bucket: String): UnlockMessageListener
        +unregisterListener(channelName: String, bucket: String): void
        +publishUnlockMessage(channelName: String, lockKey: String, unlockType: UnlockType): void
    }
    
    class UnlockMessageListener {
        -lockSemaphoreHolder: LockSemaphoreHolder
        +onMessage(message: Message, pattern: byte[]): void
        +getChannelName(): String
    }
    
    class UnlockType {
        <<enumeration>>
        EXPLICIT
        EXPIRED
        STOLEN
    }
    
    %% Lua Scripts
    class LuaScripts {
        <<static>>
        +TRY_LOCK: String
        +UNLOCK: String
        +TRY_READ_LOCK: String
        +UNLOCK_READ_LOCK: String
        +TRY_WRITE_LOCK: String
        +UNLOCK_WRITE_LOCK: String
        +TRY_STAMPED_LOCK: String
        +VALIDATE_STAMP: String
        +UNLOCK_STAMPED_LOCK: String
        +TRY_STATE_LOCK: String
    }
    
    %% Redis Data Structures
    class RedisDataStructures {
        <<static>>
        +REENTRANT_LOCK: String
        +READ_WRITE_LOCK: String
        +STAMPED_LOCK: String
        +STATE_LOCK: String
    }
    
    %% Error Handling
    class RedisLockErrorHandler {
        +translateException(ex: Throwable, lockKey: String, ownerId: String): AbstractRedisLockException
        +translateScriptException(ex: Throwable, scriptName: String, keys: List, args: List): AbstractRedisLockException
        +enrichException(ex: AbstractRedisLockException, details: Map): AbstractRedisLockException
    }
    
    %% Relationships
    RedisLockOperations --> ScriptLoader : uses
    RedisLockOperations --> ClusterCommandExecutor : uses
    RedisLockOperations --> RedisLockErrorHandler : uses
    RedisLockOperations --> UnlockMessageListenerManager : uses
    RedisLockOperations --> RedisKeyBuilder : uses
    
    ScriptLoader --> LuaScripts : loads
    UnlockMessageListenerManager --> UnlockMessageListener : manages
    UnlockMessageListener --> UnlockType : processes
    
    %% Notes
    note for RedisLockOperations "Core service that translates lock operations to Redis commands"
    note for ScriptLoader "Loads and caches Lua scripts for atomic operations"
    note for ClusterCommandExecutor "Handles Redis Cluster key routing"
    note for RedisKeyBuilder "Builds Redis keys with proper namespacing and hash tags"
    note for LuaScripts "Lua scripts for atomic lock operations"
```

## Redis Data Structures

The Redis Locking module uses different Redis data structures for each lock type:

### 1. Reentrant Lock

```
lockKey -> ownerId (String)
```

**Example:**
```
lock:{bucket}:user:123:profile -> client:1234:thread:5678
```

**TTL:** Set to lease time (e.g., 30 seconds)

### 2. Read-Write Lock

```
lockKey -> Hash {
  write_owner_id: String,  // ID of write lock holder or empty
  read_holders: Integer    // Count of read lock holders
}

// For each reader:
lockKey:__rwttl__:{readerId}:{count} -> "1" (String)
```

**Example:**
```
lock:{bucket}:catalog:products -> {
  write_owner_id: "",
  read_holders: 3
}

lock:{bucket}:catalog:products:__rwttl__:client:1234:thread:5678:1 -> "1"
lock:{bucket}:catalog:products:__rwttl__:client:5678:thread:9012:1 -> "1"
lock:{bucket}:catalog:products:__rwttl__:client:9012:thread:3456:1 -> "1"
```

**TTL:** Individual TTL for each reader key

### 3. Stamped Lock

```
lockKey -> Hash {
  owner_id: String,   // Current lock owner
  mode: String,       // WRITE, READ, or OPTIMISTIC
  stamp: String       // Current valid stamp
}
```

**Example:**
```
lock:{bucket}:order:123:processing -> {
  owner_id: "client:1234:thread:5678",
  mode: "WRITE",
  stamp: "W:1234567890"
}
```

**TTL:** Set to lease time (e.g., 30 seconds)

### 4. State Lock

```
lockKey -> ownerId (String)
lockKey:state:{stateKey} -> stateValue (String)
```

**Example:**
```
lock:{bucket}:order:123:status -> client:1234:thread:5678
lock:{bucket}:order:123:status:state:status -> "PROCESSING"
```

**TTL:** Set to lease time (e.g., 30 seconds)

## Lua Scripts

The module uses Lua scripts to ensure atomicity of operations. Here are the key scripts:

### 1. try_lock.lua

```lua
-- KEYS[1]: lock key
-- ARGV[1]: owner ID
-- ARGV[2]: lease time in milliseconds

-- Check if the lock exists
local lockExists = redis.call('EXISTS', KEYS[1])

if lockExists == 0 then
    -- Lock doesn't exist, create it
    redis.call('SET', KEYS[1], ARGV[1], 'PX', ARGV[2])
    return 1
else
    -- Lock exists, check if it's owned by the same client
    local currentOwner = redis.call('GET', KEYS[1])
    if currentOwner == ARGV[1] then
        -- Extend lease time (reentrant lock)
        redis.call('PEXPIRE', KEYS[1], ARGV[2])
        return 1
    else
        -- Lock is owned by someone else
        return 0
    end
end
```

### 2. unlock.lua

```lua
-- KEYS[1]: lock key
-- KEYS[2]: pub/sub channel
-- ARGV[1]: owner ID
-- ARGV[2]: unlock type (EXPLICIT, EXPIRED, STOLEN)

-- Check if the lock exists
local lockExists = redis.call('EXISTS', KEYS[1])

if lockExists == 0 then
    -- Lock doesn't exist, nothing to do
    return 1
else
    -- Lock exists, check if it's owned by the same client
    local currentOwner = redis.call('GET', KEYS[1])
    if currentOwner == ARGV[1] then
        -- Delete the lock
        redis.call('DEL', KEYS[1])
        -- Publish unlock message
        redis.call('PUBLISH', KEYS[2], KEYS[1] .. ':' .. ARGV[2])
        return 1
    else
        -- Lock is owned by someone else
        return 0
    end
end
```

### 3. try_read_lock.lua

```lua
-- KEYS[1]: lock key
-- KEYS[2]: read lock timeout key
-- ARGV[1]: reader ID
-- ARGV[2]: lease time in milliseconds

-- Check if there's a write lock
local lockData = redis.call('HGETALL', KEYS[1])
local writeOwnerId = nil
local readHolders = 0

-- Parse hash data
for i = 1, #lockData, 2 do
    if lockData[i] == 'write_owner_id' then
        writeOwnerId = lockData[i+1]
    elseif lockData[i] == 'read_holders' then
        readHolders = tonumber(lockData[i+1])
    end
end

-- If write lock exists and not owned by this client, fail
if writeOwnerId and writeOwnerId ~= '' and writeOwnerId ~= ARGV[1] then
    return 0
end

-- Increment read holders
if readHolders == 0 then
    redis.call('HSET', KEYS[1], 'read_holders', 1)
else
    redis.call('HINCRBY', KEYS[1], 'read_holders', 1)
end

-- Set read lock timeout key
redis.call('SET', KEYS[2], '1', 'PX', ARGV[2])

return 1
```

### 4. try_stamped_lock.lua

```lua
-- KEYS[1]: lock key
-- ARGV[1]: owner ID
-- ARGV[2]: stamp type (W:, R:, O:)
-- ARGV[3]: lease time in milliseconds

-- Check if the lock exists
local lockExists = redis.call('EXISTS', KEYS[1])

if lockExists == 0 then
    -- Lock doesn't exist, create it
    local stamp = ARGV[2] .. ':' .. redis.call('TIME')[1]
    redis.call('HSET', KEYS[1], 'owner_id', ARGV[1], 'mode', ARGV[2], 'stamp', stamp)
    redis.call('PEXPIRE', KEYS[1], ARGV[3])
    return stamp
else
    -- Lock exists, check mode and ownership
    local lockData = redis.call('HGETALL', KEYS[1])
    local currentOwner = nil
    local currentMode = nil
    local currentStamp = nil
    
    -- Parse hash data
    for i = 1, #lockData, 2 do
        if lockData[i] == 'owner_id' then
            currentOwner = lockData[i+1]
        elseif lockData[i] == 'mode' then
            currentMode = lockData[i+1]
        elseif lockData[i] == 'stamp' then
            currentStamp = lockData[i+1]
        end
    end
    
    -- Handle different acquisition scenarios based on current mode and requested mode
    -- ... (mode-specific logic)
    
    return currentStamp
end
```

### 5. try_state_lock.lua

```lua
-- KEYS[1]: lock key
-- KEYS[2]: state key
-- ARGV[1]: owner ID
-- ARGV[2]: expected state
-- ARGV[3]: new state
-- ARGV[4]: lease time in milliseconds

-- Check current state
local currentState = redis.call('GET', KEYS[2])

-- If state doesn't match expected state, fail
if currentState ~= ARGV[2] then
    return 0
end

-- Try to acquire lock
local lockExists = redis.call('EXISTS', KEYS[1])

if lockExists == 0 then
    -- Lock doesn't exist, create it and update state
    redis.call('SET', KEYS[1], ARGV[1], 'PX', ARGV[4])
    redis.call('SET', KEYS[2], ARGV[3])
    return 1
else
    -- Lock exists, check if it's owned by the same client
    local currentOwner = redis.call('GET', KEYS[1])
    if currentOwner == ARGV[1] then
        -- Extend lease time and update state
        redis.call('PEXPIRE', KEYS[1], ARGV[4])
        redis.call('SET', KEYS[2], ARGV[3])
        return 1
    else
        -- Lock is owned by someone else
        return 0
    end
end
```

## Redis Command Execution Flow

The execution flow for Redis operations follows this pattern:

1. **Lock Request**:
   ```
   AbstractRedisLock.acquireLock()
     → RedisLockOperations.tryLock()
       → ScriptLoader.executeScript("try_lock.lua")
         → ClusterCommandExecutor.executeScriptCommand()
           → Redis EVALSHA command
   ```

2. **Unlock Request**:
   ```
   AbstractRedisLock.releaseLock()
     → RedisLockOperations.unlock()
       → ScriptLoader.executeScript("unlock.lua")
         → ClusterCommandExecutor.executeScriptCommand()
           → Redis EVALSHA command
           → Redis PUBLISH to unlock channel
   ```

3. **Non-Blocking Wait**:
   ```
   AbstractRedisLock.acquireLock()
     → LockSemaphoreHolder.getSemaphore().tryAcquire()
     → (If lock unavailable) Wait for Pub/Sub notification
     → UnlockMessageListener.onMessage()
       → LockSemaphoreHolder.notifyUnlock()
         → Semaphore.release()
   ```

## Redis Cluster Considerations

The module is designed to work with Redis Cluster by:

1. **Hash Tags**:
   - Using `{bucket}` in keys to ensure related keys are on the same shard
   - Example: `lock:{bucket}:user:123:profile`

2. **Multi-Key Operations**:
   - Using Lua scripts to ensure atomic operations within a shard
   - Ensuring all keys for a single lock operation use the same hash tag

3. **Command Routing**:
   - `ClusterCommandExecutor` handles proper routing of commands to the correct Redis Cluster node
   - Retries operations on cluster topology changes

## Key Naming Strategy

The module uses a hierarchical key naming strategy:

```
{prefix}:{bucket}:{name}
```

Where:
- `prefix`: Global prefix for all lock keys (default: "lock")
- `bucket`: Logical grouping of locks (default: "default")
- `name`: User-provided lock name

Additional keys follow these patterns:

- Read lock timeout keys: `{lockKey}:__rwttl__:{readerId}:{count}`
- State keys: `{lockKey}:state:{stateKey}`
- Pub/Sub channels: `{channelPrefix}:{bucket}`

## Redis Performance Considerations

The module optimizes Redis performance through:

1. **Minimized Network Round-Trips**:
   - Using Lua scripts for atomic multi-command operations
   - Batching operations where possible

2. **Efficient Key Expiration**:
   - Using Redis TTL for automatic lock expiration
   - Watchdog for lease extension without constant polling

3. **Non-Blocking Wait**:
   - Using Redis Pub/Sub for unlock notifications
   - Avoiding polling for lock availability

4. **Connection Management**:
   - Using connection pooling via Spring Data Redis
   - Handling connection failures with appropriate retries

5. **Memory Optimization**:
   - Using minimal data structures
   - Automatic cleanup of expired keys