package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a lock operation fails after retry attempts.
 * <p>
 * This exception is thrown when a Redis lock operation fails after all
 * configured retry attempts have been exhausted. It provides context about
 * the retry attempts and the underlying cause of the failure.
 * </p>
 */
public class LockOperationException extends AbstractRedisLockException {

    private final String operation;
    private final int attemptsMade;
    private final int maxAttempts;

    /**
     * Constructs a new LockOperationException with the specified details.
     *
     * @param lockName     The full Redis key of the lock involved
     * @param lockType     The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId  The ID of the owner attempting the operation (can be null)
     * @param requestUuid  The unique ID for the lock operation attempt (can be null)
     * @param operation    The name of the operation that failed
     * @param attemptsMade The number of attempts made before failure
     * @param maxAttempts  The maximum number of attempts configured
     * @param message      Descriptive error message
     */
    public LockOperationException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String operation, int attemptsMade, int maxAttempts, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.operation = operation;
        this.attemptsMade = attemptsMade;
        this.maxAttempts = maxAttempts;
    }

    /**
     * Constructs a new LockOperationException with the specified details and cause.
     *
     * @param lockName     The full Redis key of the lock involved
     * @param lockType     The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId  The ID of the owner attempting the operation (can be null)
     * @param requestUuid  The unique ID for the lock operation attempt (can be null)
     * @param operation    The name of the operation that failed
     * @param attemptsMade The number of attempts made before failure
     * @param maxAttempts  The maximum number of attempts configured
     * @param message      Descriptive error message
     * @param cause        The underlying cause of this exception
     */
    public LockOperationException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String operation, int attemptsMade, int maxAttempts, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.operation = operation;
        this.attemptsMade = attemptsMade;
        this.maxAttempts = maxAttempts;
    }

    /**
     * Gets the name of the operation that failed.
     *
     * @return The operation name
     */
    public String getOperation() {
        return operation;
    }

    /**
     * Gets the number of attempts made before failure.
     *
     * @return The number of attempts made
     */
    public int getAttemptsMade() {
        return attemptsMade;
    }

    /**
     * Gets the maximum number of attempts configured.
     *
     * @return The maximum number of attempts
     */
    public int getMaxAttempts() {
        return maxAttempts;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        if (operation != null) {
            contextMap.put("lock.operation", operation);
        }
        contextMap.put("lock.attemptsMade", attemptsMade);
        contextMap.put("lock.maxAttempts", maxAttempts);
    }
}