# Modernized Redis Locking Module: Architecture Overview

This document provides a high-level overview of the architecture of the modernized Redis locking module. The goal of this refactoring is to improve the module's performance, reliability, and maintainability by addressing issues in the previous implementation and aligning with best practices for distributed locking with Redis. **The architecture described here is mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md`.**

## Key Architectural Principles

*   **Atomicity**: Critical operations (acquire, release, extend) are implemented using Redis Lua scripts to ensure atomicity and prevent race conditions.
*   **Non-Polling Acquisition**: The module moves away from inefficient polling towards a notification-based mechanism using Redis Pub/Sub and semaphores for waiting threads.
*   **Reliability**: Includes mechanisms like a watchdog for automatic lease extension and a fallback to re-polling to handle potential missed Pub/Sub messages.
*   **Clear Separation of Concerns**: The module is structured into distinct components responsible for specific tasks (e.g., lock operations, messaging, error handling, configuration).
*   **Configurability**: Lock behavior can be configured through properties and lock buckets, allowing for flexibility in different use cases.
*   **Observability**: Exposes metrics to provide visibility into the module's performance and usage.
*   **Distributed Reentrancy**: Reentrancy for reentrant locks is managed using Redis HashMaps to store the reentrancy count and owner information, ensuring correctness across distributed instances.
*   **Blocking `lock()` Method**: The `lock()` method adheres to the `java.util.concurrent.Lock` interface by blocking indefinitely until the lock is acquired or interrupted. The `defaultTimeout` property is not used in this method.

## High-Level Component Diagram

```mermaid
C4Context
    title System Context for Redis Locking Module

    System(client, "Client Application", "Application using the Redis Locking Module")

    System_Boundary(locking_module, "Redis Locking Module") {
        Component(lock_types, "Lock Types", "Implementations of distributed locks (Reentrant, State, ReadWrite)")
        Component(lock_components, "Lock Components", "Shared components (Watchdog, Error Handler, Messaging Manager, etc.)")
        Component(redis_operations, "Redis Operations", "Handles low-level interaction with Redis, executes Lua scripts")
        Component(configuration, "Configuration", "Manages module and bucket configuration")
        Component(messaging, "Messaging", "Handles Redis Pub/Sub for unlock notifications")
    }

    System(redis, "Redis", "Redis Server (Standalone or Cluster)")

    client --> lock_types : Uses
    lock_types --> lock_components : Uses
    lock_types --> redis_operations : Uses
    lock_components --> redis_operations : Uses
    lock_components --> messaging : Uses
    redis_operations --> redis : Communicates with
    messaging --> redis : Uses Pub/Sub
    client --> configuration : Configures
```

## Key Flows

*   **Lock Acquisition**: A client requests a lock. The module attempts to acquire it atomically via a Lua script. If the lock is held, the client waits on a semaphore, notified by a Redis Pub/Sub message when the lock is released. A fallback mechanism prevents indefinite waiting on missed messages.
*   **Lock Release**: A client releases a lock. The module releases it atomically via a Lua script and publishes an unlock message to Redis Pub/Sub.
*   **Lease Extension**: The watchdog periodically extends the lease of acquired locks using a Lua script.

## Data Storage

The module stores lock-related data in Redis using a well-defined key schema (see [Redis Key Schema](redis_key_schema.md)). This includes:

*   Main lock keys (using Redis Strings or Hashes for reentrant locks)
*   State lock keys (using Redis Strings)
*   Read lock keys (using Redis Sets)
*   Write lock keys (using Redis Strings)
*   Response cache keys (using Redis Strings)

## Configuration

The module is configured primarily through Spring `@ConfigurationProperties` (see [Configuration](configuration.md)). Lock buckets allow for fine-grained configuration of different lock instances.

This overview provides a foundation for understanding the structure and key mechanisms of the modernized Redis locking module. More detailed information can be found in the linked documents.