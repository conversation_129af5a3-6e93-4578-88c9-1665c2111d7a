# Destilink Framework API Documentation Generator

A tool to automatically extract API endpoints from Spring controllers and generate documentation.

## Features

- Scans Java files for Spring controllers and REST endpoints
- Extracts HTTP methods, paths, and method names
- Groups endpoints by controller and package
- Generates both Markdown and HTML documentation
- Creates a navigable table of contents

## Usage

```bash
cd utils/api-doc-generator
node api_doc_generator.js
```

## Output

The tool generates two documentation files:

- `output/api-documentation.md` - Markdown documentation
- `output/api-documentation.html` - HTML documentation with styling

## Integration with MkDocs

The generated Markdown file can be included in the project's MkDocs documentation:

1. Copy the generated Markdown file to the docs directory:
   ```bash
   cp output/api-documentation.md ../../docs/api-reference.md
   ```

2. Add the file to the navigation in `mkdocs.yml`:
   ```yaml
   nav:
     - Home: index.md
     - API Reference: api-reference.md
   ```

## Customization

You can customize the tool by modifying the configuration at the top of the script:

- `rootDir`: Root directory to scan for Java files
- `outputDir`: Directory where documentation will be generated
- `ignorePatterns`: Directories to ignore during scanning