# Plan: Redis Lock - Step 3: Builder API and Lock Instantiation

This step implements the fluent builder API for creating lock instances, as described in the documentation. This provides a user-friendly and configurable way to obtain locks.

### Step 3.1: Implement `LockBucketRegistry.java`

This bean is the main entry point for applications to create locks.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockBucketRegistry.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class LockBucketRegistry {
    private final LockComponentRegistry componentRegistry;
    private final RedisLockProperties globalProperties;

    public LockBucketBuilder builder(String bucketName) {
        LockBucketConfig defaultConfig = LockBucketConfig.builder()
                .leaseTime(globalProperties.getLeaseTime())
                .retryInterval(globalProperties.getRetryInterval())
                .pubSubWaitTimeout(globalProperties.getPubSubWaitTimeout())
                .stateKeyExpiration(globalProperties.getStateKeyExpiration())
                .build();
        return new LockBucketBuilder(componentRegistry, bucketName, defaultConfig);
    }
}
```

### Step 3.2: Implement `LockBucketBuilder.java`

This class allows configuring bucket-level properties.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockBucketBuilder.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

@RequiredArgsConstructor
public class LockBucketBuilder {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private LockBucketConfig bucketConfig;

    public LockBucketBuilder withDefaultLeaseTime(Duration leaseTime) {
        this.bucketConfig = this.bucketConfig.toBuilder().leaseTime(leaseTime).build();
        return this;
    }

    public LockBucketBuilder withDefaultRetryInterval(Duration retryInterval) {
        this.bucketConfig = this.bucketConfig.toBuilder().retryInterval(retryInterval).build();
        return this;
    }
    
    // Add other with... methods for bucket config properties

    public LockConfigBuilder lock() {
        return new LockConfigBuilder(componentRegistry, bucketName, bucketConfig);
    }
}
```

### Step 3.3: Implement `LockConfigBuilder.java`

This class is for selecting the lock type.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockConfigBuilder.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.impl.RedisReentrantLock;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class LockConfigBuilder {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final LockBucketConfig bucketConfig;

    public ReentrantLockConfigBuilder reentrant(String lockName) {
        // This is a simplified example. A full implementation would have builders for all lock types.
        return new ReentrantLockConfigBuilder(componentRegistry, bucketName, lockName, bucketConfig);
    }
    
    // Add methods for other lock types: readWrite(name), stamped(name), state(name, expectedState)
}
```

### Step 3.4: Implement `AbstractLockTypeConfigBuilder` and Subclasses

These builders handle instance-specific configuration.

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/AbstractLockTypeConfigBuilder.java`
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.Duration;

@RequiredArgsConstructor
@Getter(AccessLevel.PROTECTED)
public abstract class AbstractLockTypeConfigBuilder<B extends AbstractLockTypeConfigBuilder<B, L>, L extends AbstractRedisLock> {
    private final LockComponentRegistry componentRegistry;
    private final String bucketName;
    private final String lockName;
    private final LockBucketConfig bucketConfig;

    private Duration instanceLeaseTime;
    private Duration instanceRetryInterval;
    
    @SuppressWarnings("unchecked")
    public B withLeaseTime(Duration leaseTime) {
        this.instanceLeaseTime = leaseTime;
        return (B) this;
    }
    
    @SuppressWarnings("unchecked")
    public B withRetryInterval(Duration retryInterval) {
        this.instanceRetryInterval = retryInterval;
        return (B) this;
    }

    protected Duration getEffectiveLeaseTime() {
        return instanceLeaseTime != null ? instanceLeaseTime : bucketConfig.leaseTime();
    }
    
    protected Duration getEffectiveRetryInterval() {
        return instanceRetryInterval != null ? instanceRetryInterval : bucketConfig.retryInterval();
    }

    public abstract L build();
}
```

**File to Create:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/ReentrantLockConfigBuilder.java`
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.impl.RedisReentrantLock;

public class ReentrantLockConfigBuilder extends AbstractLockTypeConfigBuilder<ReentrantLockConfigBuilder, RedisReentrantLock> {

    public ReentrantLockConfigBuilder(LockComponentRegistry componentRegistry, String bucketName, String lockName, LockBucketConfig bucketConfig) {
        super(componentRegistry, bucketName, lockName, bucketConfig);
    }

    @Override
    public RedisReentrantLock build() {
        return new RedisReentrantLock(
            getComponentRegistry(),
            getBucketConfig(),
            getLockName(),
            getEffectiveLeaseTime().toMillis(),
            getEffectiveRetryInterval().toMillis()
        );
    }
}
```

### Step 3.5: Update Lock Constructors

The constructors for lock implementations should be made package-private to enforce creation via the builder API.

**File to Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/impl/RedisReentrantLock.java` (and others)

**Change:**
```java
// Change constructor from public to package-private
RedisReentrantLock(
        LockComponentRegistry componentRegistry,
        LockBucketConfig config,
        String lockName,
        long lockTtlMillis,
        long retryIntervalMillis) {
    // ... constructor logic
}
```

---
