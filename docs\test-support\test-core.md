# Test-Core Module

The test-core module provides some basic utils for testing and provides some test dependencies.

```xml
<dependency>
    <groupId>com.tui.destilink.framework.test-support</groupId>
    <artifactId>test-core</artifactId>
</parent>
```

## Test-Class-Id

The module injects a test-class-id property on context creation. The id is unique per test-class and can be used
in tests. The property can be referenced by other properties.

```properties
test-support.core.test-class-id
```

In addition, the meta-annotation `@TestClassId` can also be used to inject the property. The annotation can be used
in the same way as the Spring `@Value` annotation is used for injecting fields or for autowiring.

## Config-Service

When using certain modules like `ms-core` the config-service is configured to be required by default.
This module provides an override for this behaviour to allow test executions out-of-the-box.