# Redis Locking Module: Lock Watchdog

This diagram illustrates the Lock Watchdog mechanism in the `locking-redis-lock` module.

```mermaid
classDiagram
    %% Core Classes
    class LockWatchdog {
        -ScheduledExecutorService scheduler
        -Map~String, WatchedLock~ watchedLocks
        -RedisLockOperations lockOperations
        -RedisLockProperties properties
        -LockMonitor lockMonitor
        -RedisLockErrorHandler errorHandler
        -boolean running
        +start(): void
        +stop(): void
        +registerLock(lockKey: String, ownerId: String, leaseTimeMs: long): void
        +unregisterLock(lockKey: String): void
        -scheduleLeaseExtension(): void
        -extendLeases(): void
        -extendLease(lockKey: String, watchedLock: WatchedLock): CompletableFuture~Boolean~
        -handleLeaseExtensionFailure(lockKey: String, watchedLock: WatchedLock, ex: Throwable): void
    }
    
    class WatchedLock {
        <<Inner Class>>
        -String ownerId
        -long leaseTimeMs
        -long lastExtensionTime
        -int failedAttempts
        -boolean valid
        +isValid(): boolean
        +invalidate(): void
        +recordExtensionAttempt(): void
        +recordExtensionSuccess(): void
        +recordExtensionFailure(): void
        +shouldExtendLease(now: long, extensionThresholdMs: long): boolean
    }
    
    class RedisLockOperations {
        +extendLease(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +validateOwnership(lockKey: String, ownerId: String): CompletableFuture~Boolean~
    }
    
    class RedisLockProperties {
        +getWatchdogEnabled(): boolean
        +getWatchdogIntervalMs(): long
        +getWatchdogThreadCount(): int
        +getLeaseExtensionThresholdPercent(): int
        +getMaxFailedLeaseExtensions(): int
    }
    
    class LockMonitor {
        +recordLeaseExtensionAttempt(lockKey: String): void
        +recordLeaseExtensionSuccess(lockKey: String): void
        +recordLeaseExtensionFailure(lockKey: String): void
        +recordLeaseExtensionTimeout(lockKey: String): void
    }
    
    class RedisLockErrorHandler {
        +handleLeaseExtensionError(lockKey: String, ex: Throwable): void
    }
    
    %% Lock Implementations
    class AbstractRedisLock {
        #registerWithWatchdog(lockKey: String, ownerId: String, leaseTimeMs: long): void
        #unregisterFromWatchdog(lockKey: String): void
    }
    
    class RedisReentrantLock {
        -lockKey: String
        -ownerId: String
        -leaseTimeMs: long
        +lock(): void
        +unlock(): void
    }
    
    class RedisReadWriteLock {
        -lockKey: String
        -ownerId: String
        -leaseTimeMs: long
    }
    
    class RedisStampedLock {
        -lockKey: String
        -ownerId: String
        -leaseTimeMs: long
    }
    
    %% Configuration
    class RedisLockAutoConfiguration {
        +lockWatchdog(properties: RedisLockProperties, lockOperations: RedisLockOperations, lockMonitor: LockMonitor, errorHandler: RedisLockErrorHandler): LockWatchdog
    }
    
    %% Lifecycle Management
    class LockComponentRegistry {
        -LockWatchdog watchdog
        +registerLockWatchdog(watchdog: LockWatchdog): void
        +getLockWatchdog(): LockWatchdog
    }
    
    class ApplicationLifecycle {
        <<Spring>>
        +onApplicationEvent(event: ContextRefreshedEvent): void
        +onApplicationEvent(event: ContextClosedEvent): void
    }
    
    %% Relationships
    LockWatchdog *-- WatchedLock : contains
    LockWatchdog --> RedisLockOperations : uses
    LockWatchdog --> RedisLockProperties : configures
    LockWatchdog --> LockMonitor : reports metrics
    LockWatchdog --> RedisLockErrorHandler : handles errors
    
    AbstractRedisLock --> LockWatchdog : registers with
    RedisReentrantLock --|> AbstractRedisLock : extends
    RedisReadWriteLock --|> AbstractRedisLock : extends
    RedisStampedLock --|> AbstractRedisLock : extends
    
    RedisLockAutoConfiguration --> LockWatchdog : creates
    LockComponentRegistry --> LockWatchdog : manages
    ApplicationLifecycle --> LockWatchdog : controls lifecycle
    
    %% Notes
    note for LockWatchdog "Automatically extends lock leases\nto prevent expiration while in use"
    note for WatchedLock "Tracks state of each watched lock"
    note for AbstractRedisLock "Base class that handles\nwatchdog registration"
```

## Lock Watchdog Overview

The Lock Watchdog is a critical component of the Redis Locking module that automatically extends the leases of acquired locks to prevent them from expiring while they are still in use. This ensures that locks remain valid even during long-running operations or when the client application is temporarily unable to explicitly extend the lease.

### Key Responsibilities

1. **Automatic Lease Extension**: Periodically extends the leases of all registered locks
2. **Failure Handling**: Manages failures in lease extension attempts
3. **Lock Registration**: Tracks locks that need lease extension
4. **Lock Unregistration**: Removes locks that have been released
5. **Metrics Collection**: Reports metrics on lease extension operations

## Core Components

### LockWatchdog

The `LockWatchdog` class is the central component responsible for managing lock leases:

```java
@Slf4j
public class LockWatchdog {
    private final ScheduledExecutorService scheduler;
    private final Map<String, WatchedLock> watchedLocks = new ConcurrentHashMap<>();
    private final RedisLockOperations lockOperations;
    private final RedisLockProperties properties;
    private final LockMonitor lockMonitor;
    private final RedisLockErrorHandler errorHandler;
    private volatile boolean running = false;
    
    public LockWatchdog(RedisLockOperations lockOperations, 
                        RedisLockProperties properties,
                        LockMonitor lockMonitor,
                        RedisLockErrorHandler errorHandler) {
        this.lockOperations = lockOperations;
        this.properties = properties;
        this.lockMonitor = lockMonitor;
        this.errorHandler = errorHandler;
        this.scheduler = Executors.newScheduledThreadPool(
            properties.getWatchdogThreadCount(),
            new ThreadFactoryBuilder()
                .setNameFormat("redis-lock-watchdog-%d")
                .setDaemon(true)
                .build());
    }
    
    public synchronized void start() {
        if (running || !properties.getWatchdogEnabled()) {
            return;
        }
        
        running = true;
        scheduleLeaseExtension();
        log.info("Lock watchdog started with interval: {}ms", 
                properties.getWatchdogIntervalMs());
    }
    
    public synchronized void stop() {
        if (!running) {
            return;
        }
        
        running = false;
        scheduler.shutdown();
        watchedLocks.clear();
        log.info("Lock watchdog stopped");
    }
    
    public void registerLock(String lockKey, String ownerId, long leaseTimeMs) {
        watchedLocks.put(lockKey, new WatchedLock(ownerId, leaseTimeMs));
        log.debug("Registered lock for watchdog: {}", lockKey);
    }
    
    public void unregisterLock(String lockKey) {
        watchedLocks.remove(lockKey);
        log.debug("Unregistered lock from watchdog: {}", lockKey);
    }
    
    private void scheduleLeaseExtension() {
        scheduler.scheduleAtFixedRate(
            this::extendLeases,
            properties.getWatchdogIntervalMs(),
            properties.getWatchdogIntervalMs(),
            TimeUnit.MILLISECONDS);
    }
    
    private void extendLeases() {
        if (!running || watchedLocks.isEmpty()) {
            return;
        }
        
        long now = System.currentTimeMillis();
        int extensionThresholdPercent = properties.getLeaseExtensionThresholdPercent();
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (Map.Entry<String, WatchedLock> entry : watchedLocks.entrySet()) {
            String lockKey = entry.getKey();
            WatchedLock watchedLock = entry.getValue();
            
            if (!watchedLock.isValid()) {
                unregisterLock(lockKey);
                continue;
            }
            
            long extensionThresholdMs = watchedLock.leaseTimeMs * extensionThresholdPercent / 100;
            
            if (watchedLock.shouldExtendLease(now, extensionThresholdMs)) {
                watchedLock.recordExtensionAttempt();
                lockMonitor.recordLeaseExtensionAttempt(lockKey);
                
                CompletableFuture<Void> future = extendLease(lockKey, watchedLock)
                    .exceptionally(ex -> {
                        handleLeaseExtensionFailure(lockKey, watchedLock, ex);
                        return false;
                    })
                    .thenAccept(success -> {
                        if (success) {
                            watchedLock.recordExtensionSuccess();
                            lockMonitor.recordLeaseExtensionSuccess(lockKey);
                        }
                    });
                
                futures.add(future);
            }
        }
        
        // Wait for all extensions to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .exceptionally(ex -> null)
            .join();
    }
    
    private CompletableFuture<Boolean> extendLease(String lockKey, WatchedLock watchedLock) {
        return lockOperations.extendLease(
            lockKey, 
            watchedLock.ownerId, 
            watchedLock.leaseTimeMs);
    }
    
    private void handleLeaseExtensionFailure(String lockKey, WatchedLock watchedLock, Throwable ex) {
        watchedLock.recordExtensionFailure();
        lockMonitor.recordLeaseExtensionFailure(lockKey);
        
        if (watchedLock.failedAttempts >= properties.getMaxFailedLeaseExtensions()) {
            log.warn("Max failed lease extensions reached for lock: {}, invalidating", 
                    lockKey, ex);
            watchedLock.invalidate();
        } else {
            errorHandler.handleLeaseExtensionError(lockKey, ex);
        }
    }
    
    private static class WatchedLock {
        private final String ownerId;
        private final long leaseTimeMs;
        private long lastExtensionTime;
        private int failedAttempts;
        private boolean valid = true;
        
        WatchedLock(String ownerId, long leaseTimeMs) {
            this.ownerId = ownerId;
            this.leaseTimeMs = leaseTimeMs;
            this.lastExtensionTime = System.currentTimeMillis();
        }
        
        boolean isValid() {
            return valid;
        }
        
        void invalidate() {
            valid = false;
        }
        
        void recordExtensionAttempt() {
            // Record attempt timestamp
        }
        
        void recordExtensionSuccess() {
            lastExtensionTime = System.currentTimeMillis();
            failedAttempts = 0;
        }
        
        void recordExtensionFailure() {
            failedAttempts++;
        }
        
        boolean shouldExtendLease(long now, long extensionThresholdMs) {
            return now - lastExtensionTime >= extensionThresholdMs;
        }
    }
}
```

### WatchedLock

The `WatchedLock` inner class tracks the state of each lock registered with the watchdog:

- `ownerId`: The ID of the lock owner
- `leaseTimeMs`: The lease time in milliseconds
- `lastExtensionTime`: The timestamp of the last successful lease extension
- `failedAttempts`: The number of consecutive failed lease extension attempts
- `valid`: Whether the lock is still valid

### Integration with Lock Implementations

Lock implementations register themselves with the watchdog when acquired and unregister when released:

```java
public abstract class AbstractRedisLock {
    protected final LockComponentRegistry lockComponentRegistry;
    
    protected void registerWithWatchdog(String lockKey, String ownerId, long leaseTimeMs) {
        LockWatchdog watchdog = lockComponentRegistry.getLockWatchdog();
        if (watchdog != null) {
            watchdog.registerLock(lockKey, ownerId, leaseTimeMs);
        }
    }
    
    protected void unregisterFromWatchdog(String lockKey) {
        LockWatchdog watchdog = lockComponentRegistry.getLockWatchdog();
        if (watchdog != null) {
            watchdog.unregisterLock(lockKey);
        }
    }
}
```

## Lease Extension Process

### 1. Registration

When a lock is acquired, it registers itself with the watchdog:

```java
public class RedisReentrantLock extends AbstractRedisLock {
    @Override
    public void lock() {
        // Acquire lock logic...
        
        // Register with watchdog
        registerWithWatchdog(lockKey, ownerId, leaseTimeMs);
    }
}
```

### 2. Scheduled Extension

The watchdog periodically checks all registered locks and extends their leases if needed:

1. For each registered lock, check if it's time to extend the lease
2. If yes, attempt to extend the lease using `RedisLockOperations`
3. Record success or failure of the extension attempt
4. Update metrics via `LockMonitor`

### 3. Unregistration

When a lock is released, it unregisters itself from the watchdog:

```java
public class RedisReentrantLock extends AbstractRedisLock {
    @Override
    public void unlock() {
        // Unregister from watchdog
        unregisterFromWatchdog(lockKey);
        
        // Release lock logic...
    }
}
```

## Configuration

The watchdog behavior is configured through `RedisLockProperties`:

```java
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
@Validated
public class RedisLockProperties {
    /**
     * Whether to enable the lock watchdog for automatic lease extension.
     */
    private boolean watchdogEnabled = true;
    
    /**
     * Interval in milliseconds at which the watchdog checks and extends leases.
     */
    @Min(100)
    private long watchdogIntervalMs = 1000;
    
    /**
     * Number of threads used by the watchdog for lease extension.
     */
    @Min(1)
    private int watchdogThreadCount = 1;
    
    /**
     * Percentage of lease time that should elapse before attempting extension.
     * For example, 33 means extend when 1/3 of the lease time has elapsed.
     */
    @Min(1)
    @Max(99)
    private int leaseExtensionThresholdPercent = 33;
    
    /**
     * Maximum number of consecutive failed lease extensions before invalidating a lock.
     */
    @Min(1)
    private int maxFailedLeaseExtensions = 3;
    
    // Getters and setters...
}
```

## Lifecycle Management

The watchdog is started when the application context is refreshed and stopped when the context is closed:

```java
@Component
public class LockWatchdogLifecycle implements ApplicationListener<ApplicationEvent> {
    private final LockWatchdog lockWatchdog;
    
    public LockWatchdogLifecycle(LockWatchdog lockWatchdog) {
        this.lockWatchdog = lockWatchdog;
    }
    
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ContextRefreshedEvent) {
            lockWatchdog.start();
        } else if (event instanceof ContextClosedEvent) {
            lockWatchdog.stop();
        }
    }
}
```

## Error Handling

The watchdog handles lease extension failures through the `RedisLockErrorHandler`:

```java
@Slf4j
public class RedisLockErrorHandler {
    public void handleLeaseExtensionError(String lockKey, Throwable ex) {
        if (ex instanceof RedisConnectionFailureException) {
            log.warn("Redis connection failure during lease extension for lock: {}", 
                    lockKey, ex);
        } else if (ex instanceof RedisCommandTimeoutException) {
            log.warn("Redis command timeout during lease extension for lock: {}", 
                    lockKey, ex);
        } else {
            log.error("Error extending lease for lock: {}", lockKey, ex);
        }
    }
}
```

## Metrics

The watchdog reports metrics through the `LockMonitor`:

```java
@Slf4j
public class LockMonitor {
    private final MeterRegistry meterRegistry;
    
    public LockMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    public void recordLeaseExtensionAttempt(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.attempt", 
                "lockKey", lockKey).increment();
    }
    
    public void recordLeaseExtensionSuccess(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.success", 
                "lockKey", lockKey).increment();
    }
    
    public void recordLeaseExtensionFailure(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.failure", 
                "lockKey", lockKey).increment();
    }
    
    public void recordLeaseExtensionTimeout(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.timeout", 
                "lockKey", lockKey).increment();
    }
}
```

## Key Design Considerations

### 1. Non-Blocking Operations

The watchdog uses `CompletableFuture` for non-blocking lease extensions, allowing it to handle many locks efficiently:

```java
CompletableFuture<Boolean> extendLease(String lockKey, WatchedLock watchedLock) {
    return lockOperations.extendLease(
        lockKey, 
        watchedLock.ownerId, 
        watchedLock.leaseTimeMs);
}
```

### 2. Thread Safety

The watchdog uses thread-safe collections and synchronization to handle concurrent operations:

```java
private final Map<String, WatchedLock> watchedLocks = new ConcurrentHashMap<>();
```

### 3. Failure Tolerance

The watchdog tolerates a configurable number of consecutive failures before invalidating a lock:

```java
if (watchedLock.failedAttempts >= properties.getMaxFailedLeaseExtensions()) {
    log.warn("Max failed lease extensions reached for lock: {}, invalidating", 
            lockKey, ex);
    watchedLock.invalidate();
}
```

### 4. Configurable Behavior

All aspects of the watchdog are configurable through properties:

- Whether the watchdog is enabled
- The interval at which leases are checked and extended
- The number of threads used for lease extension
- The threshold at which leases are extended
- The maximum number of consecutive failed lease extensions

### 5. Graceful Shutdown

The watchdog ensures proper cleanup during application shutdown:

```java
public synchronized void stop() {
    if (!running) {
        return;
    }
    
    running = false;
    scheduler.shutdown();
    watchedLocks.clear();
    log.info("Lock watchdog stopped");
}
```

## Sequence of Operations

1. **Lock Acquisition**:
   - Lock is acquired
   - Lock registers with watchdog
   - Watchdog adds lock to watched locks map

2. **Lease Extension**:
   - Watchdog periodically checks all watched locks
   - For each lock that needs extension, watchdog attempts to extend the lease
   - Success or failure is recorded
   - Metrics are updated

3. **Lock Release**:
   - Lock is released
   - Lock unregisters from watchdog
   - Watchdog removes lock from watched locks map

4. **Application Shutdown**:
   - Watchdog is stopped
   - Scheduler is shut down
   - Watched locks map is cleared

## Conclusion

The Lock Watchdog is a critical component that ensures locks remain valid during long-running operations. It provides automatic lease extension, failure handling, and metrics collection, all while being highly configurable and thread-safe.