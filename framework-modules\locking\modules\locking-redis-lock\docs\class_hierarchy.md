# Redis Locking Module: Class Hierarchy

## 1. Introduction

This document outlines the comprehensive class hierarchy for the `locking-redis-lock` module within the Destilink Framework. The hierarchy is designed to support all documented lock types, adhering to the **Async-First approach** detailed in the [Architecture Overview](architecture_overview.md) and aligning with standard Java patterns and Destilink Framework guidelines.

Understanding this hierarchy is crucial for developers working with or extending the locking module. It clarifies the roles, responsibilities, and relationships between different lock-related interfaces and classes. For a general overview of all documentation, please refer to the [main documentation index](index.md).

## 2. Core Design Principles

The class hierarchy is built upon the following core principles:

*   **Async-First**: Core operations are asynchronous, returning `CompletableFuture`s, as emphasized in the [Architecture Overview](architecture_overview.md) and [Lock Acquisition Mechanism](lock_acquisition.md) documents. Synchronous `java.util.concurrent.locks.Lock` methods serve as blocking wrappers.
*   **Standard Java Compliance**: The hierarchy integrates seamlessly with standard Java concurrency interfaces (`java.util.concurrent.locks.Lock`, `java.util.concurrent.locks.ReadWriteLock`).
*   **Clear Abstraction**: An abstract base class (`AbstractRedisLock`) encapsulates common Redis interaction logic, promoting code reuse and maintainability. Details on its internal workings can be found in [Implementation Details](implementation_details.md).
*   **Modularity**: Specific lock types (e.g., reentrant, state-based, read-write, stamped) are concrete implementations, allowing for clear separation of concerns.
*   **Configuration Driven**: Lock behavior, including eligibility for watchdog renewal (see [Watchdog Mechanism](watchdog.md)), is influenced by the [Configuration](configuration.md) provided to lock instances.
*   **Robust Error Handling**: All custom exceptions thrown by these classes extend `AbstractRedisLockException` as defined in the [Exception Handling Strategy](exception_handling.md).

For definitions of terms used here, please consult the [Glossary](glossary.md). The evolution of this design from previous considerations is partly captured in the [Modernization Plan](modernization.md).

## 3. Class Hierarchy Diagram

```mermaid
classDiagram
    direction TB

    class Lock {
        <<interface>>
        +lock()
        +tryLock()
        +unlock()
        +newCondition()
    }

    class ReadWriteLock {
        <<interface>>
        +readLock() Lock
        +writeLock() Lock
    }

    class AsyncLock {
        <<interface>>
        +lockAsync()
        +tryLockAsync()
        +tryLockAsync(long, TimeUnit)
        +unlockAsync()
    }
    AsyncLock --|> Lock

    class AsyncReadWriteLock {
        <<interface>>
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    AsyncReadWriteLock --|> ReadWriteLock

    class AbstractRedisLock {
        <<Abstract>>
        #String lockName
        #String ownerId
        #RedisLockOperations redisLockOperations
        #RedisLockProperties redisLockProperties
        #LockOwnerSupplier lockOwnerSupplier
        #LockSemaphoreHolder lockSemaphoreHolder
        +lockAsync()
        +tryLockAsync()
        +unlockAsync()
        +lock()
        +tryLock()
        +unlock()
    }
    AbstractRedisLock ..|> AsyncLock

    class RedisReentrantLock {
        +RedisReentrantLock(String, RedisLockOperations, ...)
    }
    RedisReentrantLock --|> AbstractRedisLock

    class RedisStateLock {
        +RedisStateLock(String, RedisLockOperations, ...)
    }
    RedisStateLock --|> AbstractRedisLock

    class RedisStampedLock {
        +RedisStampedLock(String, RedisLockOperations, ...)
        // +tryOptimisticRead()
        // +validate(long)
        // +tryConvertToWriteLock(long)
    }
    RedisStampedLock --|> AbstractRedisLock

    class RedisReadWriteLock {
        -AsyncLock readLockInstance
        -AsyncLock writeLockInstance
        +RedisReadWriteLock(String, RedisLockOperations, ...)
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    RedisReadWriteLock ..|> AsyncReadWriteLock

    class RedisReadLock {
        +RedisReadLock(String, RedisLockOperations, ...)
    }
    RedisReadLock --|> AbstractRedisLock

    class RedisWriteLock {
        +RedisWriteLock(String, RedisLockOperations, ...)
    }
    RedisWriteLock --|> AbstractRedisLock

    RedisReadWriteLock *-- RedisReadLock : creates
    RedisReadWriteLock *-- RedisWriteLock : creates
```

## 4. Interface and Class Descriptions

### 4.1. Core Java SDK Interfaces

* `java.util.concurrent.locks.Lock`: The standard synchronous lock interface from the JDK. All locks in this module are compatible with this interface.
* `java.util.concurrent.locks.ReadWriteLock`: The standard synchronous read-write lock interface from the JDK.

### 4.2. Custom Asynchronous Lock Interfaces

* **`com.tui.destilink.framework.locking.redis.lock.AsyncLock`**
  * **Extends:** `java.util.concurrent.locks.Lock`
  * **Purpose:** Defines the contract for asynchronous lock operations, crucial for the module's [Async-First design principle](architecture_overview.md). Methods return `CompletableFuture`s to avoid blocking application threads.
  * **Key Methods:** `lockAsync()`, `tryLockAsync()`, `unlockAsync()`.
  * **Note:** Implementations of this interface (typically via `AbstractRedisLock`) provide the synchronous `java.util.concurrent.locks.Lock` methods as blocking wrappers around these core asynchronous operations. This allows an `AsyncLock` instance to be used wherever a standard `Lock` is expected, while still promoting asynchronous usage as the primary pattern.

* **`com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock`**
  * **Extends:** `java.util.concurrent.locks.ReadWriteLock`
  * **Purpose:** Defines the contract for an asynchronous read-write lock, ensuring that its `readLock()` and `writeLock()` methods return `AsyncLock` instances.
  * **Key Methods:** `readLock()`, `writeLock()`.

### 4.3. Abstract Base Class

* **`com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncLock`
  * **Purpose:** Provides a foundational implementation for Redis-based distributed locks, encapsulating common logic.
  * **Key Responsibilities:**
    * Manages common fields: `lockName`, `ownerId`, injected services like `RedisLockOperations`, `RedisLockProperties`, `LockOwnerSupplier`, and `LockSemaphoreHolder`.
    * Implements the core asynchronous lock/unlock logic by delegating to `RedisLockOperations`, which in turn executes [Lua scripts](lua_scripts_2.md) for atomicity.
    * Implements the synchronous `java.util.concurrent.locks.Lock` interface methods (inherited via `AsyncLock`) by blocking on the results of its own asynchronous methods. This design ensures that while the core logic is asynchronous (aligning with the "Async-First" principle), instances of `AbstractRedisLock` and its subclasses can be directly used as standard `java.util.concurrent.locks.Lock`s. These synchronous methods effectively act as convenient blocking wrappers.
    * Handles the [unlock messaging mechanism](messaging.md) via `LockSemaphoreHolder` and `UnlockMessageListener`.
    * Orchestrates the [lock acquisition flow](lock_acquisition.md), including interactions with Redis Pub/Sub.
    * Manages interactions with the [LockWatchdog](watchdog.md) for locks configured as "Application-Instance-Bound" (see Section 5).
    * Constructs Redis keys according to the defined [Redis Key Schema](redis_key_schema.md).
    * The `newCondition()` method typically throws `UnsupportedOperationException`, as distributed conditions are complex and generally not supported.
    * Performance characteristics are discussed in [Performance Considerations](performance_considerations.md).

### 4.4. Concrete Redis Lock Implementations

This section describes the concrete lock classes. These implementations extend `AbstractRedisLock` (or compose locks that do) and therefore provide both the asynchronous API defined by `AsyncLock` and the standard synchronous `java.util.concurrent.locks.Lock` API through inherited blocking wrapper methods. All concrete lock implementations reside in the `com.tui.destilink.framework.locking.redis.lock.impl` package.

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisReentrantLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
  * **Purpose:** The primary, general-purpose, reentrant asynchronous distributed lock implementation. This corresponds to what might be referred to as a standard exclusive lock.
  * **Behavior:**
    * Uses Lua scripts (detailed in [Lua Scripts Documentation](lua_scripts_2.md)) for standard reentrant lock acquisition and release.
    * Can be configured as an "Application-Instance-Bound Lock" (see [Glossary](glossary.md) and Section 5 below).

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisStateLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
  * **Purpose:** A specialized lock that associates a user-defined state (as a String) with the lock itself. The state is stored in Redis alongside the lock information. This allows for more complex coordination scenarios where the lock's state is an integral part of the locking semantics.
  * **Behavior:**
    * Uses specific Lua scripts to manage the lock and its associated state atomically.
    * Provides methods to get and potentially update the state while the lock is held.
    * Can be configured as an "Application-Instance-Bound Lock."

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisStampedLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
  * **Purpose:** Provides a distributed lock with modes for reading, writing, and optimistic reading, inspired by `java.util.concurrent.locks.StampedLock`. It's designed for scenarios where read operations are frequent and write operations are rare, allowing for higher concurrency with optimistic reads.
  * **Behavior:**
    * Implements read, write, and optimistic read lock acquisition and release using specific Lua scripts.
    * Provides methods like `tryOptimisticRead()`, `validate(long stamp)`, `tryConvertToWriteLock(long stamp)`, etc.
    * The write lock part can be configured as an "Application-Instance-Bound Lock."
    * Optimistic reads do not block writers but require validation using a stamp.
    * Note: While inspired by `java.util.concurrent.locks.StampedLock`, its integration into the `AsyncLock` hierarchy means its API might be adapted; it primarily provides the core `AsyncLock` methods with additional stamped lock semantics.

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisReadWriteLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock`
  * **Purpose:** Provides a distributed read-write lock, allowing multiple readers or a single writer.
  * **Composition:** Internally creates and manages instances of `com.tui.destilink.framework.locking.redis.lock.impl.RedisReadLock` and `com.tui.destilink.framework.locking.redis.lock.impl.RedisWriteLock`.

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisReadLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
  * **Purpose:** Represents the read lock component of a `RedisReadWriteLock`.
  * **Behavior:** Employs specific Lua scripts that permit concurrent acquisition by multiple readers.

* **`com.tui.destilink.framework.locking.redis.lock.impl.RedisWriteLock`**
  * **Extends:** `com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`
  * **Purpose:** Represents the write lock component of a `RedisReadWriteLock`.
  * **Behavior:** Utilizes specific Lua scripts to ensure exclusive access for a single writer. Can also be configured as an "Application-Instance-Bound Lock."

## 5. "Application-Instance-Bound Lock" Characteristic

The "Application-Instance-Bound Lock" (defined in the [Glossary](glossary.md)) is not a distinct class type in this hierarchy. Instead, it is a behavioral characteristic that `RedisReentrantLock`, `RedisStateLock`, `RedisWriteLock`, and the write mode of `RedisStampedLock` can exhibit based on their configuration.

*   **Activation:** This behavior is activated when a lock instance is configured with:
    1.  A `leaseTime` (via `RedisLockProperties` - see [Configuration](configuration.md)) that is positive and meets the minimum threshold for watchdog activation.
    2.  A `LockOwnerSupplier` that provides a stable and unique identifier for the current application instance or process.
*   **Mechanism:**
    *   `AbstractRedisLock` checks these conditions.
    *   If met, upon successful lock acquisition, it registers the lock with the `LockWatchdog`.
    *   The `LockWatchdog` (detailed in [Watchdog Mechanism](watchdog.md)) then periodically renews the lock's TTL in Redis as long as the lock is considered held by the registered owner.

This approach ensures that long-running operations holding such locks do not suffer from premature expiration, enhancing the robustness of the distributed system. Metrics related to lock acquisition, including potential timeouts and wait times, are covered in the [Metrics Documentation](metrics.md). For users migrating or adopting this module, the [Migration Notes](migration_notes.md) provide useful context, although this specific hierarchy is for the v1.0 design. The overall testing approach for these components is detailed in the [Testing Strategy](testing_strategy.md).
