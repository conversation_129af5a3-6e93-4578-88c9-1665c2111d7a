# Redis Locking Module: Final Performance Considerations

## 1. Introduction

This document discusses the performance characteristics and tuning considerations for the `locking-redis-lock` module. The architecture is designed with performance in mind, particularly by minimizing direct Redis polling and leveraging atomic Lua script operations.

## 2. Key Performance Improvements Over Polling-Based Approaches

*   **Reduced Redis Load**: The primary performance gain comes from the non-polling lock acquisition strategy.
    *   Instead of clients constantly polling Redis, they passively wait on local semaphores.
    *   Redis commands are primarily executed only during actual acquisition attempts, release operations, or lease extensions by the watchdog. This drastically reduces idle Redis traffic and server CPU load, especially under contention.
*   **Efficient Client-Side Waiting**: Threads block passively on `java.util.concurrent.Semaphore` instances, consuming minimal client-side CPU resources while waiting, leading to better overall application efficiency.
*   **Atomic Operations with Lua Scripts**: Using Lua scripts for complex operations like acquisition (including reentrancy checks), release, and lease extension reduces network round trips between the client and Redis. This leads to lower latency for these critical operations.
*   **Optimized Reentrancy Management**: Storing reentrancy count and owner information directly within a Redis Hash for reentrant locks allows for efficient, atomic updates on the server side.

## 3. Performance Tuning and Considerations

*   **Redis Network Latency**: The performance of all Redis-based operations is highly sensitive to network latency between application instances and the Redis server(s). Minimize this latency for optimal results.
*   **Redis Server Throughput**: Ensure your Redis deployment (standalone or cluster) has sufficient CPU, memory, and network throughput to handle the peak load of lock operations plus other application traffic.
*   **Lua Script Complexity**: While the provided Lua scripts are designed for efficiency, be mindful that very complex Lua logic can consume Redis CPU. The current scripts are focused and performant.
*   **Pub/Sub Overhead**: Redis Pub/Sub is generally efficient. However, an extremely high volume of lock acquisitions and releases (leading to many Pub/Sub messages) can introduce some overhead. The design of one `UnlockMessageListener` per *bucket* (which subscribes to a pattern like `<prefix>:<bucketName>:__unlock_channels__:*` and then internally maps to specific `LockSemaphoreHolder` instances by parsing the `lockKey` from the message payload) is a balance between resource usage for listeners and message fan-out.
*   **`watchdogIntervalMs` (Global `RedisLockProperties`)**: This controls how frequently the `LockWatchdog`'s central scheduled task runs to check all monitored locks.
    *   A shorter interval means the watchdog is more responsive in initiating renewal checks for locks whose `nextExtensionTime` has passed.
    *   This is different from the per-lock `extensionIntervalMillis` (derived from `watchdogMaxTtl / watchdogRenewalMultiplier`), which dictates how often an individual lock *should* be renewed.
*   **`watchdogMaxTtl` and `watchdogRenewalMultiplier` (Global `RedisLockProperties`)**:
    *   These determine the actual TTL set by the watchdog (`watchdogMaxTtl`) and how frequently each lock is renewed (`watchdogMaxTtl / watchdogRenewalMultiplier`).
    *   Shorter `watchdogMaxTtl` values mean locks expire faster if a client crashes, but require more frequent renewals.
    *   A smaller `watchdogRenewalMultiplier` (e.g., 2 instead of 3) means more frequent renewals for a given `watchdogMaxTtl`, increasing resilience but also Redis traffic.
*   **`retryInterval` (Global default in `RedisLockProperties`, overridable per bucket/instance)**:
    *   This is the fallback polling interval used by a waiting thread *after* its wait on a semaphore (based on the lock's current TTL) times out without receiving a Pub/Sub notification.
    *   A shorter interval leads to faster retries if Pub/Sub messages are missed but increases Redis traffic in such scenarios.
*   **Lock Contention**: Extremely high contention for a *single specific lock key* can still lead to performance degradation as multiple clients vie for the same resource, leading to more retries and Pub/Sub signaling. Application design should aim to minimize such hotspots if possible (e.g., by using more granular locks).
*   **Redis Cluster and Hash Tags**: Correct use of hash tags (e.g., `{{<bucket_name>:<lock_key>}}`) in the [Redis Key Schema](redis_key_schema.md) is crucial for performance and correctness in a Redis Cluster environment. It ensures related keys (like main lock data and its state key, if applicable) are co-located on the same node, enabling multi-key Lua scripts.
*   **`lock()` vs. `tryLock()`**:
    *   `lock()`: Blocks indefinitely. Use when the thread absolutely must acquire the lock.
    *   `tryLock(long timeout, TimeUnit unit)`: Use when a bounded wait time is required to prevent indefinite blocking and allow alternative actions if the lock isn't acquired quickly.

## 4. Monitoring

Utilize the metrics exposed by the module via Micrometer (see [Metrics](metrics.md)) to monitor:
*   Lock acquisition times (average, max, percentiles).
*   Lock contention rates or wait times.
*   Watchdog activity (number of locks monitored, extensions performed/failed).
*   Error rates for lock operations.

This data is essential for identifying performance bottlenecks, understanding lock usage patterns, and appropriately tuning the configuration parameters.