# Modernized Redis Locking Module: Performance Considerations

This document discusses the performance characteristics and considerations of the modernized Redis Locking Module. The refactoring effort focused on improving performance compared to the previous implementation, particularly in high-contention scenarios. **Understanding these performance aspects is mandatory for effectively deploying and tuning the module, as defined in the source document `lock-final/redis-lock-improvements.md`.**

## Performance Improvements

The modernized architecture introduces several key performance improvements:

*   **Reduced Redis Load (Non-Polling)**: The most significant performance gain comes from replacing the polling-based lock acquisition with a notification-based mechanism.
    *   **Previous Implementation**: Constant polling generated a high volume of `EXISTS` or `GET` commands on Redis, even when locks were held, leading to increased CPU usage and network traffic on the Redis server.
    *   **Modernized Implementation**: Clients waiting for a lock now subscribe to a Pub/Sub channel and block on a semaphore. Redis commands are only executed when a lock acquisition is attempted or when the watchdog extends a lease. This drastically reduces idle traffic and server load.
*   **Efficient Client-Side Waiting**: Threads waiting for a lock now block passively on a `java.util.concurrent.Semaphore` instead of actively polling.
    *   **Previous Implementation**: Polling threads consumed CPU resources even when waiting.
    *   **Modernized Implementation**: Waiting threads consume minimal CPU resources, improving overall application efficiency.
*   **Atomic Operations with Lua Scripts**: Using Lua scripts for critical operations reduces the number of round trips between the client and Redis.
    *   **Impact**: Lower latency for lock acquisition, release, and extension operations, especially over higher-latency networks.
*   **Redis HashMaps for Reentrancy**: Storing reentrancy count and owner information in a Redis Hash for reentrant locks can be more efficient than managing this information across multiple simple keys or client-side state in a distributed context.
    *   **Impact**: Atomic updates to the reentrancy count within the hash are efficient Redis operations. The key access pattern for reentrant locks is consolidated to a single key with hash fields.

## Performance Considerations and Tuning

*   **Redis Latency**: The performance of the locking module is highly dependent on the network latency between the application instances and the Redis server(s). Minimize latency for optimal performance.
*   **Redis Throughput**: Ensure your Redis instance has sufficient throughput to handle the volume of lock operations, especially under high contention.
*   **Lua Script Performance**: While Lua scripts reduce round trips, complex scripts can consume Redis CPU resources. The scripts in this module are designed to be efficient.
*   **Pub/Sub Overhead**: While Pub/Sub is generally efficient, a very high volume of unlock notifications could introduce some overhead. The per-bucket listener approach helps manage this.
*   **Watchdog Interval**: The `watchdogIntervalMs` configuration property affects how often lock leases are extended. A shorter interval provides more resilience against premature expiration but increases Redis traffic from lease extensions. Tune this based on your lease times and network reliability.
*   **Retry Interval**: The `retryIntervalMs` (configured per bucket) determines the fallback re-polling interval. A shorter interval leads to faster retries after a missed Pub/Sub message but increases Redis traffic if notifications are consistently missed.
*   **Contention**: Performance can degrade under extremely high contention for the same lock key due to increased retries and signaling. Design your application to minimize contention where possible.
*   **Redis Cluster Hash Tags**: The use of hash tags (`{{...}}`) is crucial for performance and correctness in Redis Cluster by ensuring related keys are in the same hash slot. Ensure your keys follow the schema defined in [Redis Key Schema](redis_key_schema.md).
*   **`lock()` vs. `tryLock()`**: The `lock()` method blocks indefinitely. For scenarios where a timeout is required, use `tryLock(long timeout, TimeUnit unit)`. This provides predictable timeout behavior without relying on an implicit `defaultTimeout`.

## Monitoring

Leverage the metrics exposed by the module (see [Metrics](metrics.md)) to monitor key performance indicators such as lock acquisition time, contention levels, and watchdog activity. This data is essential for identifying performance bottlenecks and tuning the configuration.

By understanding these performance considerations and utilizing the available configuration and monitoring options, you can optimize the performance of the modernized Redis Locking Module in your application.