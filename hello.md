Of course. Given the critical nature of this task, a new, definitive plan must be generated from a first-principles analysis of our entire conversation. The existing `detailed-plan.md` is now considered a corrupted artifact. This new document will serve as the single source of truth, synthesizing all final decisions from `conversion.md` and `detailed-analysis.md` into a coherent, semantically consistent, and unambiguous plan.

Here is the Refactoring and Implementation Master Plan.

---

# Refactoring and Implementation Master Plan for Redis Distributed Locking

**Version:** 2.0
**Objective:** To provide a single, unambiguous, and semantically consistent master plan for the refactoring of the `locking-redis-lock` module. This document supersedes all previous plans and is the definitive guide for implementation, ensuring safety, correctness, and maintainability.

## 1. Glossary of Core Concepts

To ensure absolute clarity, the following terms are strictly defined and used consistently throughout this plan.

| Term                          | Definition                                                                                                                                                                                                                                                                                                                         |
| :---------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **`userProvidedLeaseTime`**   | The `java.time.Duration` a user requests when acquiring or extending a lock. This represents the *desired relative lease duration* from the moment of execution.                                                                                                                                                                   |
| **`originalLeaseTimeMillis`** | The `long` millisecond value of `userProvidedLeaseTime`. This value is stored in Redis to represent the user's intended full lease cycle. It is **only** modified by a user-initiated `acquire` or `extend` operation, **never** by the watchdog.                                                                                  |
| **`expiresAtMillis`**         | The **absolute Unix timestamp** (in milliseconds) at which the lock is set to expire in Redis. This value is calculated *on the Redis server* using `redis.call('TIME')` and is updated on every successful `acquire`, `extend`, or watchdog `refresh` operation. It is the primary source of truth for the lock's current expiry. |
| **`Idempotency Wrapper`**     | A mandatory pattern for all state-mutating Lua scripts. It uses a unique `requestUuid` and a `responseCacheTtl` to ensure that retried network requests do not cause duplicate operations.                                                                                                                                         |
| **`requestUuid`**             | A unique identifier (`java.util.UUID`) generated by `RedisLockOperationsImpl` for each logical operation (e.g., one call to `tryLock`). This same UUID is used for all internal retries of that single operation.                                                                                                                  |
| **`Lock Type Identifier`**    | A hardcoded string (e.g., `reentrant`, `stamped`) that uniquely identifies a lock's implementation type. It is a mandatory part of every Redis key to ensure semantic isolation.                                                                                                                                                   |
| **`Watchdog Window`**         | A calculated duration (`watchdog.interval * watchdog.factor`) used by the watchdog. It is **not** a TTL set in Redis. It is used to determine: 1) if a lock is eligible for monitoring (`userProvidedLeaseTime > watchdogWindow`), and 2) the refresh schedule for the watchdog.                                                   |

---

## 2. Foundational Principle: Redis Key Schema and Semantic Isolation

All subsequent logic depends on a strict and unambiguous Redis key schema that prevents cross-type lock contamination.

*   **Mandatory Key Format:** Every Redis key associated with a lock **must** include a **Lock Type Identifier**. The format is:
    ```
    <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}
    ```
*   **Example:**
    *   Reentrant Lock: `myApp:prod:__locks__:reentrant:{order-123}`
    *   Stamped Lock: `myApp:prod:__locks__:stamped:{order-123}`
*   **Scope:** This rule applies to **all** keys related to a lock, including the main lock key, the associated data hash, Pub/Sub channel names, and idempotency response cache keys.
*   **Responsibility:** The `LockBucket` and all lock factories are responsible for constructing these keys correctly. This is a non-negotiable, system-wide contract.

### Mermaid Diagram: Key Schema
```mermaid
graph TD
    subgraph "Redis Key Construction"
        A["Prefix: myApp:prod"] --> B["Bucket: orders"]
        B --> C["Locks Namespace: __locks__"]
        C --> D{"Lock Type Identifier"}
        D -->|"reentrant"| E_R["reentrant"]
        D -->|"stamped"| E_S["stamped"]
        E_R --> F["Hash Tagged Lock Name: {order-123}"]
        E_S --> F
        F --> G["Final Key: myApp:prod:__locks__:reentrant:{order-123}"]
    end
```

---

## 3. Foundational Principle: Idempotency and Lua Script Atomicity

To guarantee safety against network errors and client retries, all state-mutating operations are non-negotiably atomic and idempotent.

*   **Lua-Only Operations:** All operations that read or modify lock state, metadata, or TTLs **must** be executed within a single Lua script. **Direct Redis calls from Java (e.g., `PEXPIRE`, `HSET`) for lock management are strictly forbidden.**
*   **The Idempotency Wrapper Pattern:** Every state-mutating Lua script **must** implement this wrapper.
    1.  **Inputs:** The script receives a unique `requestUuid` and a `responseCacheTtl`.
    2.  **Check Cache:** The script first checks if a response for the `requestUuid` exists in the response cache. If yes, it returns the cached response immediately.
    3.  **Execute Logic:** If no cached response exists, the script executes its core logic (e.g., acquiring the lock).
    4.  **Cache Response:** Upon completion, the script caches its structured return value using the `requestUuid` as the key and `responseCacheTtl` as the TTL.
*   **Responsibility:** `RedisLockOperationsImpl` is responsible for generating the `requestUuid` for each logical operation and passing it to the Lua script.

### Mermaid Diagram: Idempotency Wrapper Flow
```mermaid
sequenceDiagram
    participant C as Client (AbstractRedisLock)
    participant O as Operations (RedisLockOperationsImpl)
    participant R as Redis (Lua Script)

    C->>O: tryLock(leaseTime)
    O->>O: Generate unique requestUuid
    O->>R: executeScript('acquire_lock.lua', requestUuid, responseCacheTtl, ...)
    R->>R: Check response cache for requestUuid
    alt Response Found
        R->>O: Return Cached Response
    else Response Not Found
        R->>R: Execute Core Lock Logic
        R->>R: Store New Response in Cache (with responseCacheTtl)
        R->>O: Return New Response
    end
    O->>C: Return Result
```

---

## 4. Lease Time and Expiry Management

This process is critical and must be implemented with precision to avoid ambiguity and race conditions.

1.  **Source of Truth:** The absolute expiry time, `expiresAtMillis`, calculated on the Redis server, is the definitive source of truth for a lock's current TTL.
2.  **Redis Storage:** Each lock is represented by two keys:
    *   A main lock key (e.g., `...:reentrant:{order-123}`) holding the `ownerId`. Its TTL is managed with `PEXPIREAT`.
    *   A companion **Redis Hash** (e.g., `...:reentrant:{order-123}:data`) storing the lock's metadata:
        *   `ownerId`: The lock owner's identifier.
        *   `expiresAtMillis`: The absolute Unix timestamp of expiry.
        *   `originalLeaseTimeMillis`: The user's intended relative lease time.
3.  **Lua Script Contract:**
    *   **Calculation:** All `expiresAtMillis` values are calculated inside Lua using `redis.call('TIME')` to prevent client-side clock skew.
    *   **Return Values:** Lua scripts that acquire or check locks **must** return a structured response (e.g., a Lua table) containing at least: `status_code`, `expiresAtMillis`, and `originalLeaseTimeMillis`. The Java layer (`RedisLockOperationsImpl`) will parse this into a result object.

### Mermaid Diagram: Lease Acquisition and Data Flow
```mermaid
sequenceDiagram
    participant User
    participant LockImpl as AbstractRedisLock
    participant LockOps as RedisLockOperationsImpl
    participant Lua as Redis (acquire_lock.lua)

    User->>LockImpl: lock(userProvidedLeaseTime = 30s)
    LockImpl->>LockOps: acquireLockAsync(relativeLeaseTimeMillis = 30000)
    LockOps->>Lua: executeScript(30000, ...)
    Lua->>Lua: now = redis.time()
    Lua->>Lua: expiresAtMillis = now + 30000
    Lua->>Lua: Store in Hash: {ownerId, expiresAtMillis, originalLeaseTimeMillis: 30000}
    Lua->>Lua: SET main_key with PEXPIREAT expiresAtMillis
    Lua-->>LockOps: Return {status: 1, expiresAtMillis, originalLeaseTimeMillis: 30000}
    LockOps-->>LockImpl: CompletableFuture<LockResult>
    LockImpl-->>User: Lock Acquired
```

---

## 5. The Lock Watchdog Mechanism

The watchdog ensures long-running processes do not lose their locks prematurely. Its logic is precise and non-configurable beyond its core operational parameters.

1.  **Activation:**
    *   The `LockWatchdog` service is **always active**. There is no `enabled` flag.    *   It **only monitors** a specific lock instance if **both** conditions are met:
        1.  The `LockOwnerSupplier` determines the lock is application-instance-bound (`canUseWatchdog() == true`).
        2.  The `userProvidedLeaseTime` is greater than the calculated `watchdogWindow` (`interval * factor`).
2.  **Initial Lock TTL:** When a lock is acquired:
    *   If the watchdog is **not** used, the lock is set in Redis with a TTL corresponding to the full `userProvidedLeaseTime`.
    *   If the watchdog **is** used, the lock is initially set in Redis with a TTL corresponding to the `watchdogWindow`. The full `userProvidedLeaseTime` is still stored as `originalLeaseTimeMillis` in the data hash.
3.  **Refresh Logic:** The watchdog runs periodically based on its `interval`.
    *   **It NEVER modifies `originalLeaseTimeMillis`**.
    *   **Standard Refresh:** It extends the lock's `expiresAtMillis` to be `redis.time() + watchdogWindow`.
    *   **Final Leg:** As the lock approaches its final, user-intended expiry (`userIntendedExpireTimeMillis`), the watchdog performs a final refresh, setting the `expiresAtMillis` to be exactly `userIntendedExpireTimeMillis`, and then unregisters the lock.

### Mermaid Diagram: Watchdog Lock State

```mermaid
stateDiagram-v2
    [*] --> NotMonitored: Lock Acquired
    note right of NotMonitored: Lock lease is too short or not instance-bound

    [*] --> Monitored: Lock acquired with long lease
    state Monitored {
        [*] --> StandardRefresh
        StandardRefresh --> StandardRefresh: Extends by watchdogWindow
        StandardRefresh --> FinalLeg: User expiry approaches
        FinalLeg --> [*]: Lock expires or released
    }
    note right of StandardRefresh: Watchdog is active for this lock

    NotMonitored --> Monitored: User extends lease
    Monitored --> NotMonitored: User shortens lease
    NotMonitored --> [*]: Lock expires or released
```

---

## 6. Lock Acquisition Concurrency and Flow

To ensure the application remains responsive, all blocking and I/O-intensive work is offloaded to virtual threads.

1.  **Virtual Thread Handoff:** Every public lock method (`lock`, `tryLock`, `unlockAsync`, etc.) **must** immediately delegate its execution to a task running on a `VirtualThreadPerTaskExecutor`.
2.  **MDC Propagation:** The MDC context from the calling thread **must** be captured and restored on the virtual thread to ensure consistent logging.
3.  **Acquisition Loop (on Virtual Thread):**
    *   **Register First:** The logic **must** first register its interest in an unlock signal with the `UnlockMessageListenerManager` (by getting/creating a `LockSemaphoreHolder`). This prevents a race condition where an unlock message is missed.
    *   **Attempt Acquisition:** It then calls `RedisLockOperations` to attempt to acquire the lock.
    *   **Wait:** If the lock is busy, the virtual thread parks by waiting on a `CompletableFuture` managed by the `LockSemaphoreHolder`. The wait timeout is `min(TTL from script, retryInterval)`.
    *   **Loop:** This loop continues until the lock is acquired or the overall `acquireTimeout` is breached.
4.  **`acquireTimeout` Precedence:**
    *   `acquireTimeout` **must not** interrupt an in-flight Redis command.
    *   The result of a Redis command (success or a Redis-specific error) **always takes precedence** over a concurrently expiring `acquireTimeout`. A `TimeoutException` is only thrown if the timeout is reached *between* Redis operations.

---

## 7. Final Configuration Properties

Based on this plan, the following is the definitive list of required configuration properties. All other previously discussed properties are to be removed.

### `destilink.fw.locking.redis`

| Property               | Type       | Description                                                                                                  |
| :--------------------- | :--------- | :----------------------------------------------------------------------------------------------------------- |
| `enabled`              | `boolean`  | Master switch to enable/disable the entire Redis locking feature.                                            |
| `response-cache-ttl`   | `Duration` | TTL for idempotency records in the Redis response cache. Must be long enough to cover client retry windows.  |
| `state-key-expiration` | `Duration` | TTL for auxiliary state keys (e.g., for `StateLock`). Ensures temporary state does not persist indefinitely. |

### `destilink.fw.locking.redis.defaults` (Overridable by Builders)

| Property          | Type       | Description                                                                                                                                                                            |
| :---------------- | :--------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `lease-time`      | `Duration` | Default lease duration for a lock if not specified by the user.                                                                                                                        |
| `retry-interval`  | `Duration` | Default wait time between attempts to acquire a busy lock (used with TTL fallback) **and** for retrying a failed Redis command.                                                        |
| `max-retries`     | `int`      | Default max number of retries for a **single failing Redis operation** (e.g., due to transient network issues). This does **not** limit the number of attempts to acquire a busy lock. |
| `acquire-timeout` | `Duration` | Default overall timeout for a `tryLock` operation.                                                                                                                                     |

### `destilink.fw.locking.redis.watchdog` (System-Level)

| Property                     | Type       | Description                                                                                                                                     |
| :--------------------------- | :--------- | :---------------------------------------------------------------------------------------------------------------------------------------------- |
| `interval`                   | `Duration` | How often the watchdog runs to check and extend active locks.                                                                                   |
| `factor`                     | `double`   | Multiplier used with `interval` to calculate the `watchdogWindow`. A lock is only monitored if its `userProvidedLeaseTime` exceeds this window. |
| `core-pool-size`             | `int`      | Core pool size for the watchdog's dedicated `ScheduledExecutorService`.                                                                         |
| `thread-name-prefix`         | `String`   | Name prefix for watchdog executor threads for observability.                                                                                    |
| `shutdown-await-termination` | `Duration` | Timeout for gracefully shutting down the watchdog's executor service.                                                                           |

---

This master plan provides a complete, consistent, and safe path forward for the refactoring. All implementation and documentation efforts must strictly adhere to these principles and specifications.