# Exception Handling Hierarchy

```mermaid
classDiagram
    class AbstractRedisLockException {
        <<Abstract>>
        +String lockName
        +String lockType
        +String lockOwnerId
        +String requestUuid
        +String message
        +Throwable cause
        +Marker getMarker()
        #void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    note for AbstractRedisLockException "Extends MarkerNestedRuntimeException and implements ExceptionMarkerProvider<br/>Base for all lock-specific exceptions"

    class MarkerNestedRuntimeException {
        <<Framework Base>>
        +Marker getMarker()
    }
    AbstractRedisLockException --|> MarkerNestedRuntimeException

    class ExceptionMarkerProvider {
        <<Interface>>
        +Marker getMarker()
    }
    MarkerNestedRuntimeException ..|> ExceptionMarkerProvider

    class LockAcquisitionException {
        +String redisError
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockAcquisitionException --|> AbstractRedisLockException

    class LockTimeoutException {
        +long timeoutMillis
        +int attempts
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockTimeoutException --|> AbstractRedisLockException

    class LockReleaseException {
        +String attemptedOwnerId
        +String actualOwnerId
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockReleaseException --|> AbstractRedisLockException

    class LeaseExtensionException {
        +long leaseTimeMillis
        +Object extensionResult
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LeaseExtensionException --|> AbstractRedisLockException

    class LockInterruptedException {
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockInterruptedException --|> AbstractRedisLockException

    class LockCommandException {
        +String scriptName
        +String commandName
        +String arguments
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockCommandException --|> AbstractRedisLockException

    class LockConnectionException {
        +String connectionError
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockConnectionException --|> AbstractRedisLockException

    class LockNotOwnedException {
        +String expectedOwnerId
        +String actualOwnerId
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockNotOwnedException --|> AbstractRedisLockException

    class LockNotFoundException {
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockNotFoundException --|> AbstractRedisLockException

    class StateMismatchException {
        +String stateKey
        +String expectedState
        +String actualState
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    StateMismatchException --|> AbstractRedisLockException

    class StateNotFoundException {
        +String stateKey
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    StateNotFoundException --|> AbstractRedisLockException

    class StateUpdateException {
        +String stateKey
        +String expectedState
        +String actualState
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    StateUpdateException --|> AbstractRedisLockException

    class LockConfigurationException {
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockConfigurationException --|> AbstractRedisLockException

    class LockSerializationException {
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockSerializationException --|> AbstractRedisLockException

    class LockDeserializationException {
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockDeserializationException --|> AbstractRedisLockException

    class LockInternalException {
        +void populateSpecificMarkers(Map<String, Object> contextMap)
    }
    LockInternalException --|> AbstractRedisLockException

    class RedisLockErrorHandler {
        +AbstractRedisLockException handleRedisException(Throwable t, String lockName)
        +AbstractRedisLockException handleLockException(Exception e, String lockName, String operation)
        +AbstractRedisLockException wrapCompletionException(CompletionException e, String lockName, String operation)
    }
    note for RedisLockErrorHandler "Translates low-level Redis exceptions<br/>into contextual lock exceptions"
```
