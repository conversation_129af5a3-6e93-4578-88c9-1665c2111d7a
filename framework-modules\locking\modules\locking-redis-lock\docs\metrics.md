# Redis Locking Module: Final Metrics Documentation

## 1. Introduction

This document outlines the key metrics exposed by the `locking-redis-lock` module. These metrics provide visibility into the module's performance, usage patterns, and health, facilitating monitoring and troubleshooting. The module leverages Micrometer for metrics collection, allowing integration with various monitoring systems.

## 2. Metric Categories and Key Metrics

The following categories of metrics are essential for understanding the behavior of the locking module. Specific metric names will follow Micrometer conventions and should be prefixed appropriately (e.g., `destilink.locking.redis.`).

### 2.1. Lock Acquisition Metrics

*   **`lock.acquisition.time` (Timer)**: Measures the duration of lock acquisition attempts.
    *   Tags: `lockName`, `bucketName`, `lockType`, `success` (true/false).
    *   Useful for identifying slow lock acquisitions or high contention.
*   **`lock.acquisition.attempts` (Counter)**: Counts the number of lock acquisition attempts.
    *   Tags: `lockName`, `bucketName`, `lockType`, `result` (e.g., "acquired", "timeout", "interrupted", "error").
*   **`lock.wait.time` (Timer)**: Measures the duration threads spend waiting for a lock to become available (i.e., time spent blocked on `LockSemaphoreHolder`).
    *   Tags: `lockName`, `bucketName`, `lockType`.
*   **`lock.active.waiters` (Gauge)**: The current number of threads actively waiting to acquire a specific lock.
    *   Tags: `lockName`, `bucketName`, `lockType`.
    *   Typically implemented by querying `LockSemaphoreHolder.getWaitersCount()`.

### 2.2. Lock Lifecycle Metrics

*   **`lock.held.duration` (Timer)**: Measures how long locks are held once acquired.
    *   Tags: `lockName`, `bucketName`, `lockType`, `ownerId`.
*   **`lock.active.count` (Gauge)**: The current number of actively held locks.
    *   Tags: `bucketName`, `lockType`, `scope` (application/distributed).
    *   Can be derived from `LockWatchdog`'s monitored locks or a separate registry if non-watchdog-managed locks need to be tracked.

### 2.3. Watchdog Metrics

*   **`lock.watchdog.extensions` (Counter)**: Counts the number of successful lease extensions performed by the watchdog.
    *   Tags: `lockName`, `bucketName`.
*   **`lock.watchdog.extension.failures` (Counter)**: Counts the number of failed lease extensions.
    *   Tags: `lockName`, `bucketName`, `reason` (e.g., "owner_mismatch", "redis_error").
*   **`lock.watchdog.monitored.count` (Gauge)**: The current number of locks being actively monitored by the watchdog.

### 2.4. Pub/Sub Messaging Metrics

*   **`lock.unlock.notifications.received` (Counter)**: Number of unlock messages received by `UnlockMessageListener` instances.
    *   Tags: `bucketName` (or `channelName` if more granular).
*   **`lock.unlock.notifications.published` (Counter)**: Number of unlock messages published by the `unlock.lua` script.
    *   Tags: `bucketName` (or `channelName`).
*   **`lock.semaphore.signals` (Counter)**: Number of times semaphores in `LockSemaphoreHolder` were signaled.
    *   Tags: `lockName`, `bucketName`.

### 2.5. Lua Script Execution Metrics

*   **`lock.lua.script.execution.time` (Timer)**: Measures the execution time of Lua scripts on the Redis server.
    *   Tags: `scriptName` (e.g., "try_lock", "unlock", "extend_lock").
*   **`lock.lua.script.execution.errors` (Counter)**: Counts errors during Lua script execution.
    *   Tags: `scriptName`.

### 2.6. Error Metrics

*   **`lock.operation.errors` (Counter)**: Counts general errors encountered during lock operations (acquisition, release, extension), categorized by exception type.
    *   Tags: `operationType` (acquire, release, extend, state_update), `exceptionType`.

## 3. Configuration and Exposure

*   Metrics are exposed via Micrometer, which integrates with Spring Boot Actuator.
*   Application teams can configure their Micrometer setup to export these metrics to their chosen monitoring system (e.g., Prometheus, Datadog, New Relic).
*   The `LockMonitor` bean, conditionally enabled, is responsible for registering and updating these metrics.

## 4. Using Metrics

These metrics are vital for:
*   **Performance Tuning**: Identifying bottlenecks in lock acquisition or high contention areas.
*   **Troubleshooting**: Diagnosing issues related to lock failures, watchdog problems, or messaging delays.
*   **Capacity Planning**: Understanding lock usage patterns to ensure Redis resources are adequate.
*   **Alerting**: Setting up alerts for critical issues like high error rates, excessive wait times, or watchdog failures.

By providing comprehensive metrics, the `locking-redis-lock` module aims to be a transparent and manageable component within the Destilink Framework.