# Redis Locking Module: Final Testing Strategy

## 1. Introduction

This document outlines the comprehensive testing strategy for the `locking-redis-lock` module. A robust testing approach is crucial to ensure the correctness, reliability, and performance of this distributed locking mechanism, particularly given its interactions with Redis and concurrent access patterns. This strategy consolidates best practices from previous planning.

## 2. Testing Principles

*   **Layered Testing**: Employ a combination of unit tests, integration tests, and performance tests.
*   **Focus on Correctness**: Ensure core locking logic (acquisition, release, reentrancy using Redis Hashes, state management, read-write separation) functions correctly under diverse conditions.
*   **Atomicity Verification**: Specifically test scenarios relying on the atomicity of Lua scripts to prevent race conditions.
*   **Distributed Environment Simulation**: Design tests that simulate multiple clients concurrently acquiring and releasing locks.
*   **Error Handling Coverage**: Verify correct handling of various error conditions (Redis issues, command failures, invalid operations) and ensure appropriate, contextual exceptions are thrown.
*   **Configuration Validation**: Test behavior with different valid and invalid configurations.
*   **Performance Validation**: Conduct performance tests to measure throughput, latency, and resource consumption, validating the benefits of the non-polling mechanism.

## 3. Testing Layers

### 3.1. Unit Tests

*   **Purpose**: Test individual components and classes in isolation.
*   **Focus**: Logic within classes that do not directly interact with Redis or require complex Spring context. External dependencies (like `RedisLockOperations` or `ScriptLoader` for a component under test) should be mocked.
*   **Examples**:
    *   Logic within `LockSemaphoreHolder` (waiter counting, semaphore signaling).
    *   Configuration parsing and validation in `RedisLockProperties` and `LockBucketConfig` (if any complex logic exists beyond simple setters/getters).
    *   Utility classes.
    *   Parts of `AbstractRedisLock` or concrete lock implementations that involve state management or logic not directly tied to Redis commands (e.g., parameter preparation for scripts, internal state checks before calling Redis operations).
    *   `RedisLockErrorHandler` logic for mapping specific internal errors to exception types (mocking the input errors).

### 3.2. Integration Tests

*   **Purpose**: Test the interaction between the module's components and with a real or embedded Redis instance.
*   **Focus**:
    *   **Redis Interaction**:
        *   Verify Lua scripts are correctly loaded and executed.
        *   Ensure Redis keys (including Hashes for reentrancy, Sets for read locks) are created, manipulated, and deleted as expected.
    *   **End-to-End Lock Behavior**:
        *   Test lock acquisition (`lock()`, `tryLock()`, `tryLock(timeout)`), release (`unlock()`, `fullyUnlock()`), reentrancy, state management (`RedisStateLock`), and read-write lock (`RedisReadWriteLock`) semantics.
        *   Verify correct owner and reentrancy count management in Redis Hashes for reentrant locks.
        *   **`RedisReadWriteLock` Specific TTL and State Tests (aligning with Redisson-inspired Lua logic)**:
            *   Verify the correct creation, TTL setting (based on `leaseTimeMs` of the specific read acquisition), and eventual deletion of **individual read lock timeout keys** (e.g., `myApp:__lock_buckets__:resource:__rwttl__:{myRWLock}:<readerId>:<reentrantCount>`) upon each reentrant read lock acquisition and corresponding release.
            *   Test that the **main `RedisReadWriteLock` Hash key's TTL** is dynamically and correctly updated:
                *   Upon read acquisition, it's extended to at least the TTL of the newly acquired individual read lock timeout key.
                *   Upon read release, it's recalculated based on the maximum remaining TTL of any *other* active individual read lock timeout keys for *any* reader.
                *   Upon write acquisition, its TTL is set/extended appropriately.
            *   Ensure the main `RedisReadWriteLock` Hash key (containing `mode` and reader/writer reentrancy counts) is correctly deleted when all its constituent locks (all reentrant instances of reads and writes) are fully released and their respective timeout/lease mechanisms conclude.
            *   Test the atomicity of mode changes within the main Hash (e.g., from 'write' to 'read' upon write lock release if readers were waiting or if readers still hold locks).
        *   Test asynchronous lock operations provided by `AsyncLock` implementations. Verify that `CompletableFuture`s complete correctly (success, failure, timeout, interruption) under various contention scenarios and that the non-blocking behavior is maintained.
    *   **Concurrency**:
        *   Simulate multiple threads/processes concurrently attempting to acquire/release the same and different locks to uncover race conditions, deadlocks, or synchronization issues.
        *   Test fairness if applicable to semaphore configurations.
    *   **Watchdog Mechanism**:
        *   Verify correct registration and unregistration of locks.
        *   Test that the watchdog correctly extends lock leases for application-instance-bound locks whose lease time exceeds `watchdogMaxTtl`.
        *   Verify that locks expire correctly if the watchdog stops renewing (simulating client failure).
        *   Test the `extend_lock.lua` script's atomicity and ownership check.
    *   **Pub/Sub Messaging & Non-Polling Wait**:
        *   Verify that unlock notifications are published correctly via the `unlock.lua` script to the specific lock's channel (e.g., `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}`), with the message payload containing *only* the `UnlockType`. The `lockKey` is derived from the channel name by the listener.
        *   Test that `UnlockMessageListener` (per bucket, subscribed to a pattern like `<prefix>:<bucketName>:__unlock_channels__:*`) receives messages, correctly parses the `lockKey` and `UnlockType` from the payload, and signals the correct `LockSemaphoreHolder`.
        *   Ensure waiting threads are awakened by semaphore signals.
        *   Test the fallback re-polling mechanism (based on lock TTL or `retryInterval`) when Pub/Sub messages are intentionally suppressed or delayed.
        *   Verify `LockSemaphoreHolder` correctly drains stale permits.
    *   **Error Handling & Exception Translation**:
        *   Simulate Redis errors (connection issues, command failures, script errors) and verify that `RedisLockErrorHandler` translates them into the correct, context-rich `AbstractRedisLockException` subtypes with appropriate `ExceptionMarkerProvider` data.
    *   **Configuration Scenarios**: Test module behavior with various valid configurations (e.g., watchdog enabled/disabled per bucket, different lease times, retry intervals).
*   **Tools**:
    *   Testcontainers with a Redis container or an embedded Redis server (e.g., `redis-server --port 0` for dynamic port allocation).
    *   Concurrency utilities like `CountDownLatch`, `CyclicBarrier`, and thread pools to simulate concurrent access.

### 3.3. Performance Tests

*   **Purpose**: Measure the module's performance characteristics and validate improvements.
*   **Focus**:
    *   Throughput (lock acquisitions/releases per second).
    *   Latency of lock operations.
    *   Resource consumption (client-side CPU, Redis CPU/memory) under various loads.
    *   Scalability with increasing numbers of clients and lock contention.
*   **Scenarios**:
    *   Low, medium, and high contention for the same lock key.
    *   Multiple clients operating on different lock keys.
    *   Long-held locks with watchdog activity.
*   **Metrics**: Utilize Micrometer metrics exposed by the `LockMonitor` for data collection.
*   **Comparison**: Where possible, compare against benchmarks from any previous polling-based implementation to quantify improvements.

## 4. Specific Testing Considerations

*   **Reentrancy (Redis Hashes)**: Thoroughly test reentrant lock acquisition and release with multiple nested calls from the same owner. Verify that the reentrancy count in the Redis Hash is correctly managed and the lock is only fully released when the count drops to zero. Test scenarios where different owners attempt to acquire a reentrant lock held by another.
*   **State Locks**: Test state initialization (including `getOrInitializeState`), conditional lock acquisition based on state, atomic state updates (`updateState`), and state changes during unlock. Verify idempotency of state update operations using the response cache mechanism.
*   **Read-Write Locks**: Test concurrent read access, exclusive write access, a writer blocking readers, readers blocking a writer, and potential reentrancy within read or write locks if supported.
*   **`LockSemaphoreHolder` Management**: Ensure `LockSemaphoreHolder` instances (and their listeners) are correctly created on demand per lock key and are eligible for garbage collection (e.g., via Guava Cache `weakValues()`) when no longer referenced by waiting threads.
*   **Redis Cluster**: If the module is intended for Redis Cluster, integration tests **must** be run against a Redis Cluster setup to verify correct behavior with hash tags (e.g., `{<lockName>}` ensuring keys like `<prefix>:<bucketName>:__locks__:{<lockName>}` and `<prefix>:<bucketName>:__locks__:{<lockName>}:state` are co-located) and key distribution for multi-key Lua scripts.

## 5. Test Coverage

*   Aim for high code coverage (e.g., >80-85% for critical components) as measured by tools like JaCoCo.
*   Focus on testing observable behavior and contracts rather than internal implementation details where possible, to make tests less brittle.
*   Ensure all public API methods of lock implementations are covered.
*   Pay special attention to edge cases, error conditions, and boundary values in configurations.

By adhering to this comprehensive testing strategy, the `locking-redis-lock` module can achieve a high degree of reliability and robustness.