# RedisReentrantLock Activity Diagram

RedisReentrantLock is a Redis-based implementation of a reentrant mutual exclusion lock. It allows the same thread to acquire the lock multiple times (reentrancy) without deadlocking.

## Key Features

- Reentrant Acquisition: Supports multiple acquisitions by the same thread
- Lease Extension: Extends the lock's lease in Redis during reentrant acquisitions
- Hold Count Tracking: Maintains a thread-local counter for reentrant acquisitions
- Full Unlock Option: Provides a method to forcefully release the lock regardless of hold count

## Activity Diagram

```mermaid
flowchart TD
    %% Lock Acquisition
    start([Start]) --> lock["lock()"]
    lock --> tryAcquire["tryAcquireLock(requestUuid, lockOwnerId)"]
    
    tryAcquire --> checkReentrant{"holdsCount\n> 0?"}
    checkReentrant -- Yes --> incrementCount["Increment holdsCount"]
    checkReentrant -- No --> executeAcquire["Execute 'try_lock' Lua script"]
    
    incrementCount --> extendLease["Execute 'extend_lock' Lua script"]
    extendLease --> checkExtendResult{"Result\n== 1?"}
    checkExtendResult -- Yes --> returnSuccess1[Return null]
    checkExtendResult -- No --> throwLeaseExtension[Throw LeaseExtensionException]
    
    executeAcquire --> checkAcquireResult{"Result\nnull?"}
    checkAcquireResult -- Yes --> setHoldsCount["Set holdsCount = 1"]
    checkAcquireResult -- No --> returnTTL[Return TTL]
    setHoldsCount --> registerWatchdog["Register with LockWatchdog"]
    setHoldsCount --> returnSuccess2[Return null]
    
    %% Lock Release
    unlock["unlock()"] --> releaseLock["releaseLock(requestUuid, lockOwnerId)"]
    
    releaseLock --> checkHeldByThread{"holdsCount\n> 0?"}
    checkHeldByThread -- No --> throwIllegalMonitor[Throw IllegalMonitorStateException]
    checkHeldByThread -- Yes --> decrementCount["Decrement holdsCount"]
    
    decrementCount --> checkZero{"holdsCount\n== 0?"}
    checkZero -- No --> returnPartial[Return 0]
    checkZero -- Yes --> cleanupThreadLocal["Remove ThreadLocal"]
    cleanupThreadLocal --> unregisterWatchdog["Unregister from LockWatchdog"]
    unregisterWatchdog --> executeUnlock["Execute 'unlock' Lua script"]
    executeUnlock --> returnResult[Return result]
    
    %% Full Unlock
    fullyUnlock["fullyUnlock()"] --> checkHeldByThread2{"holdsCount\n> 0?"}
    checkHeldByThread2 -- No --> throwIllegalMonitor2[Throw IllegalMonitorStateException]
    checkHeldByThread2 -- Yes --> cleanupThreadLocal2["Remove ThreadLocal"]
    cleanupThreadLocal2 --> unregisterWatchdog2["Unregister from LockWatchdog"]
    unregisterWatchdog2 --> executeForceUnlock["Execute 'unlock' Lua script\nwith decrementReentrant=false"]
    executeForceUnlock --> checkUnlockResult{"Result\n== 1?"}
    checkUnlockResult -- Yes --> returnSuccess3[Return]
    checkUnlockResult -- No --> logWarning["Log warning"]
    logWarning --> returnSuccess4[Return]