# Lock Implementation Hierarchy

```mermaid
classDiagram
    class Lock {
        <<java.util.concurrent.locks.Lock>>
        +void lock()
        +void lockInterruptibly()
        +boolean tryLock()
        +boolean tryLock(long time, TimeUnit unit)
        +void unlock()
        +Condition newCondition()
    }

    class AsyncLock {
        <<Interface - extends Lock>>
        +CompletableFuture<Void> lockAsync()
        +CompletableFuture<Void> lockInterruptiblyAsync()
        +CompletableFuture<Boolean> tryLockAsync()
        +CompletableFuture<Boolean> tryLockAsync(long time, TimeUnit unit)
        +CompletableFuture<Void> unlockAsync()
    }
    AsyncLock --|> Lock

    class AbstractRedisLock {
        <<Abstract>>
        #LockComponentRegistry componentRegistry
        #LockBucketConfig bucketConfig
        #String lockName
        #String lockOwnerId
        #long leaseTimeMs
        #long retryIntervalMs
        +lock()
        +lockInterruptibly()
        +boolean tryLock()
        +boolean tryLock(long time, TimeUnit unit)
        +void unlock()
        +Condition newCondition()
        +CompletableFuture<Void> lockAsync()
        +CompletableFuture<Void> lockInterruptiblyAsync()
        +CompletableFuture<Boolean> tryLockAsync()
        +CompletableFuture<Boolean> tryLockAsync(long time, TimeUnit unit)
        +CompletableFuture<Void> unlockAsync()
        #abstract CompletableFuture<LockAcquisitionResult> tryAcquireLockScriptAsync(String requestUuid, String ownerId, long leaseTimeMs)
        #abstract CompletableFuture<Boolean> releaseLockScriptAsync(String requestUuid, String ownerId, boolean decrementReentrant)
        #CompletableFuture<Object> executeScriptAsync(String scriptName, List<String> keys, List<String> args)
    }
    AbstractRedisLock ..|> AsyncLock

    class RedisReentrantLock {
        +RedisReentrantLock(LockComponentRegistry componentRegistry, LockBucketConfig config, String lockName, ...)
        +CompletableFuture<Void> fullyUnlockAsync()
        #CompletableFuture<LockAcquisitionResult> tryAcquireLockScriptAsync(String requestUuid, String ownerId, long leaseTimeMs)
        #CompletableFuture<Boolean> releaseLockScriptAsync(String requestUuid, String ownerId, boolean decrementReentrant)
    }
    RedisReentrantLock --|> AbstractRedisLock

    class RedisStateLock {
        +String expectedState
        +RedisStateLock(LockComponentRegistry componentRegistry, LockBucketConfig config, String lockName, String expectedState, ...)
        +CompletableFuture<String> getStateAsync()
        +CompletableFuture<Boolean> updateStateAsync(String newState)
        +CompletableFuture<Boolean> unlockWithStateAsync(String newStateOnUnlock)
        #CompletableFuture<LockAcquisitionResult> tryAcquireLockScriptAsync(String requestUuid, String ownerId, long leaseTimeMs)
        #CompletableFuture<Boolean> releaseLockScriptAsync(String requestUuid, String ownerId, boolean decrementReentrant)
    }
    RedisStateLock --|> AbstractRedisLock

    class ReadWriteLock {
        <<java.util.concurrent.locks.ReadWriteLock>>
        +Lock readLock()
        +Lock writeLock()
    }

    class RedisReadWriteLock {
        +RedisReadWriteLock(LockComponentRegistry componentRegistry, LockBucketConfig config, String lockName, ...)
        +Lock readLock()
        +Lock writeLock()
        +ReadLock readLockInstance
        +WriteLock writeLockInstance
    }
    RedisReadWriteLock ..|> ReadWriteLock

    class ReadLock {
        <<Inner Class of RedisReadWriteLock>>
        +ReadLock(RedisReadWriteLock parent, ...)
        +CompletableFuture<Boolean> tryAcquireReadLockAsync(String requestUuid, String readerId, String writerId, long leaseTimeMs)
        +CompletableFuture<Boolean> releaseReadLockAsync(String requestUuid, String readerId)
    }
    ReadLock --|> AbstractRedisLock
    RedisReadWriteLock "1" *-- "1" ReadLock

    class WriteLock {
        <<Inner Class of RedisReadWriteLock>>
        +WriteLock(RedisReadWriteLock parent, ...)
        +CompletableFuture<Boolean> tryAcquireWriteLockAsync(String requestUuid, String writerId, long leaseTimeMs)
        +CompletableFuture<Boolean> releaseWriteLockAsync(String requestUuid, String writerId)
    }
    WriteLock --|> AbstractRedisLock
    RedisReadWriteLock "1" *-- "1" WriteLock

    class RedisStampedLock {
        +RedisStampedLock(LockComponentRegistry componentRegistry, LockBucketConfig config, String lockName, ...)
        +String writeLock()
        +String tryWriteLock()
        +String tryWriteLock(long time, TimeUnit unit)
        +String readLock()
        +String tryReadLock()
        +String tryReadLock(long time, TimeUnit unit)
        +String tryOptimisticRead()
        +boolean validate(String stamp)
        +String tryConvertToWriteLock(String readStamp)
        +String tryConvertToReadLock(String writeStamp)
        +void unlock(String stamp)
        +CompletableFuture<String> writeLockAsync()
        +CompletableFuture<String> tryWriteLockAsync()
        +CompletableFuture<String> tryWriteLockAsync(long time, TimeUnit unit)
        +CompletableFuture<String> readLockAsync()
        +CompletableFuture<String> tryReadLockAsync()
        +CompletableFuture<String> tryReadLockAsync(long time, TimeUnit unit)
        +CompletableFuture<String> tryOptimisticReadAsync()
        +CompletableFuture<Boolean> validateAsync(String stamp)
        +CompletableFuture<String> tryConvertToWriteLockAsync(String readStamp)
        +CompletableFuture<String> tryConvertToReadLockAsync(String writeStamp)
        +CompletableFuture<Void> unlockAsync(String stamp)
        #CompletableFuture<LockAcquisitionResult> tryAcquireLockScriptAsync(String requestUuid, String ownerId, long leaseTimeMs)
        #CompletableFuture<Boolean> releaseLockScriptAsync(String requestUuid, String ownerId, boolean decrementReentrant)
    }
    RedisStampedLock --|> AbstractRedisLock

    class LockComponentRegistry {
        +ScriptLoader scriptLoader
        +UnlockMessageListenerManager unlockMessageListenerManager
        +LockWatchdog lockWatchdog
        +RedisLockOperations redisLockOperations
        +DefaultLockOwnerSupplier lockOwnerSupplier
        +RedisLockErrorHandler errorHandler
        +ObjectProvider<LockMonitor> lockMonitorProvider
    }

    class LockBucketConfig {
        +String bucketName
        +String scope
        +Duration leaseTime
        +Duration retryInterval
        +Duration stateKeyExpiration
        +Duration pubSubWaitTimeout
        +LockOwnerSupplier lockOwnerSupplier
    }

    class LockAcquisitionResult {
        +boolean acquired
        +long remainingTtlMs
        +String errorMessage
    }

    AbstractRedisLock o-- LockComponentRegistry : uses shared components
    AbstractRedisLock o-- LockBucketConfig : uses configuration
    AbstractRedisLock --> LockAcquisitionResult : returns from script execution
```

## Lock Creation Flow

```mermaid
sequenceDiagram
    participant ApplicationCode
    participant LockBucketRegistry
    participant LockBucketBuilder
    participant LockConfigBuilder
    participant ReentrantLockConfigBuilder
    participant RedisReentrantLock

    ApplicationCode->>+LockBucketRegistry: builder("orders")
    LockBucketRegistry->>+LockBucketBuilder: new LockBucketBuilder(componentRegistry, bucketConfig)
    LockBucketBuilder-->>-LockBucketRegistry: builder instance
    LockBucketRegistry-->>-ApplicationCode: LockBucketBuilder

    ApplicationCode->>+LockBucketBuilder: withDefaultLeaseTime(Duration.ofMinutes(5))
    LockBucketBuilder-->>-ApplicationCode: updated builder

    ApplicationCode->>+LockBucketBuilder: lock()
    LockBucketBuilder->>+LockConfigBuilder: new LockConfigBuilder(componentRegistry, updatedBucketConfig)
    LockConfigBuilder-->>-LockBucketBuilder: config builder
    LockBucketBuilder-->>-ApplicationCode: LockConfigBuilder

    ApplicationCode->>+LockConfigBuilder: reentrant("order123")
    LockConfigBuilder->>+ReentrantLockConfigBuilder: new ReentrantLockConfigBuilder(componentRegistry, bucketConfig, "order123")
    ReentrantLockConfigBuilder-->>-LockConfigBuilder: lock type builder
    LockConfigBuilder-->>-ApplicationCode: ReentrantLockConfigBuilder

    ApplicationCode->>+ReentrantLockConfigBuilder: withLeaseTime(Duration.ofMinutes(10))
    ReentrantLockConfigBuilder-->>-ApplicationCode: updated builder

    ApplicationCode->>+ReentrantLockConfigBuilder: build()
    ReentrantLockConfigBuilder->>+RedisReentrantLock: new RedisReentrantLock(componentRegistry, finalConfig, "order123", ownerId, leaseTime, retryInterval)
    RedisReentrantLock-->>-ReentrantLockConfigBuilder: lock instance
    ReentrantLockConfigBuilder-->>-ApplicationCode: RedisReentrantLock
```
