# Destilink Framework Code Analyzer

A tool to enforce coding standards and best practices in the Destilink Framework.

## Features

- Scans Java and YAML files for violations of framework standards
- Detects critical issues like prohibited annotations (@ComponentScan, @SpringBootApplication)
- Identifies code style violations and best practice deviations
- Generates detailed reports with issue severity and locations
- Customizable rules via JSON configuration

## Usage

```bash
cd utils/code-analyzer
node code_analyzer.js
```

## Rules

The analyzer checks for the following issues:

### Critical Issues

- Use of `@ComponentScan` (strictly prohibited in framework modules)
- Use of `@SpringBootApplication` (forbidden in framework modules)
- Using `@Configuration` instead of `@AutoConfiguration` for module entry points

### Errors

- Field injection with `@Autowired` (use constructor injection instead)
- Field injection with `@Value` (use constructor injection instead)

### Warnings

- Missing conditional annotations on `@Bean` methods
- Missing JavaDoc for public classes and interfaces
- Missing JavaDoc for `@Bean` methods
- Non-standard property prefixes in YAML files

## Customizing Rules

Edit the `rules.json` file to customize patterns or add new rules:

```json
{
  "java": {
    "patterns": [
      {
        "name": "patternName",
        "regex": "regex pattern",
        "severity": "critical|error|warning",
        "message": "Message to display when pattern is found"
      }
    ]
  }
}
```

## Integration with CI/CD

The analyzer exits with code 1 if any critical issues are found, making it suitable for CI/CD pipelines:

```yaml
code-quality:
  script:
    - cd utils/code-analyzer
    - node code_analyzer.js
  allow_failure: false
```