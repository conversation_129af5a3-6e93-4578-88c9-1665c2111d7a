# Keycloak Test-Support

The Keycloak-Test-Support module allows testing with a local keycloak instance by defining clients and users
via annotations. A separate realm is created for each test-class using the test-class-id which is unique per test-class
and guaranties cleanup of realms after a failed test. The realm is created before testing and is destroyed afterward.
If the realm already exists on creation the old one is deleted first.

```xml
<dependency>
    <groupId>com.tui.destilink.framework.test-support</groupId>
    <artifactId>keycloak-test-support</artifactId>
</parent>
```

## Keycloak Connection

By default, the local keycloak instance is expected under `localhost:8181` with the username `admin`and
password `admin`.
The connection can be customized with the following environment variables.

```bash
KEYCLOAK_HOST=localhost
KEYCLOAK_PORT=8181
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
```

The gitlab pipeline sets the correct environment variables for the pipeline-local keycloak instance.

## Properties

The keycloak service automatically injects properties for issuer-uri, realm, users and tech-clients.
They may be referenced by other properties.

```properties
# Issuer
test-support.keycloak.realm
test-support.keycloak.issuer-uri
# Users
test-support.keycloak.user.<username>.username
test-support.keycloak.user.<username>.password
# Tech-Clients
test-support.keycloak.tech-client.<tech-client-id>.client-id
test-support.keycloak.tech-client.<tech-client-id>.client-secret
```