# Configuration and Property Hierarchy

```mermaid
classDiagram
    class RedisCoreProperties {
        +KeyspacePrefixes keyspacePrefixes
        +String getPrefix(String keyspace)
    }
    note for RedisCoreProperties "Integrates with com.tui.destilink.framework.redis.core.config.RedisCoreProperties for shared Redis config, including primary key prefixing."
    class RedisLockAutoConfiguration {
        +RedisLockProperties properties
    }
    note for RedisLockAutoConfiguration "@EnableConfigurationProperties(RedisLockProperties.class)"

    class RedisLockProperties {
        +boolean enabled
        +Duration leaseTime
        +Duration retryInterval
        +Duration stateKeyExpiration
        +String lockOwnerIdValidationRegex
        +int maxLockNameLength
        +int maxBucketNameLength
        +int maxScopeLength
        +WatchdogProperties watchdog
        +PubSubProperties pubSub
        +LuaScriptProperties luaScripts
        +LettuceProperties lettuce
    }
    RedisLockProperties ..> RedisCoreProperties : integrates
    RedisLockAutoConfiguration --|> RedisLockProperties : uses

    class WatchdogProperties {
        +boolean enabled
        +Duration scheduleFixedDelay
        +Duration minLeaseTimeForActivation
        +Duration maxTtlForRenewalCheck
    }
    RedisLockProperties o-- WatchdogProperties : has

    class PubSubProperties {
        +boolean enabled
        +Duration waitTimeout
        +String unlockChannelPattern
        +int messageListenerConcurrency
    }
    RedisLockProperties o-- PubSubProperties : has

    class LuaScriptProperties {
        +int cacheSize
        +Duration cacheExpireAfterAccess
    }
    RedisLockProperties o-- LuaScriptProperties : has

    class LettuceProperties {
        +String clientName
        +Duration commandTimeout
        +Duration shutdownTimeout
        +ClusterProperties cluster
        +PoolProperties pool
    }
    RedisLockProperties o-- LettuceProperties : has

    class ClusterProperties {
        +boolean enabled
        +int maxRedirects
        +Duration refreshPeriod
        +boolean validateClusterNodeMembership
    }
    LettuceProperties o-- ClusterProperties : has

    class PoolProperties {
        +int maxActive
        +int maxIdle
        +int minIdle
        +Duration maxWait
    }
    LettuceProperties o-- PoolProperties : has

    class Duration
    class String
    class boolean
    class int
```
