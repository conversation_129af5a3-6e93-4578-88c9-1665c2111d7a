/**
 * Destilink Framework Module Visualizer
 * Generates dependency graphs for framework modules
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  rootDir: path.resolve(__dirname, '../..'),
  outputDir: path.resolve(__dirname, 'output'),
  modulesDir: path.resolve(__dirname, '../../framework-modules')
};

// Find all module directories with pom.xml
function findModules(dir) {
  const modules = [];
  
  function scan(currentDir) {
    if (fs.existsSync(path.join(currentDir, 'pom.xml'))) {
      const name = path.basename(currentDir);
      const module = {
        name,
        path: currentDir,
        pomPath: path.join(currentDir, 'pom.xml'),
        dependencies: [],
        artifactId: name
      };
      
      modules.push(module);
    }
    
    try {
      // Scan subdirectories
      const items = fs.readdirSync(currentDir);
      for (const item of items) {
        const itemPath = path.join(currentDir, item);
        if (fs.statSync(itemPath).isDirectory() && 
            !['target', 'node_modules', '.git'].includes(item)) {
          scan(itemPath);
        }
      }
    } catch (err) {
      console.error(`Error scanning ${currentDir}: ${err.message}`);
    }
  }
  
  scan(dir);
  return modules;
}

// Extract dependencies from pom.xml using simple regex
function extractDependencies(modules) {
  for (const module of modules) {
    try {
      const pomContent = fs.readFileSync(module.pomPath, 'utf8');
      
      // Extract artifactId
      const artifactIdMatch = pomContent.match(/<artifactId>([^<]+)<\/artifactId>/);
      if (artifactIdMatch && artifactIdMatch[1]) {
        module.artifactId = artifactIdMatch[1];
      }
      
      // Extract dependencies
      const depRegex = /<dependency>\s*<groupId>com\.tui\.destilink\.framework<\/groupId>\s*<artifactId>([^<]+)<\/artifactId>/g;
      let match;
      while ((match = depRegex.exec(pomContent)) !== null) {
        module.dependencies.push({
          artifactId: match[1]
        });
      }
    } catch (error) {
      console.error(`Error processing ${module.name}: ${error.message}`);
    }
  }
}

// Generate Mermaid diagram
function generateMermaidDiagram(modules) {
  let mermaid = 'graph TD\n';
  const moduleMap = new Map();
  
  // Create nodes
  modules.forEach(module => {
    const id = module.artifactId.replace(/-/g, '_');
    moduleMap.set(module.artifactId, id);
    mermaid += `  ${id}["${module.artifactId}"]\n`;
  });
  
  // Create edges
  modules.forEach(module => {
    const sourceId = moduleMap.get(module.artifactId);
    module.dependencies.forEach(dep => {
      const targetId = moduleMap.get(dep.artifactId);
      if (targetId) {
        mermaid += `  ${targetId} --> ${sourceId}\n`;
      }
    });
  });
  
  return mermaid;
}

// Generate HTML visualization
function generateHtml(modules, mermaidDiagram) {
  return `<!DOCTYPE html>
<html>
<head>
  <title>Destilink Framework Module Visualization</title>
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
  <style>
    body { font-family: Arial; margin: 20px; }
    .container { max-width: 1200px; margin: 0 auto; }
    .mermaid { margin: 20px 0; overflow: auto; }
    .module-item { margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; }
    .module-name { font-weight: bold; color: #0066cc; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Destilink Framework Module Visualization</h1>
    
    <div class="mermaid">
${mermaidDiagram}
    </div>
    
    <div class="module-list">
      <h2>Module Details</h2>
      ${modules.map(module => `
        <div class="module-item">
          <div class="module-name">${module.artifactId}</div>
          <div>Path: ${module.path.replace(config.rootDir, '')}</div>
          ${module.dependencies.length > 0 ? `
            <div>Dependencies:
              <ul>
                ${module.dependencies.map(dep => `<li>${dep.artifactId}</li>`).join('')}
              </ul>
            </div>
          ` : '<div>No internal dependencies</div>'}
        </div>
      `).join('')}
    </div>
  </div>
  
  <script>
    mermaid.initialize({ startOnLoad: true });
  </script>
</body>
</html>`;
}

// Main function
function main() {
  console.log('Scanning for modules...');
  const modules = findModules(config.modulesDir);
  console.log(`Found ${modules.length} modules`);
  
  console.log('Extracting dependencies...');
  extractDependencies(modules);
  
  console.log('Generating visualizations...');
  
  // Mermaid diagram
  const mermaidDiagram = generateMermaidDiagram(modules);
  fs.writeFileSync(path.join(config.outputDir, 'module-dependencies.mmd'), mermaidDiagram);
  
  // JSON output
  fs.writeFileSync(
    path.join(config.outputDir, 'module-dependencies.json'), 
    JSON.stringify(modules, null, 2)
  );
  
  // HTML visualization
  const html = generateHtml(modules, mermaidDiagram);
  fs.writeFileSync(path.join(config.outputDir, 'module-visualization.html'), html);
  
  console.log('Done! Output files generated in ' + config.outputDir);
}

// Run the program
main();