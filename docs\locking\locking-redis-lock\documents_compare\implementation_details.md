# Modernized Redis Locking Module: Implementation Details

This document provides detailed implementation specifics for key components and mechanisms within the modernized Redis locking module. Understanding these details is crucial for contributing to the module, debugging issues, and effectively using its features. **The implementation details described here are mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md` and implemented in the module.**

## Core Components

The module is structured around several core components that collaborate to provide the distributed locking functionality:

*   **`AbstractRedisLock`**: The base class for all lock types. It provides common functionality such as the constructor (injecting `LockComponentRegistry`), the main `lock()`, `tryLock()`, and `unlock()` methods, and the `executeScriptAsync()` helper. It defines abstract methods like `tryAcquireLock()` and `releaseLock()` that are implemented by specific lock types. It handles the interaction with the `LockSemaphoreHolder` for waiting threads and the `LockWatchdog` for lease extension. **Note that the `lock()` method in this class blocks indefinitely until the lock is acquired, adhering to the `java.util.concurrent.Lock` interface specification. The `defaultTimeout` property is not used in the final implementation of the `lock()` method.**
*   **`RedisReentrantLock`**: Implements the reentrant lock logic, extending `AbstractRedisLock`. It overrides `tryAcquireLock()` and `releaseLock()` to manage the reentrancy count. **Crucially, the reentrancy count and lock owner information for reentrant locks are stored in a Redis Hash associated with the lock key, not in a `ThreadLocal` variable.** This ensures correctness in distributed environments where threads might execute on different JVM instances. It includes the `fullyUnlock()` method to release the lock regardless of the reentrancy count.
*   **`RedisStateLock`**: Implements the state lock logic, extending `AbstractRedisLock`. It overrides `tryAcquireLock()` and `releaseLock()` and adds state-specific methods like `getState()`, `getOrInitializeState()`, and `updateState()`. It uses Lua scripts for atomic state operations and handles the response cache for idempotency.
*   **`RedisReadWriteLock`**: Implements the read-write lock logic, extending `AbstractRedisLock`. It contains inner `ReadLock` and `WriteLock` classes that provide the `Lock` interface implementations for read and write operations, respectively. It manages the read lock set and the write lock key in Redis.
*   **`LockComponentRegistry`**: A central registry holding shared components used by all lock instances, such as the `StringRedisTemplate`, `ScriptLoader`, `LockOwnerSupplier`, `RedisLockErrorHandler`, `LockWatchdog`, `UnlockMessageListenerManager`, and the `waitingNotifications` map. This promotes dependency injection and avoids passing numerous dependencies to each lock instance.
*   **`LockSemaphoreHolder`**: Manages `java.util.concurrent.Semaphore` instances for threads waiting on specific lock keys. It is used by `AbstractRedisLock` to block and unblock threads based on unlock notifications. It tracks waiting threads and last access time for cleanup.
*   **`UnlockMessageListener`**: A component responsible for subscribing to Redis Pub/Sub channels and receiving unlock notifications. It uses an optimal executor for processing messages and signals the appropriate `LockSemaphoreHolder` when a notification arrives.
*   **`UnlockMessageListenerManager`**: Manages the lifecycle of `UnlockMessageListener` instances, typically creating one listener per lock bucket to handle notifications for all locks within that bucket. It uses `ObjectProvider` for dependency injection and includes `@PreDestroy` for proper shutdown.
*   **`RedisLockErrorHandler`**: Handles the translation of low-level Redis exceptions into the module's specific exception hierarchy and integrates with structured logging via `ExceptionMarkerProvider`.
*   **`LockWatchdog**: A background mechanism responsible for automatically extending the lease time of acquired locks before they expire. This prevents locks from being prematurely released if the client holding the lock is still active but experiences a delay. The watchdog is registered by `AbstractRedisLock` when a lock is successfully acquired.
*   **`LockBucket`**: Represents a configured lock bucket and is responsible for constructing Redis keys based on the bucket's configuration and the provided lock key. It encapsulates the Redis key schema logic.
*   **`RedisLockProperties`**: A `@ConfigurationProperties` class holding the module's configuration properties, such as the Redis key prefix, default timeouts, and bucket configurations. **This class is the primary source for configuring the Redis locking module.**
*   **`LockBucketConfig`**: Represents the resolved configuration for a specific lock bucket, including its name, default lease time, and other bucket-specific settings.
*   **`LockBucketRegistry`**: A registry that holds all configured `LockBucket` instances, allowing lock builders to retrieve the configuration for a given bucket name.
*   **`LockConfigBuilder` and `AbstractLockTypeConfigBuilder` subclasses**: Builder classes used to configure and create instances of different lock types (`RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`) within specific lock buckets.

## Key Mechanisms

### Lock Acquisition and Waiting

As detailed in the [Lock Acquisition Mechanism](lock_acquisition.md) document, the module uses a notification-based approach with `LockSemaphoreHolder` and Redis Pub/Sub, falling back to re-polling after TTL expiration.

### Lock Release

Lock release is performed atomically using Lua scripts. When a lock is successfully released, an unlock message is published to a Redis Pub/Sub channel to notify waiting threads.

### Lock Watchdog

The `LockWatchdog` is a background task that periodically extends the lease of acquired locks. When a lock is acquired, it is registered with the watchdog. The watchdog then schedules a task to extend the lock's TTL before it expires. This process repeats until the lock is released.

### Exception Handling

As detailed in the [Exception Handling](exception_handling.md) document, the `RedisLockErrorHandler` translates Redis exceptions into the module's exception hierarchy.

### Messaging

As detailed in the [Messaging](messaging.md) document, Redis Pub/Sub is used for unlock notifications. The `UnlockMessageListener` and `UnlockMessageListenerManager` handle the subscription and processing of these messages.

### Idempotency (State Lock)

The `RedisStateLock` uses a response cache in Redis, keyed by a request ID, to ensure that state update operations are idempotent. This prevents unintended side effects if a state update request is retried.

### Metrics

The module exposes metrics using Micrometer to provide visibility into lock usage, contention, and performance. Gauges are used to track the number of currently held locks and the number of threads waiting for locks.

### Configuration

Configuration is managed via `RedisLockProperties` and `LockBucketConfig`. Lock buckets allow for different configurations (e.g., default lease time, retry intervals) for different groups of locks.

Understanding these implementation details is crucial for effective use and maintenance of the modernized Redis locking module.