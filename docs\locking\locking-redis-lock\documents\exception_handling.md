# Redis Locking Module: Exception Handling

## 1. Introduction

Effective error handling is crucial for a robust distributed locking mechanism. The `locking-redis-lock` module employs a structured exception hierarchy to provide clear, contextual information when errors occur during lock operations. This document outlines this hierarchy and the purpose of each specialized exception, emphasizing integration with the core Destilink Framework logging capabilities for structured JSON logging via the `ExceptionMarkerProvider` interface.

The design is guided by the proposals in `redis-lock-improvements.md` and aims to align with the existing exception structure while enhancing context and consistency.

## 2. Base Exception: `AbstractRedisLockException`

All custom exceptions thrown by the Redis locking module **must** extend a common base class, `AbstractRedisLockException`. This class serves to:

*   Provide a common type for catching all lock-related errors.
*   Carry common contextual information relevant to any lock operation failure.
*   Integrate with the Destilink Framework's structured logging by implementing the [`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/ExceptionMarkerProvider.java:1) interface.
*   It is recommended that `AbstractRedisLockException` extends [`com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/MarkerNestedRuntimeException.java:1) (or `MarkerNestedException` if a checked base is preferred, though runtime is more common for such issues) to inherit base marker provider capabilities if available, or implement `ExceptionMarkerProvider` directly.

**Required Contextual Fields (Enforced by Constructors):**

*   `lockName` (String): The full Redis key of the lock involved.
*   `lockType` (String): The specific type of lock implementation (e.g., "RedisReentrantLock", "RedisReadWriteLock.ReadLock").
*   `lockOwnerId` (String, nullable): The ID of the owner attempting the operation.
*   `requestUuid` (String, nullable): A unique ID for the specific lock operation attempt, useful for tracing.
*   `message` (String): A descriptive error message.
*   `cause` (Throwable, nullable): The underlying exception.

**Constructors:**
Constructors for `AbstractRedisLockException` and all its subclasses **must** enforce the provision of these required contextual information fields. Fields that are always present (like `lockName`, `lockType`, `message`) should be mandatory constructor parameters. Optional fields (`lockOwnerId`, `requestUuid`, `cause`) can be nullable or have dedicated constructors.

**`ExceptionMarkerProvider` Implementation:**

The `AbstractRedisLockException` must implement the `getMarker()` method:

```java
// In AbstractRedisLockException.java (or its base like MarkerNestedRuntimeException)
@Override
public Marker getMarker() {
    Map<String, Object> contextMap = new HashMap<>();
    contextMap.put("lock.name", this.lockName); // Standardized key naming
    contextMap.put("lock.type", this.lockType);

    if (this.lockOwnerId != null) {
        contextMap.put("lock.ownerId", this.lockOwnerId);
    }
    if (this.requestUuid != null) {
        contextMap.put("lock.requestUuid", this.requestUuid);
    }
    // Allow subclasses to add their specific context
    populateSpecificMarkers(contextMap);
    
    // Ensure no null values are put into the map for Markers.appendEntries
    contextMap.values().removeIf(Objects::isNull);

    return Markers.appendEntries(contextMap);
}

/**
 * To be overridden by subclasses to add their specific contextual fields to the marker map.
 * @param contextMap The map to populate with additional markers.
 */
protected void populateSpecificMarkers(Map<String, Object> contextMap) {
    // Base implementation does nothing.
}
```
The [`ExceptionLogstashMarkersJsonProvider`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/ExceptionLogstashMarkersJsonProvider.java:1) in the core logging framework will automatically include data from this `Marker` in the structured JSON log output.

## 3. Specialized Exception Classes

The following specialized exceptions extend `AbstractRedisLockException`, provide more specific failure details, and contribute their unique context to the marker via an overridden `populateSpecificMarkers` method. Their constructors must call `super(...)` with all required base context and then initialize their own specific fields.

*   **`LockAcquisitionException`**:
    *   **Purpose**: Thrown when an attempt to acquire a lock fails for reasons other than a simple timeout or interruption.
    *   **Specific Context for Marker**: `redis.error` (String, if applicable, e.g., error message from Lua script).
    *   *Existing.*

*   **`LockTimeoutException`**:
    *   **Purpose**: Thrown when a blocking lock acquisition attempt fails to acquire the lock within the specified timeout period.
    *   **Specific Context for Marker**: `lock.timeoutMillis` (long), `lock.attempts` (int).
    *   *Existing.*

*   **`LockReleaseException`**:
    *   **Purpose**: Thrown when an attempt to release a lock fails.
    *   **Specific Context for Marker**: `lock.attemptedOwnerId` (String), `lock.actualOwnerId` (String, if known and different, indicating an ownership mismatch).
    *   *Existing.*

*   **`LeaseExtensionException`**:
    *   **Purpose**: Thrown when the `LockWatchdog` or reentrant acquisition fails to extend an active lock's lease.
    *   **Specific Context for Marker**: `lock.leaseTimeMillis` (long, the lease being extended to), `lock.extensionResult` (String/Object, from Redis).
    *   *To be made a top-level class.*

*   **`LockInterruptedException`**:
    *   **Purpose**: Wraps an `InterruptedException` if a thread is interrupted while waiting to acquire a lock.
    *   **Specific Context for Marker**: No additional fields beyond base; `InterruptedException` is the `cause`.
    *   *Proposed.*

*   **`LockCommandException`**:
    *   **Purpose**: For Lua script or Redis command execution failures (not connection issues).
    *   **Specific Context for Marker**: `redis.scriptName` (String) or `redis.commandName` (String), `redis.arguments` (String).
    *   *Proposed.*

*   **`LockConnectionException`**:
    *   **Purpose**: For failures due to underlying Redis connection problems.
    *   **Specific Context for Marker**: `redis.connection.error` (String, details of connection failure).
    *   *Proposed.*

*   **`StateLock` Specific Exceptions** (e.g., `StateMismatchException`, `StateNotFoundException`, `StateUpdateException`):
    *   **Purpose**: For errors specific to `RedisStateLock`.
    *   **Specific Context for Marker**: `lock.state.key` (String), `lock.state.expected` (String, if applicable), `lock.state.actual` (String, if applicable).
    *   *Existing; ensure they extend the new base and implement marker provision.*

*   **`LockConfigurationException`**:
    *   **Purpose**: Thrown when there is an issue with lock configuration.
    *   **Specific Context for Marker**: `lock.config.parameter` (String), `lock.config.value` (String, if applicable).

*   **`LockConversionException`**:
    *   **Purpose**: Thrown when there is an issue converting between lock types.
    *   **Specific Context for Marker**: `lock.conversion.source` (String), `lock.conversion.target` (String).

*   **`LockDeserializationException`**:
    *   **Purpose**: Thrown when there is an issue deserializing lock data from Redis.
    *   **Specific Context for Marker**: `lock.data.format` (String), `lock.data.error` (String).

*   **`LockInternalException`**:
    *   **Purpose**: Thrown for internal errors in the locking module.
    *   **Specific Context for Marker**: `lock.component` (String), `lock.operation` (String).

*   **`LockNotFoundException`**:
    *   **Purpose**: Thrown when a lock is not found when expected.
    *   **Specific Context for Marker**: No additional fields beyond base.

*   **`LockNotOwnedException`**:
    *   **Purpose**: Thrown when an operation is attempted on a lock not owned by the current owner.
    *   **Specific Context for Marker**: `lock.attempted.owner` (String), `lock.actual.owner` (String, if known).

*   **`LockSerializationException`**:
    *   **Purpose**: Thrown when there is an issue serializing lock data for Redis.
    *   **Specific Context for Marker**: `lock.data.format` (String), `lock.data.error` (String).

*   **`StampValidationException`**:
    *   **Purpose**: Thrown when stamp validation fails in a `RedisStampedLock`.
    *   **Specific Context for Marker**: `lock.stamp.expected` (String), `lock.stamp.actual` (String, if known).

## 4. Error Handling Strategy

*   **`RedisLockErrorHandler`**: This bean remains central for catching low-level Redis exceptions and translating them into the appropriate, context-rich `AbstractRedisLockException` subtype, ensuring all required context for marker generation is populated.
*   **Lua Script Error Reporting**: Scripts should return clear error indicators to enable mapping to specific, well-contextualized exceptions.

## 5. Logging Integration

By implementing `ExceptionMarkerProvider` in `AbstractRedisLockException`, all contextual data from lock exceptions will be automatically included as structured fields in the JSON log output by the Destilink Core logging framework.

For general logging within the module:
*   Use SLF4J with parameterized messages: `log.debug("Processing lock {} for owner {}", lockName, ownerId);`
*   For operations spanning multiple log statements, use [`LockContextDecorator`](../src/main/java/com/tui/destilink/framework/locking/redis/logging/context/LockContextDecorator.java:1) (ensuring it aligns with [`AbstractContextDecorator`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/context/decorator/AbstractContextDecorator.java:1)) to add persistent contextual information (e.g., `lock.name`, `lock.operation`, `lock.ownerId`, `lock.requestId`) to the MDC via a try-with-resources `Scope`.
    ```java
    try (Scope scope = lockContextDecorator.withContext(lockName, "acquire", ownerId, requestId)) {
        log.info("Attempting to acquire lock");
        // Actual lock acquisition/release logic here
        log.info("Lock acquisition successful");
    }