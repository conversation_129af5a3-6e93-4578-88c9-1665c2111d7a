# Redis Locking Module: Metrics and Monitoring

This diagram illustrates the Metrics and Monitoring infrastructure in the `locking-redis-lock` module.

```mermaid
classDiagram
    %% Core Classes
    class LockMonitor {
        -MeterRegistry meterRegistry
        +recordLockAcquisitionAttempt(lockKey: String): void
        +recordLockAcquisitionSuccess(lockKey: String, durationMs: long): void
        +recordLockAcquisitionFailure(lockKey: String, reason: String): void
        +recordLockAcquisitionTimeout(lockKey: String): void
        +recordLockRelease(lockKey: String, heldTimeMs: long): void
        +recordLeaseExtensionAttempt(lockKey: String): void
        +recordLeaseExtensionSuccess(lockKey: String): void
        +recordLeaseExtensionFailure(lockKey: String): void
        +recordLeaseExtensionTimeout(lockKey: String): void
        +recordLockConversionAttempt(lockKey: String, fromType: String, toType: String): void
        +recordLockConversionSuccess(lockKey: String, fromType: String, toType: String, durationMs: long): void
        +recordLockConversionFailure(lockKey: String, fromType: String, toType: String, reason: String): void
        -createTag(lockKey: String): Tag
        -createTags(lockKey: String, additionalTags: Tag...): Iterable~Tag~
    }
    
    class MeterRegistry {
        <<Micrometer>>
        +counter(name: String, tags: Iterable~Tag~): Counter
        +timer(name: String, tags: Iterable~Tag~): Timer
        +gauge(name: String, obj: Object, valueFunction: ToDoubleFunction): Gauge
    }
    
    class Counter {
        <<Micrometer>>
        +increment(): void
        +increment(amount: double): void
    }
    
    class Timer {
        <<Micrometer>>
        +record(duration: long, unit: TimeUnit): void
        +record(callable: Callable): T
        +recordCallable(callable: Callable): T
    }
    
    class Gauge {
        <<Micrometer>>
        +value(): double
    }
    
    class Tag {
        <<Micrometer>>
        +of(key: String, value: String): Tag
    }
    
    class RedisLockHealthIndicator {
        -RedisConnectionFactory connectionFactory
        -RedisLockProperties properties
        -LockMonitor lockMonitor
        +health(): Health
        -checkRedisConnection(): Health
        -checkLockOperations(): Health
    }
    
    class Health {
        <<Spring>>
        +status: Status
        +details: Map~String, Object~
        +up(): Health.Builder
        +down(): Health.Builder
        +unknown(): Health.Builder
        +outOfService(): Health.Builder
    }
    
    class HealthIndicator {
        <<Spring>>
        +health(): Health
    }
    
    class RedisLockMetricsAutoConfiguration {
        +lockMonitor(meterRegistry: MeterRegistry): LockMonitor
        +redisLockHealthIndicator(connectionFactory: RedisConnectionFactory, properties: RedisLockProperties, lockMonitor: LockMonitor): RedisLockHealthIndicator
    }
    
    %% Lock Implementations
    class AbstractRedisLock {
        #lockMonitor: LockMonitor
        #recordLockAcquisitionAttempt(lockKey: String): void
        #recordLockAcquisitionSuccess(lockKey: String, startTime: long): void
        #recordLockAcquisitionFailure(lockKey: String, reason: String): void
        #recordLockAcquisitionTimeout(lockKey: String): void
        #recordLockRelease(lockKey: String, acquisitionTime: long): void
    }
    
    class RedisReentrantLock {
        -lockKey: String
        -acquisitionTime: long
        +lock(): void
        +unlock(): void
    }
    
    class RedisReadWriteLock {
        -lockKey: String
    }
    
    class RedisStampedLock {
        -lockKey: String
    }
    
    %% Supporting Services
    class LockWatchdog {
        -lockMonitor: LockMonitor
        -extendLeases(): void
    }
    
    class RedisLockOperations {
        -lockMonitor: LockMonitor
        +tryLock(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
    }
    
    class RedisLockErrorHandler {
        -lockMonitor: LockMonitor
        +handleLockAcquisitionError(lockKey: String, ex: Throwable): void
        +handleUnlockError(lockKey: String, ex: Throwable): void
        +handleLeaseExtensionError(lockKey: String, ex: Throwable): void
    }
    
    %% Monitoring Systems
    class PrometheusRegistry {
        <<External>>
        +scrape(): String
    }
    
    class DatadogRegistry {
        <<External>>
        +publish(): void
    }
    
    class MicrometerRegistry {
        <<External>>
        +publish(): void
    }
    
    class ActuatorEndpoint {
        <<Spring>>
        +health(): HealthEndpoint
        +metrics(): MetricsEndpoint
        +prometheus(): PrometheusEndpoint
    }
    
    %% Relationships
    LockMonitor --> MeterRegistry : uses
    LockMonitor --> Counter : creates and updates
    LockMonitor --> Timer : creates and updates
    LockMonitor --> Gauge : creates and updates
    LockMonitor --> Tag : creates
    
    RedisLockHealthIndicator --|> HealthIndicator : implements
    RedisLockHealthIndicator --> Health : creates
    
    RedisLockMetricsAutoConfiguration --> LockMonitor : creates
    RedisLockMetricsAutoConfiguration --> RedisLockHealthIndicator : creates
    
    AbstractRedisLock --> LockMonitor : uses
    RedisReentrantLock --|> AbstractRedisLock : extends
    RedisReadWriteLock --|> AbstractRedisLock : extends
    RedisStampedLock --|> AbstractRedisLock : extends
    
    LockWatchdog --> LockMonitor : uses
    RedisLockOperations --> LockMonitor : uses
    RedisLockErrorHandler --> LockMonitor : uses
    
    MeterRegistry <|-- PrometheusRegistry : implements
    MeterRegistry <|-- DatadogRegistry : implements
    MeterRegistry <|-- MicrometerRegistry : implements
    
    ActuatorEndpoint --> RedisLockHealthIndicator : exposes
    ActuatorEndpoint --> MeterRegistry : exposes
    
    %% Notes
    note for LockMonitor "Central component for\nmetrics collection"
    note for RedisLockHealthIndicator "Provides health status\nfor Redis locks"
    note for MeterRegistry "Micrometer abstraction for\nvarious monitoring systems"
```

## Metrics and Monitoring Overview

The Redis Locking module includes a comprehensive metrics and monitoring infrastructure that provides visibility into lock operations, performance, and health. This infrastructure is built on Spring Boot Actuator and Micrometer, allowing for integration with various monitoring systems such as Prometheus, Datadog, and others.

### Key Responsibilities

1. **Metrics Collection**: Collect metrics on lock acquisition, release, and lease extension
2. **Performance Monitoring**: Track timing information for lock operations
3. **Error Tracking**: Monitor failures and their causes
4. **Health Checks**: Provide health status for the Redis lock system
5. **Operational Visibility**: Enable operational insights through dashboards and alerts

## Core Components

### LockMonitor

The `LockMonitor` class is the central component responsible for collecting and reporting metrics:

```java
@Slf4j
public class LockMonitor {
    private final MeterRegistry meterRegistry;
    
    public LockMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    // Lock Acquisition Metrics
    
    public void recordLockAcquisitionAttempt(String lockKey) {
        meterRegistry.counter("redis.lock.acquisition.attempt", createTag(lockKey))
            .increment();
    }
    
    public void recordLockAcquisitionSuccess(String lockKey, long durationMs) {
        meterRegistry.counter("redis.lock.acquisition.success", createTag(lockKey))
            .increment();
        
        meterRegistry.timer("redis.lock.acquisition.time", createTag(lockKey))
            .record(durationMs, TimeUnit.MILLISECONDS);
    }
    
    public void recordLockAcquisitionFailure(String lockKey, String reason) {
        meterRegistry.counter("redis.lock.acquisition.failure", 
                createTags(lockKey, Tag.of("reason", reason)))
            .increment();
    }
    
    public void recordLockAcquisitionTimeout(String lockKey) {
        meterRegistry.counter("redis.lock.acquisition.timeout", createTag(lockKey))
            .increment();
    }
    
    // Lock Release Metrics
    
    public void recordLockRelease(String lockKey, long heldTimeMs) {
        meterRegistry.counter("redis.lock.release", createTag(lockKey))
            .increment();
        
        meterRegistry.timer("redis.lock.held.time", createTag(lockKey))
            .record(heldTimeMs, TimeUnit.MILLISECONDS);
    }
    
    // Lease Extension Metrics
    
    public void recordLeaseExtensionAttempt(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.attempt", createTag(lockKey))
            .increment();
    }
    
    public void recordLeaseExtensionSuccess(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.success", createTag(lockKey))
            .increment();
    }
    
    public void recordLeaseExtensionFailure(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.failure", createTag(lockKey))
            .increment();
    }
    
    public void recordLeaseExtensionTimeout(String lockKey) {
        meterRegistry.counter("redis.lock.lease.extension.timeout", createTag(lockKey))
            .increment();
    }
    
    // Lock Conversion Metrics
    
    public void recordLockConversionAttempt(String lockKey, String fromType, String toType) {
        meterRegistry.counter("redis.lock.conversion.attempt", 
                createTags(lockKey, 
                    Tag.of("from", fromType), 
                    Tag.of("to", toType)))
            .increment();
    }
    
    public void recordLockConversionSuccess(String lockKey, String fromType, String toType, long durationMs) {
        meterRegistry.counter("redis.lock.conversion.success", 
                createTags(lockKey, 
                    Tag.of("from", fromType), 
                    Tag.of("to", toType)))
            .increment();
        
        meterRegistry.timer("redis.lock.conversion.time", 
                createTags(lockKey, 
                    Tag.of("from", fromType), 
                    Tag.of("to", toType)))
            .record(durationMs, TimeUnit.MILLISECONDS);
    }
    
    public void recordLockConversionFailure(String lockKey, String fromType, String toType, String reason) {
        meterRegistry.counter("redis.lock.conversion.failure", 
                createTags(lockKey, 
                    Tag.of("from", fromType), 
                    Tag.of("to", toType), 
                    Tag.of("reason", reason)))
            .increment();
    }
    
    // Helper Methods
    
    private Tag createTag(String lockKey) {
        return Tag.of("lockKey", sanitizeLockKey(lockKey));
    }
    
    private Iterable<Tag> createTags(String lockKey, Tag... additionalTags) {
        List<Tag> tags = new ArrayList<>();
        tags.add(createTag(lockKey));
        tags.addAll(Arrays.asList(additionalTags));
        return tags;
    }
    
    private String sanitizeLockKey(String lockKey) {
        // Sanitize lock key for use as a tag value
        // Remove special characters, limit length, etc.
        if (lockKey == null) {
            return "unknown";
        }
        
        // Extract bucket from lock key for better grouping
        String[] parts = lockKey.split(":");
        if (parts.length >= 2) {
            return parts[1]; // Return bucket name
        }
        
        return lockKey.replaceAll("[^a-zA-Z0-9_.-]", "_")
            .substring(0, Math.min(lockKey.length(), 32));
    }
}
```

### RedisLockHealthIndicator

The `RedisLockHealthIndicator` class provides health status information for the Redis lock system:

```java
@Slf4j
public class RedisLockHealthIndicator implements HealthIndicator {
    private final RedisConnectionFactory connectionFactory;
    private final RedisLockProperties properties;
    private final LockMonitor lockMonitor;
    
    public RedisLockHealthIndicator(RedisConnectionFactory connectionFactory,
                                   RedisLockProperties properties,
                                   LockMonitor lockMonitor) {
        this.connectionFactory = connectionFactory;
        this.properties = properties;
        this.lockMonitor = lockMonitor;
    }
    
    @Override
    public Health health() {
        try {
            // Check Redis connection
            Health redisHealth = checkRedisConnection();
            if (redisHealth.getStatus() != Status.UP) {
                return redisHealth;
            }
            
            // Check lock operations
            return checkLockOperations();
        } catch (Exception e) {
            log.error("Error checking Redis lock health", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
    
    private Health checkRedisConnection() {
        try {
            RedisConnection connection = connectionFactory.getConnection();
            try {
                String pong = connection.ping();
                if (!"PONG".equals(pong)) {
                    return Health.down()
                        .withDetail("ping", "Failed to get PONG response")
                        .build();
                }
            } finally {
                connection.close();
            }
            
            return Health.up()
                .withDetail("ping", "PONG")
                .build();
        } catch (Exception e) {
            log.error("Redis connection check failed", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
    
    private Health checkLockOperations() {
        // Perform a test lock operation
        String testLockKey = "health:check:" + UUID.randomUUID().toString();
        String ownerId = UUID.randomUUID().toString();
        
        try {
            // Try to acquire a lock
            RedisTemplate<String, String> redisTemplate = new StringRedisTemplate(connectionFactory);
            Boolean acquired = redisTemplate.opsForValue().setIfAbsent(
                testLockKey, ownerId, Duration.ofSeconds(5));
            
            if (Boolean.TRUE.equals(acquired)) {
                // Release the lock
                redisTemplate.delete(testLockKey);
                
                return Health.up()
                    .withDetail("testLock", "Successfully acquired and released")
                    .build();
            } else {
                return Health.down()
                    .withDetail("testLock", "Failed to acquire")
                    .build();
            }
        } catch (Exception e) {
            log.error("Lock operations check failed", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

### RedisLockMetricsAutoConfiguration

The `RedisLockMetricsAutoConfiguration` class configures the metrics and monitoring components:

```java
@AutoConfiguration
@ConditionalOnClass(MeterRegistry.class)
@ConditionalOnProperty(
    prefix = "destilink.fw.locking.redis",
    name = "metrics-enabled",
    havingValue = "true",
    matchIfMissing = true)
public class RedisLockMetricsAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public LockMonitor lockMonitor(MeterRegistry meterRegistry) {
        return new LockMonitor(meterRegistry);
    }
    
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(HealthIndicator.class)
    @ConditionalOnProperty(
        prefix = "destilink.fw.locking.redis",
        name = "health-indicator-enabled",
        havingValue = "true",
        matchIfMissing = true)
    public RedisLockHealthIndicator redisLockHealthIndicator(
            RedisConnectionFactory connectionFactory,
            RedisLockProperties properties,
            LockMonitor lockMonitor) {
        return new RedisLockHealthIndicator(connectionFactory, properties, lockMonitor);
    }
}
```

## Integration with Lock Implementations

Lock implementations use the `LockMonitor` to record metrics during their operations:

```java
public abstract class AbstractRedisLock {
    protected final LockMonitor lockMonitor;
    
    protected void recordLockAcquisitionAttempt(String lockKey) {
        if (lockMonitor != null) {
            lockMonitor.recordLockAcquisitionAttempt(lockKey);
        }
    }
    
    protected void recordLockAcquisitionSuccess(String lockKey, long startTime) {
        if (lockMonitor != null) {
            long durationMs = System.currentTimeMillis() - startTime;
            lockMonitor.recordLockAcquisitionSuccess(lockKey, durationMs);
        }
    }
    
    protected void recordLockAcquisitionFailure(String lockKey, String reason) {
        if (lockMonitor != null) {
            lockMonitor.recordLockAcquisitionFailure(lockKey, reason);
        }
    }
    
    protected void recordLockAcquisitionTimeout(String lockKey) {
        if (lockMonitor != null) {
            lockMonitor.recordLockAcquisitionTimeout(lockKey);
        }
    }
    
    protected void recordLockRelease(String lockKey, long acquisitionTime) {
        if (lockMonitor != null) {
            long heldTimeMs = System.currentTimeMillis() - acquisitionTime;
            lockMonitor.recordLockRelease(lockKey, heldTimeMs);
        }
    }
}
```

Example usage in `RedisReentrantLock`:

```java
public class RedisReentrantLock extends AbstractRedisLock {
    private final String lockKey;
    private long acquisitionTime;
    
    @Override
    public void lock() {
        recordLockAcquisitionAttempt(lockKey);
        long startTime = System.currentTimeMillis();
        
        try {
            // Lock acquisition logic...
            
            acquisitionTime = System.currentTimeMillis();
            recordLockAcquisitionSuccess(lockKey, startTime);
        } catch (LockAcquisitionException e) {
            recordLockAcquisitionFailure(lockKey, e.getMessage());
            throw e;
        }
    }
    
    @Override
    public void unlock() {
        try {
            // Unlock logic...
            
            recordLockRelease(lockKey, acquisitionTime);
        } catch (Exception e) {
            // Error handling...
        }
    }
}
```

## Metrics Categories

### 1. Lock Acquisition Metrics

| Metric Name                      | Type    | Description                                | Tags                |
| -------------------------------- | ------- | ------------------------------------------ | ------------------- |
| `redis.lock.acquisition.attempt` | Counter | Number of lock acquisition attempts        | `lockKey`           |
| `redis.lock.acquisition.success` | Counter | Number of successful lock acquisitions     | `lockKey`           |
| `redis.lock.acquisition.failure` | Counter | Number of failed lock acquisitions         | `lockKey`, `reason` |
| `redis.lock.acquisition.timeout` | Counter | Number of lock acquisitions that timed out | `lockKey`           |
| `redis.lock.acquisition.time`    | Timer   | Time taken to acquire a lock               | `lockKey`           |

### 2. Lock Release Metrics

| Metric Name            | Type    | Description                         | Tags      |
| ---------------------- | ------- | ----------------------------------- | --------- |
| `redis.lock.release`   | Counter | Number of lock releases             | `lockKey` |
| `redis.lock.held.time` | Timer   | Time a lock was held before release | `lockKey` |

### 3. Lease Extension Metrics

| Metric Name                          | Type    | Description                               | Tags      |
| ------------------------------------ | ------- | ----------------------------------------- | --------- |
| `redis.lock.lease.extension.attempt` | Counter | Number of lease extension attempts        | `lockKey` |
| `redis.lock.lease.extension.success` | Counter | Number of successful lease extensions     | `lockKey` |
| `redis.lock.lease.extension.failure` | Counter | Number of failed lease extensions         | `lockKey` |
| `redis.lock.lease.extension.timeout` | Counter | Number of lease extensions that timed out | `lockKey` |

### 4. Lock Conversion Metrics

| Metric Name                     | Type    | Description                           | Tags                              |
| ------------------------------- | ------- | ------------------------------------- | --------------------------------- |
| `redis.lock.conversion.attempt` | Counter | Number of lock conversion attempts    | `lockKey`, `from`, `to`           |
| `redis.lock.conversion.success` | Counter | Number of successful lock conversions | `lockKey`, `from`, `to`           |
| `redis.lock.conversion.failure` | Counter | Number of failed lock conversions     | `lockKey`, `from`, `to`, `reason` |
| `redis.lock.conversion.time`    | Timer   | Time taken to convert a lock          | `lockKey`, `from`, `to`           |

### 5. Redis Connection Metrics

| Metric Name                   | Type    | Description                       | Tags               |
| ----------------------------- | ------- | --------------------------------- | ------------------ |
| `redis.lock.connection.error` | Counter | Number of Redis connection errors | `error`            |
| `redis.lock.command.error`    | Counter | Number of Redis command errors    | `command`, `error` |

## Health Indicators

The `RedisLockHealthIndicator` provides health status information for the Redis lock system:

1. **Redis Connection**: Checks if the Redis connection is working by sending a PING command
2. **Lock Operations**: Verifies that lock operations are working by acquiring and releasing a test lock

The health status is exposed through Spring Boot Actuator's `/actuator/health` endpoint:

```json
{
  "status": "UP",
  "components": {
    "redisLock": {
      "status": "UP",
      "details": {
        "ping": "PONG",
        "testLock": "Successfully acquired and released"
      }
    },
    // Other health indicators...
  }
}
```

## Configuration

The metrics and monitoring behavior is configured through `RedisLockProperties`:

```java
@ConfigurationProperties(prefix = "destilink.fw.locking.redis")
@Validated
public class RedisLockProperties {
    /**
     * Whether to enable metrics collection for Redis locks.
     */
    private boolean metricsEnabled = true;
    
    /**
     * Whether to enable the health indicator for Redis locks.
     */
    private boolean healthIndicatorEnabled = true;
    
    /**
     * Whether to include detailed metrics for individual lock keys.
     * When false, only bucket-level metrics are collected.
     */
    private boolean detailedMetricsEnabled = false;
    
    /**
     * Maximum number of distinct lock keys to track in metrics.
     * This prevents excessive tag cardinality.
     */
    @Min(1)
    private int maxTrackedLockKeys = 1000;
    
    // Getters and setters...
}
```

## Integration with Monitoring Systems

### Prometheus

The metrics are exposed through Spring Boot Actuator's `/actuator/prometheus` endpoint in Prometheus format:

```
# HELP redis_lock_acquisition_attempt_total Number of lock acquisition attempts
# TYPE redis_lock_acquisition_attempt_total counter
redis_lock_acquisition_attempt_total{lockKey="user_profile"} 1245.0
redis_lock_acquisition_attempt_total{lockKey="order_processing"} 3456.0

# HELP redis_lock_acquisition_success_total Number of successful lock acquisitions
# TYPE redis_lock_acquisition_success_total counter
redis_lock_acquisition_success_total{lockKey="user_profile"} 1240.0
redis_lock_acquisition_success_total{lockKey="order_processing"} 3450.0

# HELP redis_lock_acquisition_time_seconds Time taken to acquire a lock
# TYPE redis_lock_acquisition_time_seconds summary
redis_lock_acquisition_time_seconds_count{lockKey="user_profile"} 1240.0
redis_lock_acquisition_time_seconds_sum{lockKey="user_profile"} 12.4
redis_lock_acquisition_time_seconds_count{lockKey="order_processing"} 3450.0
redis_lock_acquisition_time_seconds_sum{lockKey="order_processing"} 34.5
```

### Datadog

For Datadog integration, the metrics are automatically published to Datadog when the Datadog agent is configured with Spring Boot:

```yaml
management:
  metrics:
    export:
      datadog:
        enabled: true
        api-key: ${DATADOG_API_KEY}
        application-key: ${DATADOG_APPLICATION_KEY}
        step: 1m
```

### Custom Dashboards

The metrics can be used to create custom dashboards in monitoring systems:

1. **Lock Acquisition Success Rate**: `redis.lock.acquisition.success / redis.lock.acquisition.attempt`
2. **Lock Acquisition Time**: `redis.lock.acquisition.time` (percentiles)
3. **Lock Hold Time**: `redis.lock.held.time` (percentiles)
4. **Lease Extension Success Rate**: `redis.lock.lease.extension.success / redis.lock.lease.extension.attempt`
5. **Lock Conversion Success Rate**: `redis.lock.conversion.success / redis.lock.conversion.attempt`

## Operational Use Cases

### 1. Performance Monitoring

The timing metrics (`redis.lock.acquisition.time`, `redis.lock.held.time`) can be used to monitor the performance of lock operations:

- **High Acquisition Times**: May indicate Redis performance issues or contention
- **Long Hold Times**: May indicate potential bottlenecks in the application

### 2. Contention Detection

The acquisition metrics can be used to detect lock contention:

- **High Failure Rate**: `redis.lock.acquisition.failure / redis.lock.acquisition.attempt` indicates high contention
- **Timeout Rate**: `redis.lock.acquisition.timeout / redis.lock.acquisition.attempt` indicates waiting threads

### 3. Error Detection

The failure metrics can be used to detect errors:

- **Connection Errors**: `redis.lock.connection.error` indicates Redis connectivity issues
- **Command Errors**: `redis.lock.command.error` indicates Redis command execution issues

### 4. Capacity Planning

The metrics can be used for capacity planning:

- **Lock Usage**: `redis.lock.acquisition.attempt` by `lockKey` shows which locks are most used
- **Contention Hotspots**: `redis.lock.acquisition.failure` by `lockKey` shows which locks have the most contention

## Alerting

The metrics can be used to set up alerts for potential issues:

1. **Redis Connection Issues**:
   - Alert when `redis.lock.connection.error` increases
   - Alert when Redis health check fails

2. **High Contention**:
   - Alert when `redis.lock.acquisition.failure / redis.lock.acquisition.attempt` exceeds a threshold
   - Alert when `redis.lock.acquisition.timeout / redis.lock.acquisition.attempt` exceeds a threshold

3. **Performance Degradation**:
   - Alert when `redis.lock.acquisition.time` (95th percentile) exceeds a threshold
   - Alert when `redis.lock.lease.extension.failure` increases

## Best Practices

### 1. Tag Cardinality

To prevent excessive tag cardinality, the `LockMonitor` sanitizes lock keys and limits the number of distinct lock keys tracked:

```java
private String sanitizeLockKey(String lockKey) {
    // Extract bucket from lock key for better grouping
    String[] parts = lockKey.split(":");
    if (parts.length >= 2) {
        return parts[1]; // Return bucket name
    }
    
    return lockKey.replaceAll("[^a-zA-Z0-9_.-]", "_")
        .substring(0, Math.min(lockKey.length(), 32));
}
```

### 2. Detailed vs. Aggregated Metrics

The `detailedMetricsEnabled` property controls whether to collect metrics for individual lock keys or aggregate by bucket:

```java
if (properties.isDetailedMetricsEnabled()) {
    // Record metrics with full lock key
    meterRegistry.counter("redis.lock.acquisition.attempt", createTag(lockKey))
        .increment();
} else {
    // Record metrics with bucket only
    String bucket = extractBucketFromLockKey(lockKey);
    meterRegistry.counter("redis.lock.acquisition.attempt", Tag.of("bucket", bucket))
        .increment();
}
```

### 3. Health Check Frequency

The health check should be performed at an appropriate frequency to avoid overloading Redis:

```yaml
management:
  endpoint:
    health:
      show-details: always
      group:
        redis-lock:
          include: redisLock
      cache:
        time-to-live: 30s
```

## Conclusion

The metrics and monitoring infrastructure in the Redis Locking module provides comprehensive visibility into lock operations, performance, and health. This enables effective operational monitoring, troubleshooting, and capacity planning for distributed locking in the application.