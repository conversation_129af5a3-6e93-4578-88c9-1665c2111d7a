# Destilink Framework Configuration Validator

A tool to validate YAML configuration files against Destilink Framework standards.

## Features

- Scans all YAML files in the framework
- Validates property naming conventions (kebab-case)
- Ensures proper property prefixes (destilink.fw)
- Checks for proper value types (unquoted booleans)
- Generates detailed validation reports

## Usage

```bash
cd utils/config-validator
node config_validator.js
```

## Validation Rules

The validator checks for the following issues:

- **Property Prefix**: Framework properties should use the standard prefix `destilink.fw`
- **Naming Convention**: Properties should use kebab-case (not snake_case or camelCase)
- **Boolean Values**: Boolean values should not be quoted (use `true`, not `"true"`)

## Integration with CI/CD

The validator exits with code 1 if any errors are found, making it suitable for CI/CD pipelines:

```yaml
validate-config:
  script:
    - cd utils/config-validator
    - node config_validator.js
  allow_failure: false
```

## Customization

You can customize the validation rules by modifying the configuration at the top of the script:

- `rootDir`: Root directory to scan for YAML files
- `outputDir`: Directory where reports will be generated
- `ignorePatterns`: Directories to ignore during scanning
- `propertyPrefix`: Standard prefix for framework properties