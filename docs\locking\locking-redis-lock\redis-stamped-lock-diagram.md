# RedisStampedLock Activity Diagram

RedisStampedLock provides capabilities similar to Java's StampedLock, including write (exclusive), read (shared), and optimistic read modes. It returns string-based stamps representing lock acquisition that are used for unlocking and validation.

## Key Features

- Multiple Lock Modes: Supports write (exclusive), read (shared), and optimistic read modes
- String-Based Stamps: Returns stamps with different prefixes (W:, R:, O:) based on lock type
- Lock Conversion: Supports upgrading read locks to write locks and downgrading write locks to read locks
- Optimistic Concurrency Control: Provides version validation for optimistic reads

## Activity Diagram

```mermaid
flowchart TD
    %% Write Lock Operations
    start([Start]) --> writeLock["writeLock()"]
    writeLock --> checkWriteReentrant{"writeHoldsCount\n> 0?"}
    checkWriteReentrant -- Yes --> incrementWriteCount["Increment writeHoldsCount"]
    checkWriteReentrant -- No --> acquireWriteLock["Execute 'try_stamped_lock' Lua script\nwith 'write' mode"]
    
    acquireWriteLock --> checkWriteResult{"Result starts\nwith '-'?"}
    checkWriteResult -- Yes --> returnNull1[Return null]
    checkWriteResult -- No --> setWriteCount["Set writeHoldsCount = 1"]
    setWriteCount --> registerWriteWatchdog["Register with LockWatchdog"]
    setWriteCount --> createWriteStamp["Create stamp: 'W:' + lockOwnerId + ':' + UUID"]
    createWriteStamp --> returnWriteStamp[Return write stamp]
    incrementWriteCount --> createWriteStamp
    
    %% Read Lock Operations
    readLock["readLock()"] --> checkReadReentrant{"readHoldsCount\n> 0?"}
    checkReadReentrant -- Yes --> incrementReadCount["Increment readHoldsCount"]
    checkReadReentrant -- No --> acquireReadLock["Execute 'try_stamped_lock' Lua script\nwith 'read' mode"]
    
    acquireReadLock --> checkReadResult{"Result starts\nwith '-'?"}
    checkReadResult -- Yes --> waitAndRetry["Wait and retry\n(exponential backoff)"]
    checkReadResult -- No --> setReadCount["Set readHoldsCount = 1"]
    setReadCount --> registerReadWatchdog["Register with LockWatchdog"]
    setReadCount --> createReadStamp["Create stamp: 'R:' + result"]
    createReadStamp --> returnReadStamp[Return read stamp]
    incrementReadCount --> createReadStamp
    waitAndRetry --> checkMaxRetries{"Max retries\nreached?"}
    checkMaxRetries -- Yes --> throwLockAcq[Throw LockAcquisitionException]
    checkMaxRetries -- No --> acquireReadLock
    
    %% Optimistic Read Operations
    tryOptimisticRead["tryOptimisticRead()"] --> executeOptimistic["Execute 'try_stamped_lock' Lua script\nwith 'optimistic' mode"]
    executeOptimistic --> checkOptimisticResult{"Result starts\nwith '-'?"}
    checkOptimisticResult -- Yes --> returnZeroVersion["Return 'O:0'"]
    checkOptimisticResult -- No --> createOptimisticStamp["Create stamp: 'O:' + result"]
    createOptimisticStamp --> returnOptimisticStamp[Return optimistic stamp]
    
    %% Validate Operation
    validate["validate(stamp)"] --> checkStampValid{"stamp starts\nwith 'O:'?"}
    checkStampValid -- No --> throwIllegalArg[Throw IllegalArgumentException]
    checkStampValid -- Yes --> extractVersion["Extract version from stamp"]
    extractVersion --> executeValidate["Execute 'validate_stamp' Lua script"]
    executeValidate --> checkValidateResult{"Result == 1?"}
    checkValidateResult -- Yes --> returnTrue[Return true]
    checkValidateResult -- No --> returnFalse[Return false]
    
    %% Unlock Operation
    unlock["unlock(stamp)"] --> checkStampType{"What type\nof stamp?"}
    checkStampType -- "W:" --> unlockWrite["unlockWriteInternal(stamp)"]
    checkStampType -- "R:" --> unlockRead["unlockReadInternal(stamp)"]
    checkStampType -- "O:" --> doNothing["Do nothing (optimistic)"]
    checkStampType -- "Invalid" --> throwIllegalArg2[Throw IllegalArgumentException]
    
    unlockWrite --> checkWriteHeld{"writeHoldsCount\n> 0?"}
    checkWriteHeld -- No --> throwIllegalMonitor[Throw IllegalMonitorStateException]
    checkWriteHeld -- Yes --> decrementWriteCount["Decrement writeHoldsCount"]
    decrementWriteCount --> checkWriteZero{"writeHoldsCount\n== 0?"}
    checkWriteZero -- No --> returnPartial1[Return]
    checkWriteZero -- Yes --> unregisterWriteWatchdog["Unregister from LockWatchdog"]
    unregisterWriteWatchdog --> executeUnlockWrite["Execute 'unlock_stamped_lock' Lua script\nwith 'write' mode"]
    
    unlockRead --> checkReadHeld{"readHoldsCount\n> 0?"}
    checkReadHeld -- No --> throwIllegalMonitor2[Throw IllegalMonitorStateException]
    checkReadHeld -- Yes --> decrementReadCount["Decrement readHoldsCount"]
    decrementReadCount --> checkReadZero{"readHoldsCount\n== 0?"}
    checkReadZero -- No --> returnPartial2[Return]
    checkReadZero -- Yes --> unregisterReadWatchdog["Unregister from LockWatchdog"]
    unregisterReadWatchdog --> executeUnlockRead["Execute 'unlock_stamped_lock' Lua script\nwith 'read' mode"]
    
    %% Conversion Operations
    tryConvertToWriteLock["tryConvertToWriteLock(readStamp)"] --> validateReadStamp{"readStamp starts\nwith 'R:'?"}
    validateReadStamp -- No --> throwIllegalArg3[Throw IllegalArgumentException]
    validateReadStamp -- Yes --> checkReadLockHeld{"readHoldsCount\n> 0?"}
    checkReadLockHeld -- No --> throwIllegalMonitor3[Throw IllegalMonitorStateException]
    checkReadLockHeld -- Yes --> executeConvertToWrite["Execute 'convert_to_write_lock' Lua script"]
    executeConvertToWrite --> checkConvertResult{"Result starts\nwith '-'?"}
    checkConvertResult -- Yes --> returnNull2[Return null]
    checkConvertResult -- No --> updateLockCounts["Set readHoldsCount = 0\nSet writeHoldsCount = 1"]
    updateLockCounts --> updateWatchdog["Unregister read lock\nRegister write lock"]
    updateWatchdog --> createConvertedWriteStamp["Create stamp: 'W:' + result"]
    createConvertedWriteStamp --> returnConvertedStamp[Return write stamp]