# Redis Locking Module: Modernization Plan

## 1. Introduction

This document outlines the key modernization tasks for the `locking-redis-lock` module. The plan is based on the analysis of the existing codebase, the proposals in `redis-lock-improvements.md`, `mig.md`, `modernized_redis_lock_architecture_v2.md`, `redis_lock_configuration_proposal.md`, and the overall Destilink Framework guidelines, particularly those in `/.amazonq/rules/guidelines.md` and `lock-final/modernization-guide.md`.

The primary goal is to align the module with current best practices, improve maintainability, ensure adherence to framework-wide standards, enhance robustness, and implement a comprehensive logging strategy.

## 2. Key Modernization Tasks

### 2.1. Refactor Spring Auto-Configuration (`RedisLockAutoConfiguration.java`)

*   **Issue**: The current [`RedisLockAutoConfiguration.java`](../src/main/java/com/tui/destilink/framework/locking/redis/config/RedisLockAutoConfiguration.java:1) uses `@ComponentScan`. This is a critical violation of Destilink Framework guidelines.
*   **Action**:
    1.  Remove the `@ComponentScan(basePackages = "com.tui.destilink.framework.locking.redis")` annotation.
    2.  Identify all beans: `RedisLockOperationsImpl`, `ScriptLoader`, `LockBucketRegistry`, `LockWatchdog`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `UnlockMessageListenerManager`, `LockComponentRegistry`, and `LockMonitor` (optional).
    3.  Explicitly define these beans within `RedisLockAutoConfiguration.java` using `@Bean` methods.
    4.  Ensure appropriate `@ConditionalOn...` annotations.
    5.  Implement `LockComponentRegistry` as a central bean to hold and provide shared services.
    6.  Ensure `RedisLockAutoConfiguration` is registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`.

### 2.2. Implement `LockComponentRegistry`

*   **Proposal**: From `modernized_redis_lock_architecture_v2.md`.
*   **Action**:
    1.  Create `LockComponentRegistry` class.
    2.  Define as a bean in `RedisLockAutoConfiguration`.
    3.  Inject shared services (`ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `ObjectProvider<LockMonitor>`) into it.
    4.  Modify `LockBucketRegistry` and `AbstractRedisLock` to use `LockComponentRegistry`.

### 2.3. Implement `UnlockMessageListenerManager` and `UnlockMessageListener`

*   **Proposal**: From `modernized_redis_lock_architecture_v2.md` and recent feedback.
*   **Action**:
    1.  Create `UnlockMessageListenerManager` class. Define as a bean.
    2.  This manager will be responsible for creating, managing the lifecycle (start/stop), and providing `UnlockMessageListener` instances. **Crucially, one `UnlockMessageListener` will be created per unique lock key (`<prefix>:<bucketName>:__unlock_channels__:{{lockName}}`) on demand.**
    3.  **`UnlockMessageListener` Enhancement**:
        *   Each `UnlockMessageListener` instance (now per lock key) will manage a single `LockSemaphoreHolder`.
        *   The `LockSemaphoreHolder` objects within the `UnlockMessageListenerManager` (or a similar central point if listeners are very short-lived) should be managed using Guava `CacheBuilder.newBuilder().weakValues().build()`. This ensures `LockSemaphoreHolder` objects are eligible for GC when no longer strongly referenced by waiting threads.
        *   The `LockSemaphoreHolder` itself will be keyed by the full lock key it represents.
    4.  Verify and refine `UnlockMessageListener.calculatePermitsToRelease` logic for different `UnlockType`s as per `mig.md` (this logic might simplify if a listener handles only one lock key's semaphore).
    5.  The wait time for `LockSemaphoreHolder.waitForUnlock()` will be determined by the current TTL of the lock in Redis, not a separate configuration property.

### 2.4. Refine Exception Hierarchy & Integrate Structured Logging

*   **Proposal**: From `redis-lock-improvements.md`, `modernized_redis_lock_architecture_v2.md`, and recent feedback on `ExceptionMarkerProvider`.
*   **Action**:
    1.  Ensure `AbstractRedisLockException` is the base class for all module exceptions.
    2.  **`AbstractRedisLockException` MUST implement [`com.tui.destilink.framework.core.logging.marker.exception.ExceptionMarkerProvider`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/ExceptionMarkerProvider.java:1).**
        *   Recommend `AbstractRedisLockException` extends [`com.tui.destilink.framework.core.logging.marker.exception.MarkerNestedRuntimeException`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/marker/exception/MarkerNestedRuntimeException.java:1).
    3.  Constructors of `AbstractRedisLockException` and its subclasses **MUST** enforce provision of required contextual fields for marker generation (`lockName`, `lockType`, `lockOwnerId`, `requestUuid`, etc.).
    4.  Implement `getMarker()` in `AbstractRedisLockException` to return an SLF4J `Marker` (e.g., `Markers.appendEntries(contextMap)`) containing these fields. Subclasses will use a `populateSpecificMarkers(Map<String, Object> contextMap)` method to add their specific details.
    5.  Review and update existing exceptions (`LockAcquisitionException`, `LockTimeoutException`, `LockReleaseException`, `LeaseExtensionException`, `StateMismatchException`, etc.) to align with this, ensuring they provide their specific context to the marker map.
    6.  Add proposed exceptions (`LockInterruptedException`, `LockCommandException`, `LockConnectionException`, `LockNotOwnedException`) if needed for clarity.
    7.  Update `RedisLockErrorHandler` to translate underlying Redis/Lettuce exceptions into these structured, contextual, marker-providing exceptions.

### 2.5. Align Configuration, Watchdog, and Lua Scripts

*   **Proposal**: From `redis_lock_configuration_proposal.md`, `watchdog.md`, and recent feedback.
*   **Action**:
    1.  Ensure `RedisLockProperties.java` correctly reflects all global properties including `leaseTime`, `retryInterval`, `watchdogMaxTtl`, `watchdogRenewalMultiplier`, `stateKeyExpiration`, etc. **Remove YAML-based bucket definitions (`destilink.fw.locking.redis.buckets.<name>.*`) and related map in `RedisLockProperties`.**
    2.  Update `LockBucketConfig.java` to be initialized with global defaults and then allow programmatic overrides via `LockBucketBuilder` for properties like default `leaseTime`, `retryInterval`, `useWatchdog` policy, and default `stateKeyExpiration` for the bucket context.
    3.  Update `LockWatchdog.java`:
        *   Lease extension interval calculation in `LockInfo` must use global `watchdogMaxTtl` and `watchdogRenewalMultiplier`.
        *   When calling the "extend\_lock.lua" script, the new lease time argument passed **MUST** be the `watchdogMaxTtl` (in milliseconds).
    4.  Update `RedisLockOperationsImpl.java` (or relevant classes calling Lua scripts) to pass the correct, modernized property names as arguments to all Lua scripts.
    5.  Review and update **"extend\_lock.lua"** script:
        *   Ensure it correctly uses the passed `watchdogMaxTtl` when executing `PEXPIRE`.
        *   Verify atomic ownership check before extending TTL.
    6.  Verify conditions for watchdog activation: bucket policy (`use-watchdog` from `LockBucketConfig`), application-instance-bound lock, and instance `leaseTime > watchdogMaxTtl`.

### 2.6. Review and Refine Lua Scripts (General)

*   **Action**:
    1.  Review all Lua scripts in `src/main/resources/lua/` for correctness, atomicity, and efficiency.
    2.  Ensure "unlock.lua" correctly publishes messages to Pub/Sub (channel format: `<prefix>:<bucketName>:__unlock_channels__:{{lockName}}`), including `lockKey` and `UnlockType`.
    3.  Update `lua_scripts.md` to document each script's parameters, behavior, and return values, reflecting any argument changes.

### 2.7. Enhance `LockSemaphoreHolder` Waiting Logic

*   **Proposal**: From `mig.md` and `redis-lock-improvements.md`.
*   **Action**:
    1.  Verify `LockSemaphoreHolder.waitForUnlock()` correctly drains stale permits (`semaphore.drainPermits()`) before `tryAcquire`. The actual wait duration will be determined by the lock's TTL in Redis when `AbstractRedisLock` calls this method.

### 2.8. Implement Comprehensive Logging Strategy

*   **Action**:
    1.  **MDC Setup**: Implement/verify [`LockContextDecorator.java`](../src/main/java/com/tui/destilink/framework/locking/redis/logging/context/LockContextDecorator.java:1) aligns with [`AbstractContextDecorator`](../../../../core/src/main/java/com/tui/destilink/framework/core/logging/context/decorator/AbstractContextDecorator.java:1). Ensure it populates MDC with `lock.name`, `lock.operation`, `lock.ownerId`, `lock.requestId` using try-with-resources `Scope`.
    2.  **Parameterized Logging**: Enforce use of SLF4J parameterized messages throughout the module.
    3.  **Structured Exception Logging**: Leverage the `ExceptionMarkerProvider` integration (Task 2.4) for detailed JSON exception logs.
    4.  **Log Levels**: Review and assign appropriate log levels (`DEBUG`, `INFO`, `WARN`, `ERROR`) for all log statements.
    5.  Document this logging strategy in `architecture.md`.

### 2.9. General Code Modernization (Ongoing)

*   **Guidance**: From `lock-final/modernization-guide.md`.
*   **Action**:
    *   **Testing**: Ensure comprehensive test coverage.
    *   **Java Features**: Use `Optional`, Stream API, lambdas effectively.
    *   **Resilience Code Cleanup**: Remove all vestiges of Resilience4j.

## 3. Verification

After implementation:
*   Validate against Destilink Framework guidelines (`/.amazonq/rules/guidelines.md`).
*   Conduct thorough testing (unit, integration).

This plan aims to create a more robust, maintainable, and guideline-compliant `locking-redis-lock` module.