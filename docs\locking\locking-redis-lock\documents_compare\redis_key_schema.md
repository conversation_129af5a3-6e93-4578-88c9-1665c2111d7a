# Modernized Redis Locking Module: Redis Key Schema

This document outlines the Redis key schema used by the modernized locking module. A well-defined and consistent key schema is crucial for managing lock data effectively in Redis and avoiding conflicts. **The schema described here is mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md` and implemented in `LockBucket.java`.**

## Key Structure

All keys used by the locking module follow a hierarchical structure using colons (`:`) as separators. The general format is:

```
<prefix>:<namespace>:<bucket_name>:<lock_key>:<suffix>
```

*   **`<prefix>`**: A configurable prefix to avoid key collisions with other applications or modules in the same Redis instance. This is defined by the `destilink.locking.redis.prefix` property in `RedisLockProperties`.
*   **`<namespace>`**: A fixed namespace to clearly identify keys belonging to the locking module. This is typically `__locks__`.
*   **`<bucket_name>`**: The name of the lock bucket, as configured in `LockBucketConfig`. This allows for logical grouping and configuration of locks.
*   **`<lock_key>`**: The unique identifier for the specific lock instance within the bucket. This is provided by the user when acquiring a lock.
*   **`<suffix>`**: An optional suffix used for specific data related to a lock, such as state or read/write lock components.

Additionally, Redis hash tags (`{{...}}`) are used around the `<bucket_name>:<lock_key>` part of the key for certain key types to ensure that related keys are stored in the same hash slot when using Redis Cluster. This is important for operations that involve multiple keys atomically, such as Lua scripts.

## Key Types and Examples

The module uses different key types for various purposes:

### Main Lock Key (String or Hash)

This is the primary key representing the lock itself. For standard locks, it stores the lock owner's identifier. **For reentrant locks, this key is a Redis Hash and stores the lock owner's identifier and the reentrancy count.**

*   **Structure:** `<prefix>:__locks__:{{<bucket_name>:<lock_key>}}`
*   **Type:** Redis String (for standard locks) or Redis Hash (for reentrant locks)
*   **Value (String Lock):** `<owner_id>` (e.g., `my-owner-id`)
*   **Value (Hash Lock - Reentrant):** A Redis Hash with fields:
    *   `owner`: The lock owner identifier (e.g., `my-owner-id`)
    *   `count`: The reentrancy count (e.g., `1`, `2`)
*   **TTL:** Set to the lock's lease time.
*   **Example (String Lock):** `my-app:__locks__:{{user-locks:user123}}`
*   **Example (Hash Lock - Reentrant):** `my-app:__locks__:{{resource-locks:printer}}` (This key is a Hash)

### State Lock Key (String)

Used specifically by `RedisStateLock` to store the current state associated with the lock.

*   **Structure:** `<prefix>:__locks__:{{<bucket_name>:<lock_key>}}:state`
*   **Type:** Redis String
*   **Value:** The state value (e.g., `"PROCESSING"`)
*   **TTL:** Can be configured separately from the main lock TTL.
*   **Example:** `my-app:__locks__:{{order-states:order456}}:state`

### Read Lock Key (Set)

Used by `RedisReadWriteLock` to store the owners of currently held read locks.

*   **Structure:** `<prefix>:__locks__:{{<bucket_name>:<lock_key>}}:read`
*   **Type:** Redis Set
*   **Value:** Set members are `<owner_id>:<reentrancy_count>` for each read lock holder.
*   **TTL:** Managed by the write lock or a separate mechanism to prevent stale read locks.
*   **Example:** `my-app:__locks__:{{resource-rw:file.txt}}:read`

### Write Lock Key (String or Hash)

Used by `RedisReadWriteLock` to store the owner of the currently held write lock. Similar structure to the main lock key. **For reentrant read-write locks, this key is a Redis Hash.**

*   **Structure:** `<prefix>:__locks__:{{<bucket_name>:<lock_key>}}:write`
*   **Type:** Redis String (for non-reentrant write locks) or Redis Hash (for reentrant write locks)
*   **Value (String Lock):** `<owner_id>`
*   **Value (Hash Lock - Reentrant):** A Redis Hash with fields:
    *   `owner`: The write lock owner identifier
    *   `count`: The reentrancy count for the write lock
*   **TTL:** Set to the write lock's lease time.
*   **Example (String Lock):** `my-app:__locks__:{{resource-rw:file.txt}}:write`
*   **Example (Hash Lock - Reentrant):** `my-app:__locks__:{{data-access:report}}` (This key is a Hash for the write lock)

### Unlock Channel Key (Pub/Sub Channel)

Used for Redis Pub/Sub messaging to notify waiting threads when a lock is released.

*   **Structure:** `<prefix>:__unlock_channels__:<bucket_name>:<lock_key>`
*   **Type:** Redis Pub/Sub Channel (not a standard key type stored in the keyspace)
*   **Example:** `my-app:__unlock_channels__:user-locks:user123`

### Response Cache Key (String)

Used for idempotency in state lock operations to cache the result of a state update.

*   **Structure:** `<prefix>:__resp_cache__:{{<bucket_name>:<lock_key>}}:<request_id>`
*   **Type:** Redis String
*   **Value:** Cached response data.
*   **TTL:** Short TTL, typically a few seconds.
*   **Example:** `my-app:__resp_cache__:{{order-states:order456}}:req789`

## Hash Tag Usage (`{{...}}`)

Hash tags are used around the `<bucket_name>:<lock_key>` segment for the main lock, state lock, read lock, and write lock keys. This ensures that all keys related to a specific lock instance within a bucket are co-located in the same hash slot in a Redis Cluster. This is critical for the atomicity of Lua scripts that operate on multiple related keys (e.g., acquiring a write lock might involve checking the read lock set).

The unlock channel key and response cache key do not use hash tags in the same way because they are not typically involved in multi-key atomic operations with the main lock keys. The unlock channel is a Pub/Sub concept, and the response cache is accessed independently.

## Implementation in `LockBucket.java`

The `LockBucket.java` class is responsible for constructing these Redis keys based on the configured prefix, bucket name, and the provided lock key. It encapsulates the key formatting logic, including the use of hash tags and determining whether to use a String or Hash based on the lock type configuration.

Understanding this key schema is essential for monitoring Redis, debugging lock issues, and ensuring correct configuration of the locking module.