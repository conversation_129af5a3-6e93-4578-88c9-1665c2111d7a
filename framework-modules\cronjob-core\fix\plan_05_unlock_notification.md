# Plan: Redis Lock - Step 5: Unlock Notification Mechanism

This step refactors the Pub/Sub mechanism to be robust and efficient, following the documented design of per-bucket listeners and per-key semaphores.

### Step 5.1: Implement `UnlockMessageListenerManager.java`

This bean manages the lifecycle of listeners.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/UnlockMessageListenerManager.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
public class UnlockMessageListenerManager {
    private final RedisConnectionFactory connectionFactory;
    private final Executor redisLockMessageExecutor;
    private final Map<String, UnlockMessageListener> listeners = new ConcurrentHashMap<>();

    public UnlockMessageListener getListener(String bucketName) {
        return listeners.computeIfAbsent(bucketName, this::createListener);
    }

    private UnlockMessageListener createListener(String bucketName) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setTaskExecutor(redisLockMessageExecutor);
        container.afterPropertiesSet();
        container.start();
        
        String channelPattern = buildChannelPattern(bucketName); // e.g., "prefix:bucket:__unlock_channels__:*"
        UnlockMessageListener listener = new UnlockMessageListener(channelPattern);
        container.addMessageListener(listener, new PatternTopic(channelPattern));
        return listener;
    }
    
    private String buildChannelPattern(String bucketName) {
        // Logic to build the channel pattern
        return "lock:" + bucketName + ":unlocks:*"; // Simplified example
    }
}
```

### Step 5.2: Implement `UnlockMessageListener.java`

This class listens to a pattern and signals the correct semaphore.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/UnlockMessageListener.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.model.UnlockType;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@RequiredArgsConstructor
public class UnlockMessageListener implements MessageListener {
    private final String channelPattern;
    private final Map<String, LockSemaphoreHolder> semaphoreHolders = new ConcurrentHashMap<>();

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel(), StandardCharsets.UTF_8);
        String lockName = extractLockNameFromChannel(channel);
        String unlockTypeStr = new String(message.getBody(), StandardCharsets.UTF_8);
        UnlockType unlockType = UnlockType.valueOf(unlockTypeStr);
        
        LockSemaphoreHolder holder = semaphoreHolders.get(lockName);
        if (holder != null) {
            holder.signal(unlockType);
        }
    }
    
    private String extractLockNameFromChannel(String channel) {
        // Logic to extract {lockName} from channel, e.g. "prefix:bucket:__unlock_channels__:{lock123}"
        return channel; // Placeholder
    }
    
    // Methods to add/remove/get semaphore holders
}
```

### Step 5.3: Implement `LockSemaphoreHolder.java`

This class manages the waiting logic.

**File to Create/Modify:** `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/service/LockSemaphoreHolder.java`

**Content:**
```java
package com.tui.destilink.framework.locking.redis.service;

import com.tui.destilink.framework.locking.redis.model.UnlockType;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

public class LockSemaphoreHolder {
    private final Semaphore semaphore = new Semaphore(0, true);
    private final AtomicInteger waiters = new AtomicInteger(0);
    // Could also hold a map of requestUuid -> CompletableFuture for async waiters

    public void signal(UnlockType unlockType) {
        // Logic to release one or more permits based on unlockType and waiters
        if (waiters.get() > 0) {
            semaphore.release();
        }
    }
    
    // Other methods: await, addWaiter, removeWaiter etc.
}
```

---
