# Redis Locking Module: Configuration Architecture

This diagram illustrates the configuration architecture of the `locking-redis-lock` module, showing how properties, auto-configuration, and conditional activation work together.

```mermaid
classDiagram
    %% Configuration Properties
    class RedisLockProperties {
        +enabled: boolean = true
        +prefix: String = "lock"
        +defaultBucket: LockBucketConfig
        +buckets: Map~String, LockBucketConfig~
        +getEffectiveBucketConfig(bucketName: String): LockBucketConfig
        +getDefaultBucketConfig(): LockBucketConfig
    }
    
    class LockBucketConfig {
        +lockTimeoutMs: long = 10000
        +lockLeaseTimeMs: long = 30000
        +lockWatchdogEnabled: boolean = true
        +lockWatchdogIntervalMs: long = 10000
        +retryCount: int = 3
        +retryDelayMs: long = 200
        +nonBlockingTimeoutMs: long = 0
        +pubSubEnabled: boolean = true
        +pubSubChannelNamePrefix: String = "unlock_channel"
        +metricsEnabled: boolean = true
        +hashTagsEnabled: boolean = true
        +mergeWith(other: LockBucketConfig): LockBucketConfig
    }
    
    %% Auto-Configuration
    class RedisLockAutoConfiguration {
        <<@AutoConfiguration>>
        +redisLockProperties(): RedisLockProperties
        +redisLockErrorHandler(): RedisLockErrorHandler
        +defaultLockOwnerSupplier(): DefaultLockOwnerSupplier
        +lockMonitor(): LockMonitor
    }
    
    class RedisLockCoreConfiguration {
        <<static class>>
        +scriptLoader(): ScriptLoader
        +clusterCommandExecutor(): ClusterCommandExecutor
        +redisLockOperations(): RedisLockOperations
    }
    
    class RedisLockComponentsConfiguration {
        <<static class>>
        +lockComponentRegistry(): LockComponentRegistry
        +lockWatchdog(): LockWatchdog
        +unlockMessageListenerManager(): UnlockMessageListenerManager
    }
    
    class RedisLockImplementationsConfiguration {
        <<static class>>
        +redisReentrantLock(): RedisReentrantLock
        +redisReadWriteLock(): RedisReadWriteLock
        +redisStampedLock(): RedisStampedLock
        +redisStateLock(): RedisStateLock
    }
    
    %% Conditional Annotations
    class ConditionalOnProperty {
        <<annotation>>
        +prefix: String
        +name: String
        +havingValue: String
        +matchIfMissing: boolean
    }
    
    class ConditionalOnMissingBean {
        <<annotation>>
        +value: Class[]
    }
    
    class ConditionalOnBean {
        <<annotation>>
        +value: Class[]
    }
    
    class EnableConfigurationProperties {
        <<annotation>>
        +value: Class[]
    }
    
    %% Spring Boot Integration
    class AutoConfigurationImports {
        <<META-INF file>>
        +com.tui.destilink.framework.locking.redis.config.RedisLockAutoConfiguration
    }
    
    class ConfigurationMetadata {
        <<META-INF file>>
        +destilink.fw.locking.redis.*
    }
    
    %% External Dependencies
    class RedisConnectionFactory {
        <<Spring Data Redis>>
    }
    
    class MeterRegistry {
        <<Micrometer>>
    }
    
    %% Relationships
    RedisLockAutoConfiguration --> RedisLockProperties : enables
    RedisLockAutoConfiguration *-- RedisLockCoreConfiguration : contains
    RedisLockAutoConfiguration *-- RedisLockComponentsConfiguration : contains
    RedisLockAutoConfiguration *-- RedisLockImplementationsConfiguration : contains
    
    RedisLockProperties *-- LockBucketConfig : contains
    
    RedisLockAutoConfiguration ..> ConditionalOnProperty : uses
    RedisLockAutoConfiguration ..> EnableConfigurationProperties : uses
    RedisLockCoreConfiguration ..> ConditionalOnMissingBean : uses
    RedisLockComponentsConfiguration ..> ConditionalOnBean : uses
    RedisLockImplementationsConfiguration ..> ConditionalOnMissingBean : uses
    
    RedisLockAutoConfiguration ..> AutoConfigurationImports : registered in
    RedisLockProperties ..> ConfigurationMetadata : described in
    
    RedisLockCoreConfiguration ..> RedisConnectionFactory : depends on
    RedisLockComponentsConfiguration ..> MeterRegistry : optional dependency
    
    %% Notes
    note for RedisLockProperties "Prefix: destilink.fw.locking.redis"
    note for RedisLockAutoConfiguration "Entry point for module auto-configuration"
    note for LockBucketConfig "Supports per-bucket configuration overrides"
    note for AutoConfigurationImports "META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports"
```

## Configuration Hierarchy

The Redis Locking module uses a hierarchical configuration approach:

1. **Global Module Configuration**
   - Enabled/disabled state for the entire module
   - Global prefix for all Redis keys
   - Default bucket configuration

2. **Per-Bucket Configuration**
   - Named buckets with specific configurations
   - Each bucket can override any property from the default configuration
   - Locks are grouped into buckets for logical separation

3. **Instance-Level Overrides**
   - Individual lock instances can override certain properties
   - Example: Custom timeout or lease time for specific locks

## Property Structure

```yaml
# Module-level configuration
destilink.fw.locking.redis:
  enabled: true                      # Enable/disable the entire module
  prefix: "lock"                     # Global prefix for all Redis keys
  
  # Default bucket configuration (applies when no specific bucket is configured)
  default-bucket:
    lock-timeout-ms: 10000           # Maximum time to wait for lock acquisition
    lock-lease-time-ms: 30000        # Time before lock expires if not renewed
    lock-watchdog-enabled: true      # Auto-extend lock leases
    lock-watchdog-interval-ms: 10000 # Interval for watchdog checks
    retry-count: 3                   # Number of acquisition attempts
    retry-delay-ms: 200              # Delay between retry attempts
    non-blocking-timeout-ms: 0       # Timeout for tryLock operations (0 = immediate)
    pub-sub-enabled: true            # Enable Redis Pub/Sub for unlock notifications
    pub-sub-channel-name-prefix: "unlock_channel" # Prefix for Pub/Sub channels
    metrics-enabled: true            # Enable Micrometer metrics
    hash-tags-enabled: true          # Enable Redis Cluster hash tags
  
  # Named bucket configurations (override default settings)
  buckets:
    user-locks:                      # Bucket for user-related locks
      lock-timeout-ms: 5000          # Shorter timeout for user locks
      lock-lease-time-ms: 60000      # Longer lease time
    
    payment-locks:                   # Bucket for payment-related locks
      lock-timeout-ms: 30000         # Longer timeout for payment locks
      retry-count: 5                 # More retries for payment locks
      metrics-enabled: true          # Always enable metrics for payment locks
```

## Configuration Resolution Process

When a lock is created or used, the configuration is resolved through this process:

1. **Bucket Resolution**:
   ```
   Specified Bucket → Default Bucket → Module Defaults
   ```

2. **Property Resolution**:
   ```
   Instance Override → Bucket Config → Default Bucket → Module Default → Hardcoded Default
   ```

3. **Conditional Bean Creation**:
   ```
   @ConditionalOnProperty → @ConditionalOnClass → @ConditionalOnMissingBean → @ConditionalOnBean
   ```

## Auto-Configuration Structure

The auto-configuration follows the Destilink Framework guidelines with a hierarchical structure:

1. **Main Auto-Configuration Class**:
   - `RedisLockAutoConfiguration`: Entry point with global conditionals
   - Enables configuration properties
   - Contains nested configuration classes

2. **Core Components Configuration**:
   - `RedisLockCoreConfiguration`: Essential services
   - Script loader, command executor, lock operations

3. **Supporting Components Configuration**:
   - `RedisLockComponentsConfiguration`: Infrastructure components
   - Component registry, watchdog, message listeners

4. **Lock Implementations Configuration**:
   - `RedisLockImplementationsConfiguration`: Concrete lock types
   - Reentrant, read-write, stamped, and state locks

## Configuration Impact on Behavior

The configuration directly affects several aspects of lock behavior:

1. **Performance Characteristics**:
   - Timeout and retry settings affect acquisition latency
   - Lease time affects lock duration
   - Watchdog interval affects CPU/network usage

2. **Reliability Characteristics**:
   - Pub/Sub settings affect unlock notification speed
   - Watchdog settings affect lock expiration handling
   - Retry settings affect acquisition success rate

3. **Operational Characteristics**:
   - Metrics settings affect observability
   - Hash tag settings affect Redis Cluster key distribution
   - Prefix settings affect key namespace isolation

## Configuration Extension Points

The module provides several extension points for customization:

1. **Custom Lock Owner Supplier**:
   - Override `DefaultLockOwnerSupplier` to customize lock ownership identification

2. **Custom Error Handler**:
   - Override `RedisLockErrorHandler` to customize exception translation

3. **Custom Lock Monitor**:
   - Override `LockMonitor` to customize metrics collection

4. **Custom Lock Implementations**:
   - Override any lock implementation bean to provide custom behavior