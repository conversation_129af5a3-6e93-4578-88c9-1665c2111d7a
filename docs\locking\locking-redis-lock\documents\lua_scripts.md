# Redis Locking Module: <PERSON><PERSON> Scripts

## 1. Introduction

The `locking-redis-lock` module relies heavily on Lua scripts executed on the Redis server to ensure atomicity for complex lock operations. These scripts are crucial for the correctness and consistency of the distributed locks.

All Lua scripts are loaded and cached by the [`ScriptLoader`](../src/main/java/com/tui/destilink/framework/locking/redis/core/ScriptLoader.java:1) bean at application startup. The [`RedisLockOperations`](../src/main/java/com/tui/destilink/framework/locking/redis/core/RedisLockOperations.java:1) service then uses these cached scripts for execution.

## 2. Lua Scripts Overview

The following Lua scripts are located in `framework-modules/locking/modules/locking-redis-lock/src/main/resources/lua/`:

*   **`try_lock.lua`**:
    *   **Purpose**: Attempts to acquire a standard or reentrant lock. If the lock is free, it sets the lock key with the owner ID and lease time. If the lock is already held by the same owner (for reentrant locks), it typically increments a hold count and extends the lease. If held by a different owner, it returns the remaining TTL of the current lock.
    *   **Key Operations**: `SETNX` (or `SET key value EX milliseconds NX` for non-reentrant), `HINCRBY` (for reentrant count), `PEXPIRE`, `GET`, `PTTL`.
    *   **Returns**: `null` or `1` on success, or the current lock's TTL in milliseconds if held by another. Some implementations might return specific codes for reentrancy success.

*   **`unlock.lua`**:
    *   **Purpose**: Releases a standard or reentrant lock. For reentrant locks, it decrements the hold count. If the count reaches zero, or for non-reentrant locks, it deletes the lock key. It must verify that the operation is performed by the current lock owner. It also publishes an unlock notification message to a Pub/Sub channel.
    *   **Key Operations**: `GET` (to check owner), `HINCRBY` (to decrement reentrant count), `DEL`, `PUBLISH`.
    *   **Returns**: `1` if the lock was successfully released by the owner, `0` if the lock was not held or not held by the caller.

*   **`extend_lock.lua`**:
    *   **Purpose**: Extends the lease (TTL) of an existing lock, but only if the caller is the current owner of the lock. This is used by the `LockWatchdog` and for reentrant lock lease extensions.
    *   **Arguments**:
        *   `KEYS[1]`: The lock key (e.g., `locks:mybucket:mylock`).
        *   `ARGV[1]`: The `expectedOwnerId`.
        *   `ARGV[2]`: The `newLeaseTimeMillis`. **When called by the `LockWatchdog`, this value MUST be the global `watchdogMaxTtl` (in milliseconds).** For other callers (e.g., reentrant lock internal extension), it might be a different lease time.
    *   **Key Operations**: `GET` (to check owner), `PEXPIRE`.
    *   **Returns**: `1` if the lease was successfully extended, `0` otherwise (e.g., lock not found or owner mismatch).
    *   **Example Logic**:
        ```lua
        -- KEYS[1] = lockKey
        -- ARGV[1] = expectedOwnerId
        -- ARGV[2] = newLeaseTimeMillis (e.g., watchdogMaxTtl for watchdog calls)
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("PEXPIRE", KEYS[1], ARGV[2])
        else
          return 0
        end
        ```

*   **`extend_locks_batch.lua`**:
    *   **Purpose**: Extends the lease (TTL) for multiple locks in a single atomic operation. For each lock, it verifies ownership before extending. The `newLeaseTimeMillis` argument applies to all locks in the batch.
    *   **Arguments**:
        *   `KEYS`: A table of lock keys.
        *   `ARGV[1]`: The `expectedOwnerId`.
        *   `ARGV[2]`: The `newLeaseTimeMillis` (e.g., `watchdogMaxTtl` if called by a batch watchdog-like process).
    *   **Key Operations**: Iterates through input keys, performs `GET` and `PEXPIRE` for each if ownership matches.
    *   **Returns**: A list of results (e.g., `1` for success, `0` for failure) for each lock key provided.

*   **`try_state_lock.lua`**:
    *   **Purpose**: Attempts to acquire a state-based lock. This typically involves checking if a separate "state key" in Redis matches an expected value. If the state matches and the lock is free, it acquires the lock.
    *   **Key Operations**: `GET` (for state key), `SETNX` (or `SET ... NX` for lock key), `PEXPIRE`.
    *   **Returns**: Success/failure code or current lock TTL.

*   **`update_state.lua`**:
    *   **Purpose**: Updates the value of a "state key" associated with a `RedisStateLock`, typically only if the caller holds the main lock for that state.
    *   **Key Operations**: `GET` (to check lock ownership), `SET` (for state key), `PEXPIRE` (for state key TTL).
    *   **Returns**: Success/failure indicator.

*   **`unlock_state_lock.lua`**:
    *   **Purpose**: Releases a `RedisStateLock`. May also involve setting the associated state key to a specific value upon unlock, if configured. Verifies ownership. Publishes unlock notification.
    *   **Key Operations**: `GET` (to check owner), `DEL` (for lock key), `SET` (optionally for state key), `PUBLISH`.
    *   **Returns**: `1` for success, `0` for failure.

*   **`get_or_initialize_state.lua`**:
    *   **Purpose**: Retrieves the current value of a state key. If the state key does not exist, it initializes it with a provided default value and sets a TTL.
    *   **Key Operations**: `GET`, `SETNX` (or `SET ... NX` if conditional set on non-existence is needed with TTL), `PEXPIRE`.
    *   **Returns**: The current or newly initialized state value.

*   **`try_read_lock.lua` / `try_write_lock.lua`**:
    *   **Purpose**: Scripts for acquiring the read and write components of a `RedisReadWriteLock`. These handle the semantics of shared read access and exclusive write access, often using counters or sets in Redis to manage reader counts.
    *   **Key Operations**: `HINCRBY` (for reader/writer counts), `HGET`, `HSET`, `PEXPIRE`, `SETNX` (for write lock exclusivity).
    *   **Returns**: Success/failure codes or TTLs.

*   **`unlock_read_lock.lua` / `unlock_write_lock.lua`**:
    *   **Purpose**: Scripts for releasing the read and write components of a `RedisReadWriteLock`. Decrement reader counts or remove writer flags. Verify ownership. Publish unlock notifications (potentially distinguishing between read and write unlock types if it affects waking waiters).
    *   **Key Operations**: `HINCRBY` (decrement), `HDEL`, `DEL`, `PUBLISH`.
    *   **Returns**: Success/failure indicators.

*   **`try_stamped_lock.lua`**:
    *   **Purpose**: Implements the logic for acquiring a `RedisStampedLock`. This might involve checking and/or returning a "stamp" (version/sequence number) along with lock acquisition.
    *   **Key Operations**: Operations to manage the stamp value (e.g., `GET`, `INCRBY`) in addition to standard lock acquisition commands.
    *   **Returns**: Lock status and potentially the current stamp.

*   **`validate_stamp.lua`**:
    *   **Purpose**: Used by `RedisStampedLock` to check if a given stamp is still valid (e.g., has not changed since it was read).
    *   **Key Operations**: `GET` (to retrieve current stamp).
    *   **Returns**: `1` if valid, `0` if invalid.

*   **`convert_to_write_lock.lua`**:
    *   **Purpose**: For `RedisStampedLock`, attempts to atomically upgrade a read lock (associated with a specific stamp) to a write lock. This fails if the stamp has changed or if other read locks exist.
    *   **Key Operations**: Complex logic involving checking stamp, reader counts, and then acquiring the write lock.
    *   **Returns**: Success/failure indicator, or new stamp if successful.

*   **`unlock_stamped_lock.lua`**:
    *   **Purpose**: Releases a `RedisStampedLock`, potentially differentiating between releasing a read mode or write mode. Verifies ownership based on stamp and owner ID. Publishes unlock notification.
    *   **Key Operations**: `GET`, `DEL`, `PUBLISH`.
    *   **Returns**: Success/failure indicator.

## 3. Script Loading and Execution

*   **Loading**: The [`ScriptLoader`](../src/main/java/com/tui/destilink/framework/locking/redis/core/ScriptLoader.java:1) bean loads all these `.lua` files from the classpath at application startup. It caches them as both `org.springframework.data.redis.core.script.RedisScript` and `com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript` objects.
*   **Execution**: [`RedisLockOperationsImpl`](../src/main/java/com/tui/destilink/framework/locking/redis/core/impl/RedisLockOperationsImpl.java:1) retrieves the cached scripts from `ScriptLoader` and executes them using `ClusterCommandExecutor.executeScript(...)`. It handles the `EVALSHA`/`EVAL` fallback mechanism (though this is often handled by the underlying Redis client library like Lettuce).

The use of Lua scripts is fundamental to ensuring that multi-step locking operations are performed atomically on the Redis server, preventing race conditions in a distributed environment.