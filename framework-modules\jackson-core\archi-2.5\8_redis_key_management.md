# Redis Key Management and Construction

```mermaid
classDiagram
    class AbstractRedisKey {
        <<Abstract from redis-core>>
        +String getKey()
        +String toString()
        #String constructKey()
    }
    note for AbstractRedisKey "Base class from redis-core module<br/>Used for consistent key construction"

    class RedisKey {
        <<from redis-core>>
        +String keyValue
        +RedisKey(String key)
        +String getKey()
    }
    RedisKey --|> AbstractRedisKey

    class RedisKeyPrefix {
        <<from redis-core>>
        +String prefix
        +RedisKeyPrefix(String prefix)
        +String getPrefix()
        +RedisKey append(String suffix)
        +RedisKey appendWithHashTag(String hashTag, String suffix)
    }
    RedisKeyPrefix --|> AbstractRedisKey

    class RedisCoreProperties {
        <<from redis-core>>
        +KeyspacePrefixes keyspacePrefixes
        +String getPrefix(String keyspace)
    }

    class KeyspacePrefixes {
        +String application
        +String distributed
        +String locking
        +String getPrefix(String keyspace)
    }
    RedisCoreProperties o-- KeyspacePrefixes

    class LockBucket {
        +RedisKeyPrefix basePrefix
        +String bucketName
        +String scope
        +LockBucket(RedisKeyPrefix basePrefix, String bucketName, String scope)
        +RedisKey getLockKey(String lockName)
        +RedisKey getStateLockKey(String lockName)
        +RedisKey getResponseCacheKey(String lockName, String requestUuid)
        +RedisKey getReadWriteTimeoutKey(String lockName, String readerId, int reentrantCount)
        +String getUnlockChannel(String lockName)
        +String getUnlockChannelPattern()
        +String getUnlockChannelBaseName()
    }

    class RedisKeySchema {
        <<Utility Class>>
        +String LOCK_NAMESPACE = "__locks__"
        +String UNLOCK_CHANNELS_NAMESPACE = "__unlock_channels__"
        +String RESPONSE_CACHE_NAMESPACE = "__resp_cache__"
        +String RW_TIMEOUT_NAMESPACE = "__rwttl__"
        +String STATE_SUFFIX = ":state"
        +String STAMPED_SUFFIX = ":stamped"
        +String constructLockKey(String prefix, String bucket, String lockName)
        +String constructUnlockChannel(String prefix, String bucket, String lockName)
        +String constructResponseCacheKey(String prefix, String bucket, String lockName, String requestUuid)
    }

    LockBucket o-- RedisKeyPrefix : uses for base prefix
    LockBucket --> RedisKey : creates specific keys
    LockBucket --> RedisKeySchema : follows naming conventions

    RedisKeyPrefix --> RedisKey : creates via append methods
    RedisCoreProperties --> RedisKeyPrefix : provides prefix via KeyspacePrefixes

    class LockKeyExamples {
        <<Examples>>
        Main Lock: "myApp:__lock_buckets__:orders:__locks__:{order123}"
        State Key: "myApp:__lock_buckets__:orders:__locks__:{order123}:state"
        Stamped Lock: "myApp:__lock_buckets__:data:__locks__:{itemX}:stamped"
        Response Cache: "myApp:__lock_buckets__:orders:__resp_cache__:{order123}:a1b2-c3d4"
        RW Timeout Key: "myApp:__lock_buckets__:resource:__rwttl__:{configA}:readerThreadId_uuid:1"
        Unlock Channel: "myApp:__lock_buckets__:orders:__unlock_channels__:{order123}"
        Channel Pattern: "myApp:__lock_buckets__:orders:__unlock_channels__:*"
    }

    note for LockKeyExamples "All keys use hash tags {lockName} for Redis Cluster compatibility"

    RedisKeySchema --> LockKeyExamples : defines structure
```

## Key Construction Flow

```mermaid
sequenceDiagram
    participant LockBucketRegistry
    participant RedisCoreProperties
    participant LockBucket
    participant RedisKeyPrefix
    participant RedisKey

    LockBucketRegistry->>+RedisCoreProperties: getPrefix("locking")
    RedisCoreProperties-->>-LockBucketRegistry: "myApp" (from keyspace-prefixes.application)
    
    LockBucketRegistry->>+RedisKeyPrefix: new RedisKeyPrefix("myApp:__lock_buckets__")
    RedisKeyPrefix-->>-LockBucketRegistry: basePrefix instance
    
    LockBucketRegistry->>+LockBucket: new LockBucket(basePrefix, "orders", "application")
    LockBucket-->>-LockBucketRegistry: bucket instance
    
    Note over LockBucket: When lock key is needed
    LockBucket->>+RedisKeyPrefix: appendWithHashTag("order123", "__locks__")
    RedisKeyPrefix->>+RedisKey: new RedisKey("myApp:__lock_buckets__:orders:__locks__:{order123}")
    RedisKey-->>-RedisKeyPrefix: key instance
    RedisKeyPrefix-->>-LockBucket: RedisKey for lock
    
    Note over LockBucket: When unlock channel is needed
    LockBucket->>+RedisKeyPrefix: appendWithHashTag("order123", "__unlock_channels__")
    RedisKeyPrefix->>+RedisKey: new RedisKey("myApp:__lock_buckets__:orders:__unlock_channels__:{order123}")
    RedisKey-->>-RedisKeyPrefix: channel key instance
    RedisKeyPrefix-->>-LockBucket: unlock channel string
    
    Note over LockBucket: When response cache key is needed
    LockBucket->>+RedisKeyPrefix: appendWithHashTag("order123", "__resp_cache__:uuid-1234")
    RedisKeyPrefix->>+RedisKey: new RedisKey("myApp:__lock_buckets__:orders:__resp_cache__:{order123}:uuid-1234")
    RedisKey-->>-RedisKeyPrefix: cache key instance
    RedisKeyPrefix-->>-LockBucket: response cache key
```
