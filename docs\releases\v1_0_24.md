# [v1.0.24](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24?ref_type=tags)

## Banner Options
![v1.0.24 Banner Option 1](../images/nova_canvas_hk1yavm1_1.png)
![v1.0.24 Banner Option 2](../images/nova_canvas_o2us2s4e_1.png)
![v1.0.24 Banner Option 3](../images/nova_canvas_n402skdj_1.png)
![v1.0.24 Banner Option 4](../images/nova_canvas_hhr27w3h_1.png)
![v1.0.24 Banner Option 5](../images/nova_canvas_nzzbw03c_1.png)

### :exploding_head: Version Updates

- Spring Boot 3.2.9 -> 3.3.4
- Spring Cloud AWS IO 3.1.1 -> 3.2.0
- AWS Advanced JDBC Wrapper 2.3.9 -> 2.4.0
- Spring OpenSearch Starter 1.4.0 -> 1.5.3
- Logback Logstash Encoder 7.4 -> 8.0
- Datadog 1.39.0 -> 1.40.1

### :question: Upgrade Steps

- Move to new ms-parent as parent maven project for all of your microservices.

```xml
<dependency>
    <groupId>com.tui.destilink.framework.parent</groupId>
    <artifactId>ms-parent</artifactId>
    <version>1.0.24</version>
</dependency>
```

- Move to new cronjob-parent as parent maven project for all of your cronjobs. If your microservice consists of
  different profiles for an microservices and cronjob, then use the ms-parent above.

```xml
<dependency>
    <groupId>com.tui.destilink.framework.parent</groupId>
    <artifactId>cronjob-parent</artifactId>
    <version>1.0.24</version>
</dependency>
```

- Remove the `spring-boot-maven-plugin` from your microservice or cronjob, as it is pre-configured in the new parents.

- Optionally remove any `<m2e.apt.activation>jdt_apt</m2e.apt.activation>` from your projects because it is already
  activated by the framework.

- Remove any properties set in your application under `destilink.fw.aws.sqs.container.default`

### :raised_hand: Breaking Changes

- New ms-parent and cronjob-parent projects. The plugin configurations for other sub-frameworks, microservices and
  cronjobs
  differs a lot and gets complicated over time. That's why dedicated parents where introduced for microservices and
  cronjobs
  that ships all dependencies and plugins preconfigured. Even the `spring-boot-maven-plugin` is no longer required in
  the `pom.xml`
  of your application.
- [aws-sqs](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/aws/modules/aws-sqs?ref_type=tags)
    - The config-properties of the module changed. In general there is no need to overwrite the default properties, just
      remove all default container properties
      under `destilink.fw.aws.sqs.container.default`. Overwriting these properties is only required in some special
      cases. This framework delivers all configurations
      for our daily cases. Especially the  `maxConcurrentMessages` which is defaulted to 100 concurrent messages per
      listener.
    - The `Acknowledgement` is wrapped automatically by the framework and does not throw an exception anymore if the
      acknowledgement fails.
      This exception is handled by the framework.
- [Eclipse] The maven property `<m2e.apt.activation>jdt_apt</m2e.apt.activation>` to activate the eclipse maven plugin
  is now included in the framework by default. The property is no longer required on child-projects.

### :star: New Features

- `speing-boot-devtools` is now included and inherited by the framework to all child-projects. All modules of the
  `destilink-framework` are excluded from the restart classloader to fix classloading issues.
-

The [aws-aurora-postgresql](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.24/modules/aws/modules/aws-aurora-postgresql/src/main/resources/1000-aws-rds-aurora-postgresql-hikari-default.application.yml?ref_type=tags)
module now automatically sets applicationName-option on postgres-connections. Connections are only named by
the driver name by default but that makes monitoring in the AWS Performance Insights complicated. Setting the
application-name
on the connection allows us to differentiate the load on the database to different services. No manual interaction
configuration
is required as the `DD_SERVICE` is used or a default value.

- [resilience-circuitbreaker](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/resilience/modules/resilience-circuitbreaker?ref_type=tags)
  Introduce a new module with default configuration for the circuitbreaker.
- [test-core](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/test-support/modules/test-core?ref_type=tags)
  Tests are often failing when no bean of the `BuildProperties` exists. This can be fixed by importing the properties
  manually on each test `@Import(BuildProperties.class)`.
  The bean is now imported automatically by the framework if there is no other bean already existing.
- [docker-compose] Introducing a central `utils/docker-compose.yaml` file to setup all services required for tests at
  once. When you deploy this script, all required components, like localstack, redis etc. are deployed automatically.

### :bug: Bug Fixes

- [aws-aurora-postgresql](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/blob/v1.0.24/modules/aws/modules/aws-aurora-postgresql/src/main/resources/1000-aws-rds-aurora-postgresql-jpa.application.yml?ref_type=tags)
    - Fixed the default property-keys `hibernate.jdbc.order_inserts` -> `hibernate.order_inserts` and
      `hibernate.jdbc.order_updates` -> `hibernate.order_updates`
- [aws-s3](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/aws/modules/aws-s3?ref_type=tags)
    - Add missing condition on `spring.cloud.aws.s3.enabled` on the `S3AutoConfiguration`
    - Make the `S3AssumingRoleCrtClientFactory` to initialize lazy to allow tests without localstack which do not
      require to exclude AWS beans
- [aws-sqs](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/aws/modules/aws-sqs?ref_type=tags)
    - Fix manual handling of FIFO queues. There is no default process for handling messages from FIFO queues defined
      which can lead to multiple messages
      of the same group getting processed at the same time, loosing messages when the DLQ is used and messages getting
      processed in the wrong order.
      The implemented process solves these issues by manually deleting messages from the Queue and forwarding them to
      the DLQ if the processing failed
      unrecoverable. In addition, all pending messages of the same message group, that were received in a single
      receive-request, are skipped on a failure to keep the order of the messages.
      If the DLQ is used, failed messages are manually deleted after 1/10 of the `maxReceiveCount` setting to make sure
      following messages do not get redirected
      to the DLQ before a messages was processed at least once.

### :fire: Improvements

- [aws-localstack-test-support](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/aws/modules/aws-localstack-test-support?ref_type=tags)
    - Allow to define the `maxReceiveCount` and `maxReceiveCountFifo` of the DLQ in the `SqsQueue`-Annotation
- [aws-sqs](https://source.tui/sourcing-hotels/destilink/destilink/destilink-framework/-/tree/v1.0.24/modules/aws/modules/aws-sqs?ref_type=tags)
    - Messages are received in batches of up to 10 messages. They are converted sequentially and are processed parallel
      afterward. As some of our converters e.g. CloudEvents
      might have some IO, the received messages are processed in parallel in Virtual Threads now. To keep the order of
      FIFO queues, the messages are split
      into groups that are processed sequentially. For non-FIFO queues, all 10 messages are processed in parallel, but
      for FIFO-queues, the messages are processed
      sequentially within there message groups.
    - Messages that are not acknowledged in the `SqsListener` or an exception is thrown, the message is considered as
      failed and is forwarded to the DLQ and deleted immediately.
      To allow the message being processed again, the interface `SqsListenerRetryable` must be used as parameter of the
      `SqsListener` which injects a handler that can be used to allow a message to be reprocessed.
      The handler automatically takes care of the order of FIFO-messages and DLQ-handling.
      **Consumer example**
  ```java
  @Trace(operationName = "receiveMessage", resourceName = "receiveMultiCommitResponse")
  @SqsListener(value = "${drh.purchase-order-service.input.queue-name}")
  public void receiveMessageFromQueue(SqsListenerRetryable retryable, Acknowledgement acknowledgment, @Payload MultiCommitResponse payload) {
      try {
          retryable.allowRetry();
          multiCommitResponseProcessor.process(payload);
          acknowledgment.acknowledge();
      } catch (final CloudEventsException ex) {
          log.error(MonitoringMarkerUtils.appendForMonitoringIssue(BaseIssues.EXTERNAL_SNS, Mood.MR_HANKEY), "Error while sending message", ex); // trigger sqs retry in case of sns sending issues
          TracingUtils.setErrorOnSpan(ex, log);
      }
  }
  ```