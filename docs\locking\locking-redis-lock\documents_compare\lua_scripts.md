# Modernized Redis Locking Module: Lua Scripts

This document provides detailed documentation and implementations for the Lua scripts used by the modernized Redis locking module. Lua scripts are essential for performing atomic operations on Redis, ensuring the correctness and reliability of the distributed lock. **The scripts described and implemented here are mandatory and must be followed, as defined in the source document `lock-final/redis-lock-improvements.md` and implemented in the module.**

## Importance of Lua Scripts

Redis operations are typically executed one command at a time. In a distributed locking scenario, multiple commands are often required to acquire or release a lock (e.g., checking if a key exists, setting a key with a TTL, deleting a key, publishing a message). Without atomicity, race conditions can occur, leading to incorrect lock behavior (e.g., multiple clients acquiring the same lock).

Lua scripts allow a sequence of Redis commands to be executed as a single, atomic unit. This guarantees that either all commands in the script are executed successfully, or none are. This is crucial for the correctness of the distributed locking algorithm.

## Redis Cluster Compatibility with Hash Tags

All Redis keys in these scripts are expected to include the lockName within curly braces `{lockName}` to create a hash tag. This ensures that all related keys for a lock are stored on the same Redis node, allowing these multi-key operations to function correctly in a Redis Cluster environment.

For example, when the scripts operate on related keys like:
- `[keyspacePrefix]:__lockbucket__:[bucketName]:__[lockType]__:{lockName}`
- `[keyspacePrefix]:__lockbucket__:[bucketName]:__[lockType]__:{lockName}:state`

The hash tag `{lockName}` ensures both keys will be assigned to the same hash slot and therefore the same Redis node.

## Script Loading and Execution

The module loads Lua scripts from the classpath using the `ScriptLoader` component. Scripts are typically stored as `.lua` files. The `RedisLockOperations` interface provides methods to execute these scripts on the Redis server, handling the passing of keys and arguments.

## Key Lua Scripts Implementations

The following are the key Lua scripts implemented in the module:

### `try_lock.lua`

This script handles atomic lock acquisition with idempotency support.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: leaseTimeMs (lock expiration)
-- ARGV[3]: lockOwnerId (client identifier)
-- ARGV[4]: reentrancyEnabled (true/false)

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[2] .. ":__lock_response_cache__:" .. ARGV[1]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Core lock logic
local result
if redis.call('exists', KEYS[1]) == 0 then
    -- No lock exists, create it
    redis.call('hset', KEYS[1], 'owner', ARGV[3])
    if ARGV[4] == 'true' then
        redis.call('hset', KEYS[1], 'count', 1) -- Set reentrant count
    end
    redis.call('pexpire', KEYS[1], ARGV[2])
    result = nil -- Lock acquired
else
    -- Lock exists
    local owner = redis.call('hget', KEYS[1], 'owner')
    if owner == ARGV[3] and ARGV[4] == 'true' then
        -- Reentrant lock - same owner
        redis.call('hincrby', KEYS[1], 'count', 1)
        redis.call('pexpire', KEYS[1], ARGV[2]) -- Reset expiration
        result = nil -- Lock acquired reentrantly
    else
        -- Lock held by another owner or reentrant not enabled
        result = tostring(redis.call('pttl', KEYS[1])) -- Return TTL
    end
end

-- Cache the result for idempotency
local cacheTTL = 300 -- 5 minutes in seconds
redis.call('setex', responseCacheKey, cacheTTL, result or "nil")

return result
```

### `unlock.lua`

This script handles atomic lock release with idempotency support and built-in Pub/Sub notification.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: unlockChannel
-- KEYS[3]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: lockOwnerId
-- ARGV[3]: decrementReentrant (true/false)

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[3] .. ":__lock_response_cache__:" .. ARGV[1]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Core unlock logic
local result
if redis.call('hexists', KEYS[1], 'owner') == 0 then
    result = "0" -- Lock doesn't exist
elseif redis.call('hget', KEYS[1], 'owner') ~= ARGV[2] then
    result = "0" -- Not the lock owner
else
    local unlockComplete = false
    -- Check if reentrant
    local count = redis.call('hget', KEYS[1], 'count')

    if not count or count == "1" or ARGV[3] == 'false' then
        -- Not reentrant or last reentrant acquisition, fully unlock
        redis.call('del', KEYS[1])
        -- PUBLISH unlock notification ATOMICALLY
        redis.call('publish', KEYS[2], KEYS[1])
        unlockComplete = true
    else
        -- Decrement reentrant count
        redis.call('hincrby', KEYS[1], 'count', -1)
    end

    result = unlockComplete and "1" or "0"
end

-- Cache the result for idempotency
local cacheTTL = 300 -- 5 minutes in seconds
redis.call('setex', responseCacheKey, cacheTTL, result)

return result
```

### `extend_lock.lua`

This script handles lock expiration extension with idempotency support.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: leaseTimeMs (lock expiration)
-- ARGV[3]: lockOwnerId

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[2] .. ":__lock_response_cache__:" .. ARGV[1]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Core extend logic with ownership verification
local result
if redis.call('hexists', KEYS[1], 'owner') == 0 then
    result = "0" -- Lock doesn't exist
elseif redis.call('hget', KEYS[1], 'owner') ~= ARGV[3] then
    result = "0" -- Not the lock owner
else
    -- Extend the lock
    redis.call('pexpire', KEYS[1], ARGV[2])
    result = "1" -- Extension successful
end

-- Cache the result for idempotency
local cacheTTL = 300 -- 5 minutes in seconds
redis.call('setex', responseCacheKey, cacheTTL, result)

return result
```

## 2. StateLock Scripts

### `try_state_lock.lua`

This script handles atomic state-based lock acquisition with idempotency support and optional state initialization.

```lua
-- KEYS[1] = lockName
-- KEYS[2] = applicationPrefix (for response cache)
-- ARGV[1] = UUID for request idempotency
-- ARGV[2] = leaseTimeMs (lock expiration)
-- ARGV[3] = lockOwnerId
-- ARGV[4] = expectedState
-- ARGV[5] = stateKeySuffix
-- ARGV[6] = initIfNotExist (true/false)
-- ARGV[7] = stateExpirationMs (optional, "nil" if not set)

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[2] .. ":__lock_response_cache__:" .. ARGV[1]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Get current state
local stateKey = KEYS[1] .. ARGV[5]
local currentState = redis.call('get', stateKey)

-- Check if state exists and handle initialization
local result
if not currentState then
    if ARGV[6] == 'true' then
        -- Initialize state if requested
        redis.call('set', stateKey, ARGV[4])
        -- Apply expiration if configured
        if ARGV[7] ~= "nil" then
            redis.call('pexpire', stateKey, ARGV[7])
        end
        currentState = ARGV[4] -- Set for the next check
    else
        -- State doesn't exist and init not requested
        result = "-3" -- State not found
    end
end

-- If we have a result already, cache and return it
if result then
    -- Cache the result for idempotency
    local cacheTTL = 300 -- 5 minutes in seconds
    redis.call('setex', responseCacheKey, cacheTTL, result)
    return result
end

-- Check if state matches
if currentState ~= ARGV[4] then
    result = "-1" -- State mismatch
else
    -- State matches, try to acquire lock
    if redis.call('exists', KEYS[1]) == 0 then
        -- No lock exists, create it
        redis.call('hset', KEYS[1], 'owner', ARGV[3])
        redis.call('hset', KEYS[1], 'count', 1)
        redis.call('pexpire', KEYS[1], ARGV[2])
        result = nil -- Lock acquired
    elseif redis.call('hget', KEYS[1], 'owner') == ARGV[3] then
        -- Reentrant lock - same owner
        redis.call('hincrby', KEYS[1], 'count', 1)
        redis.call('pexpire', KEYS[1], ARGV[2])
        result = nil -- Lock acquired reentrantly
    else
        -- Lock held by another owner
        result = tostring(redis.call('pttl', KEYS[1])) -- Return TTL
    end
end

-- Cache the result for idempotency
local cacheTTL = 300 -- 5 minutes in seconds
redis.call('setex', responseCacheKey, cacheTTL, result or "nil")

return result
```

### `update_state.lua`

This script is used to update the state value associated with a lock in Redis.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: lockOwnerId
-- ARGV[3]: newState
-- ARGV[4]: stateKeySuffix
-- ARGV[5]: stateExpirationMs (optional, "nil" if not set)

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[2] .. ":__lock_response_cache__:" .. ARGV[1]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Verify lock ownership
local result
if redis.call('hexists', KEYS[1], 'owner') == 0 then
    result = "0" -- Lock doesn't exist
elseif redis.call('hget', KEYS[1], 'owner') ~= ARGV[2] then
    result = "0" -- Not the lock owner
else
    -- Update the state
    local stateKey = KEYS[1] .. ARGV[4]
    redis.call('set', stateKey, ARGV[3])

    -- Apply expiration if configured
    if ARGV[5] ~= "nil" then
        redis.call('pexpire', stateKey, ARGV[5])
    end

    result = "1" -- Success
end

-- Cache the result for idempotency
local cacheTTL = 300 -- 5 minutes in seconds
redis.call('setex', responseCacheKey, cacheTTL, result)

return result
```

### `unlock_state_lock.lua`

This script is used to release a state lock in Redis. It checks if the current lock owner matches the provided owner before releasing the lock.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: unlockChannel
-- KEYS[3]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: lockOwnerId
-- ARGV[3]: newState
-- ARGV[4]: stateKeySuffix
-- ARGV[5]: stateExpirationMs (optional, "nil" if not set)

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[3] .. ":__lock_response_cache__:" .. ARGV[1]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Core unlock logic with state update
local result
if redis.call('hexists', KEYS[1], 'owner') == 0 then
    result = "0" -- Lock doesn't exist
elseif redis.call('hget', KEYS[1], 'owner') ~= ARGV[2] then
    result = "0" -- Not the lock owner
else
    local unlockComplete = false
    local count = redis.call('hget', KEYS[1], 'count')

    if not count or count == "1" then
        -- Fully unlock
        redis.call('del', KEYS[1])

        -- Update the state
        local stateKey = KEYS[1] .. ARGV[4]
        redis.call('set', stateKey, ARGV[3])

        -- Apply expiration if configured
        if ARGV[5] ~= "nil" then
            redis.call('pexpire', stateKey, ARGV[5])
        end

        -- PUBLISH unlock notification ATOMICALLY
        redis.call('publish', KEYS[2], KEYS[1])
        unlockComplete = true
    else
        -- Decrement reentrant count
        redis.call('hincrby', KEYS[1], 'count', -1)
    end

    result = unlockComplete and "1" or "0"
end

-- Cache the result for idempotency
local cacheTTL = 300 -- 5 minutes in seconds
redis.call('setex', responseCacheKey, cacheTTL, result)

return result
```

## 3. Batch Operations Scripts

### `extend_locks_batch.lua`

This script optimizes watchdog renewal by handling multiple locks in a single operation.

```lua
-- KEYS: Array of lock keys to extend
-- ARGV[1]: leaseTimeMs (lock expiration)
-- ARGV[2+]: lockOwnerIds (one per lock)

local result = {}
for i = 1, #KEYS do
    if redis.call('hexists', KEYS[i], 'owner') == 1 then
        if redis.call('hget', KEYS[i], 'owner') == ARGV[i + 1] then
            -- Extend the lock
            redis.call('pexpire', KEYS[i], ARGV[1])
            table.insert(result, 1) -- Success
        else
            table.insert(result, 0) -- Not owner
        end
    else
        table.insert(result, 0) -- Lock doesn't exist
    end
end
return result
```

## 4. RedLock Support Scripts

### `try_redlock.lua`

This script implements optimized lock acquisition for use in the RedLock algorithm.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: leaseTimeMs (lock expiration)
-- ARGV[3]: lockOwnerId
-- ARGV[4]: instanceId

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[2] .. ":__lock_response_cache__:" .. ARGV[1] .. ":" .. ARGV[4]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

-- Core lock logic
local result
if redis.call('exists', KEYS[1]) == 0 then
    -- No lock exists, create it
    redis.call('set', KEYS[1], ARGV[3])
    redis.call('pexpire', KEYS[1], ARGV[2])
    result = "OK" -- Lock acquired
else
    -- Lock exists
    result = "LOCKED" -- Return locked status
end

-- Cache the result for idempotency with shorter TTL for RedLock operations
local cacheTTL = 60 -- 1 minute in seconds
redis.call('setex', responseCacheKey, cacheTTL, result)

return result
```

### `unlock_redlock.lua`

This script handles releasing a lock in the RedLock algorithm context.

```lua
-- KEYS[1]: lockName
-- KEYS[2]: applicationPrefix (for response cache)
-- ARGV[1]: UUID for request idempotency
-- ARGV[2]: lockOwnerId
-- ARGV[3]: instanceId

-- Check UUID cache first (idempotency)
local responseCacheKey = KEYS[2] .. ":__lock_response_cache__:" .. ARGV[1] .. ":" .. ARGV[3]
local cachedResponse = redis.call('get', responseCacheKey)
if cachedResponse then
    return cachedResponse -- Return cached response if found
end

local result
if redis.call('exists', KEYS[1]) == 0 then
    result = "OK" -- Lock doesn't exist, consider unlocked
else
    local value = redis.call('get', KEYS[1])
    if value == ARGV[2] then
        -- We are the lock owner
        redis.call('del', KEYS[1])
        result = "OK" -- Unlocked successfully
    else
        -- Not the lock owner
        result = "NOT_OWNER" -- Return not owner status
    end
end  -- End of if-else block

-- Cache the result for idempotency with shorter TTL for RedLock operations
local cacheTTL = 60 -- 1 minute in seconds
redis.call('setex', responseCacheKey, cacheTTL, result)

return result
```

## 5. Script Validation for Complex Scripts

### `try_state_lock.lua` Validation

```mermaid
flowchart TD
    A[Start] --> B{Check UUID Cache}
    B -->|Found| C[Return Cached Result]
    B -->|Not Found| D[Get Current State]

    D --> E{State Exists?}
    E -->|No| F{Init if Not Exist?}
    F -->|Yes| G[Initialize State]
    F -->|No| H[result = "-3" State Not Found]
    E -->|Yes| I[Continue to State Check]
    G --> I

    H --> J[Cache Result]
    J --> K[Return Result]

    I --> L{State Matches Expected?}
    L -->|No| M[result = "-1" State Mismatch]
    L -->|Yes| N{Lock Exists?}

    M --> J

    N -->|No| O[Create Lock]
    N -->|Yes| P{Same Owner?}

    O --> Q[result = nil Success]
    P -->|Yes| R[Increment Count]
    P -->|No| S[result = TTL Not Acquired]

    R --> Q
    S --> J
    Q --> J
```

This diagram validates that the Lua script:
1. Checks the cache first for idempotency
2. Properly validates the state before attempting lock acquisition
3. Handles state initialization when requested
4. Provides appropriate error codes for state not found or mismatch
5. Properly handles reentrant acquisition
6. Caches the result for all execution paths
7. Has a complete set of return values for all scenarios

### `update_state.lua` Validation

```mermaid
flowchart TD
    A[Start] --> B{Check UUID Cache}
    B -->|Found| C[Return Cached Response]
    B -->|Not Found| D{Lock Exists?}
    D -->|No| E[result = "0" Failed]
    D -->|Yes| F{Owner Match?}
    F -->|No| G[result = "0" Not Owner]
    F -->|Yes| H[Update State]
    H --> I{State Expiry Set?}
    I -->|Yes| J[Set Expiry on State]
    I -->|No| K[No Expiry]
    J --> L[result = "1" Success]
    K --> L
    E --> M[Cache Result]
    G --> M
    L --> M
    M --> N[Return Result]
```

By validating the scripts with these diagrams and fixing the syntax errors, we've ensured the Redis Lua scripts are correct and will function as expected in the Redis environment.