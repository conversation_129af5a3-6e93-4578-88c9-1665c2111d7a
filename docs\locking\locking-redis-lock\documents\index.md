# Redis Locking Module Documentation

This directory contains the detailed architecture and planning documents for the `locking-redis-lock` module of the Destilink Framework.

## Documents

1.  [**Architecture Overview (`architecture.md`)**](architecture.md): Describes the high-level architecture, components, their responsibilities, and interactions.
2.  [**Configuration (`configuration.md`)**](configuration.md): Details the configuration properties, their hierarchy (global, bucket, instance), and how they are applied.
3.  [**Lock Acquisition Flow (`lock_acquisition_flow.md`)**](lock_acquisition_flow.md): Explains the lock acquisition process, including infinite wait and timeout scenarios, based on the provided flowcharts and semaphore-based notification mechanism.
4.  [**Watchdog Mechanism (`watchdog.md`)**](watchdog.md): Describes the functionality of the `LockWatchdog` service for automatic lease extension.
5.  [**Exception Handling (`exception_handling.md`)**](exception_handling.md): Outlines the structured exception hierarchy for the module.
6.  [**<PERSON><PERSON>ripts (`lua_scripts.md`)**](lua_scripts.md): Details the purpose and usage of the Lua scripts involved in lock operations.
7.  [**Modernization Plan (`modernization_plan.md`)**](modernization_plan.md): Summarizes the key modernization tasks, focusing on adherence to Destilink Framework guidelines (e.g., removing `@ComponentScan`).
8.  [**Glossary (`glossary.md`)**](glossary.md): Defines key terms used within the locking module documentation.