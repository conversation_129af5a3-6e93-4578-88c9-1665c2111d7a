# Watchdog Mechanism

```mermaid
sequenceDiagram
    participant AbstractRedisLock
    participant LockWatchdog
    participant ScheduledExecutorService
    participant RedisLockOperations
    participant ScriptLoader
    participant RedisConnection

    AbstractRedisLock->>+LockWatchdog: registerLock(lockKey, lockOwnerId, effectiveLeaseTimeMillis)
    LockWatchdog->>LockWatchdog: Add to monitoredLocks (LockInfo: lockKey, ownerId, leaseTime, extensionInterval, nextExtensionTime)
    note right of LockWatchdog: Watchdog activated if bucket.useWatchdog=true, app-instance bound, leaseTime > watchdogMaxTtl
    LockWatchdog-->>-AbstractRedisLock: Lock Registered for Watchdog Monitoring

    loop Periodically (e.g., @Scheduled extendLocks() method)
        ScheduledExecutorService->>+LockWatchdog: execute extendLocks()
        LockWatchdog->>LockWatchdog: Iterate monitoredLocks
        alt For each lock where currentTime >= nextExtensionTime
            LockWatchdog->>+RedisLockOperations: executeScriptAsync("extend_lock", lock<PERSON><PERSON>, ownerId, watchdogMaxTtlMillis)
            RedisLockOperations->>+ScriptLoader: getScript("extend_lock")
            ScriptLoader->>+RedisConnection: EVALSHA/EVAL (extend_lock.lua, KEYS[1]=lockKey, ARGV[1]=ownerId, ARGV[2]=watchdogMaxTtlMillis)
            RedisConnection-->>-ScriptLoader: Script Result (1 if extended, 0 if not found/owner mismatch)
            ScriptLoader-->>-RedisLockOperations: Script Result
            RedisLockOperations-->>-LockWatchdog: Heartbeat Result (true/false)
            alt Lease Extended Successfully
                LockWatchdog->>LockWatchdog: Update nextExtensionTime for lockKey in monitoredLocks
                note right of LockWatchdog: Continue monitoring
            else Lease Not Extended (e.g., lock expired, owner mismatch, or error)
                LockWatchdog->>LockWatchdog: Remove lockKey from monitoredLocks
                note right of LockWatchdog: Stop monitoring. Lock considered lost.
            end
        end
    end

    alt Lock Released by Client
        AbstractRedisLock->>+LockWatchdog: unregisterLock(lockKey, lockOwnerId)
        LockWatchdog->>LockWatchdog: Remove lockKey from monitoredLocks if ownerId matches
        LockWatchdog-->>-AbstractRedisLock: Lock Unregistered from Watchdog
    end
```

```mermaid
classDiagram
    class LockWatchdog {
        +RedisLockProperties properties
        +RedisLockOperations lockOperations
        +ScriptLoader scriptLoader
        -ScheduledExecutorService executorService
        -Map<String, LockInfo> monitoredLocks
        +void registerLock(String lockKey, String lockOwnerId, long effectiveLeaseTimeMillis)
        +void unregisterLock(String lockKey, String lockOwnerId)
        +void extendLocks() // Internally scheduled
    }
    note for LockWatchdog "Manages lease extensions for active locks using global watchdogMaxTtl and watchdogRenewalMultiplier from RedisLockProperties"

    class LockWatchdog.LockInfo {
        String lockKey
        String lockOwnerId
        long originalLeaseTimeMillis
        long extensionIntervalMillis // Calculated from watchdogMaxTtl / watchdogRenewalMultiplier
        long nextExtensionTime
    }

    class AbstractRedisLock {
        #LockWatchdog lockWatchdog
        +void lock()
        +boolean tryLock()
        +void unlock()
    }
    
    class RedisLockProperties {
        +Duration watchdogMaxTtl
        +int watchdogRenewalMultiplier
        +Map<String, BucketConfig> buckets
    }

    class RedisLockProperties.BucketConfig {
        +boolean useWatchdog
        +Duration leaseTime // Default lease time for the bucket
    }

    class RedisLockOperations
    class ScriptLoader
    class ScheduledExecutorService

    LockWatchdog o-- RedisLockOperations : uses
    LockWatchdog o-- ScriptLoader : uses
    LockWatchdog o-- ScheduledExecutorService : uses
    LockWatchdog ..> RedisLockProperties : uses
    AbstractRedisLock o-- LockWatchdog : uses
    LockWatchdog "1" *-- "0..*" LockWatchdog.LockInfo : manages internally
```
