# RedisReadWriteLock Activity Diagram

RedisReadWriteLock is a Redis-based implementation of a read-write lock. It maintains a pair of associated locks, one for read-only operations and one for writing. The read lock may be held simultaneously by multiple reader threads, so long as there are no writers. The write lock is exclusive.

## Key Features

- Separate Read and Write Locks: Provides separate lock instances for reading and writing
- Multiple Concurrent Readers: Allows multiple threads to hold the read lock simultaneously
- Exclusive Write Access: Write lock prevents any concurrent reads or writes
- Reentrant Behavior: Both read and write locks support reentrancy within the same thread

## Activity Diagram

```mermaid
flowchart TD
    %% ReadLock Operations
    startRead([Start ReadLock]) --> readLock["readLock()"]
    readLock --> tryAcquireRead["tryAcquireLock(requestUuid, lockOwnerId)"]
    
    tryAcquireRead --> checkReadReentrant{"readHoldsCount\n> 0?"}
    checkReadReentrant -- Yes --> incrementReadCount["Increment readHoldsCount"]
    checkReadReentrant -- No --> executeAcquireRead["Execute 'try_read_lock' Lua script"]
    
    executeAcquireRead --> checkReadResult{"Result\nnull?"}
    checkReadResult -- Yes --> setReadCount["Set readHoldsCount = 1"]
    checkReadResult -- No --> returnReadTTL[Return TTL]
    setReadCount --> registerReadWatchdog["Register with LockWatchdog\n(lockName + ':read')"]
    setReadCount --> returnReadSuccess[Return null]
    incrementReadCount --> returnReadSuccess
    
    unlockRead["unlock() (ReadLock)"] --> releaseReadLock["releaseLock(requestUuid, lockOwnerId)"]
    
    releaseReadLock --> checkReadHeld{"readHoldsCount\n> 0?"}
    checkReadHeld -- No --> throwReadIllegalMonitor[Throw IllegalMonitorStateException]
    checkReadHeld -- Yes --> decrementReadCount["Decrement readHoldsCount"]
    
    decrementReadCount --> checkReadZero{"readHoldsCount\n== 0?"}
    checkReadZero -- No --> returnReadPartial[Return 0]
    checkReadZero -- Yes --> cleanupReadThreadLocal["Remove ThreadLocal"]
    cleanupReadThreadLocal --> unregisterReadWatchdog["Unregister from LockWatchdog"]
    unregisterReadWatchdog --> executeUnlockRead["Execute 'unlock_read_lock' Lua script"]
    executeUnlockRead --> returnReadResult[Return result]
    
    %% WriteLock Operations
    startWrite([Start WriteLock]) --> writeLock["writeLock()"]
    writeLock --> tryAcquireWrite["tryAcquireLock(requestUuid, lockOwnerId)"]
    
    tryAcquireWrite --> checkWriteReentrant{"writeHoldsCount\n> 0?"}
    checkWriteReentrant -- Yes --> incrementWriteCount["Increment writeHoldsCount"]
    checkWriteReentrant -- No --> executeAcquireWrite["Execute 'try_write_lock' Lua script"]
    
    executeAcquireWrite --> checkWriteResult{"Result\nnull?"}
    checkWriteResult -- Yes --> setWriteCount["Set writeHoldsCount = 1"]
    checkWriteResult -- No --> returnWriteTTL[Return TTL]
    setWriteCount --> registerWriteWatchdog["Register with LockWatchdog\n(lockName + ':write')"]
    setWriteCount --> returnWriteSuccess[Return null]
    incrementWriteCount --> returnWriteSuccess
    
    unlockWrite["unlock() (WriteLock)"] --> releaseWriteLock["releaseLock(requestUuid, lockOwnerId)"]
    
    releaseWriteLock --> checkWriteHeld{"writeHoldsCount\n> 0?"}
    checkWriteHeld -- No --> throwWriteIllegalMonitor[Throw IllegalMonitorStateException]
    checkWriteHeld -- Yes --> decrementWriteCount["Decrement writeHoldsCount"]
    
    decrementWriteCount --> checkWriteZero{"writeHoldsCount\n== 0?"}
    checkWriteZero -- No --> returnWritePartial[Return 0]
    checkWriteZero -- Yes --> cleanupWriteThreadLocal["Remove ThreadLocal"]
    cleanupWriteThreadLocal --> unregisterWriteWatchdog["Unregister from LockWatchdog"]
    unregisterWriteWatchdog --> executeUnlockWrite["Execute 'unlock_write_lock' Lua script"]
    executeUnlockWrite --> returnWriteResult[Return result]