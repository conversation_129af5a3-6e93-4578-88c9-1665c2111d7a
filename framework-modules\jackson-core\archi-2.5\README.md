# Redis Locking Module: Architecture Class Diagrams

This directory contains detailed Mermaid class diagrams that comprehensively document the architecture of the `locking-redis-lock` module. These diagrams are 100% consistent with the module's documentation and provide different perspectives on the system's design.

## Diagrams Overview


### 1. [Overall Component Interaction](1_overall_component_interaction.md)

Shows the main components of the `locking-redis-lock` module and their high-level interactions. This diagram illustrates how Spring-managed beans collaborate to provide distributed locking functionality, with emphasis on the integration with the `redis-core` module.

**Key Components Covered:**

- `RedisLockAutoConfiguration` (Spring auto-configuration)
- `AbstractRedisLock` and concrete implementations
- `RedisLockOperations` and `RedisLockOperationsImpl`
- `LockWatchdog`, `UnlockMessageListenerManager`, `ScriptLoader`
- Integration with `ClusterCommandExecutor` from `redis-core`


### 2. [Configuration and Property Hierarchy](2_configuration_property_hierarchy.md)

Details the configuration structure using `RedisLockProperties` and its relationship with Spring Boot's auto-configuration system. Shows how properties are organized and nested.

**Key Components Covered:**

- `RedisLockProperties` and nested property classes
- `WatchdogProperties`, `PubSubProperties`, `LuaScriptProperties`
- Integration with `RedisLockAutoConfiguration`



### 3. [Lock Acquisition Flow](3_lock_acquisition_flow.md)

Illustrates the sequence and components involved when a client attempts to acquire a lock, including the async-first approach and fallback mechanisms.

**Key Process Covered:**

- Lock acquisition attempt sequence
- Lua script execution for atomic operations (via `RedisLockOperationsImpl` and `ScriptLoader`)
- Pub/Sub waiting mechanism for efficient lock acquisition
- Watchdog integration for lease management


### 4. [Unlock Messaging (Pub/Sub) Mechanism](4_unlock_messaging_mechanism.md)

Explains the Redis Pub/Sub mechanism used for notifying waiting threads when locks are released, enabling non-polling lock acquisition.

**Key Components Covered:**

- `UnlockMessageListener` and message processing
- Redis Pub/Sub channel patterns and message structure
- Integration between lock release and waiting thread notification


### 5. [Watchdog Mechanism](5_watchdog_mechanism.md)

Describes how the `LockWatchdog` extends the lease of active locks to prevent premature expiration, including the scheduling and lease extension logic.

**Key Components Covered:**

- `LockWatchdog` scheduling and lease extension
- Integration with `RedisLockRegistry` for active lock tracking
- Lua script execution for atomic lease extension (via `RedisLockOperationsImpl` and `ScriptLoader`)


### 6. [Exception Handling Hierarchy](6_exception_handling_hierarchy.md)

Outlines the custom exception hierarchy rooted in `AbstractRedisLockException` and how errors are translated and handled throughout the module.

**Key Components Covered:**

- `AbstractRedisLockException` and specialized exception types
- `ExceptionMarkerProvider` integration for structured logging
- `RedisLockErrorHandler` for exception translation


### 7. [Lua Script Execution Architecture](7_lua_script_execution_architecture.md)

Shows how Lua scripts are loaded, managed, and executed to ensure atomic Redis operations, including the caching and fallback mechanisms.

**Key Components Covered:**

- `ScriptLoader` for script loading and caching
- Script execution flow through `RedisLockOperations`
- Integration with `ClusterCommandExecutor` for script execution
- EVALSHA/EVAL fallback mechanism


### 8. [Redis Key Management and Construction](8_redis_key_management.md)

Details how Redis keys are constructed and managed using the `redis-core` module's key construction utilities, ensuring consistency and Redis Cluster compatibility.

**Key Components Covered:**

- `AbstractRedisKey`, `RedisKey`, `RedisKeyPrefix` from `redis-core`
- `LockBucket` for key construction and schema management
- Key patterns and hash tag usage for cluster compatibility


### 9. [Lock Implementation Hierarchy](9_lock_implementation_hierarchy.md)

Illustrates the hierarchy of lock implementations, from the base `AbstractRedisLock` to specific lock types like `RedisReentrantLock`, `RedisStateLock`, etc.

**Key Components Covered:**

- `AsyncLock` interface and `AbstractRedisLock` base class
- Concrete lock implementations (`RedisReentrantLock`, `RedisStateLock`, `RedisReadWriteLock`, `RedisStampedLock`)
- Builder pattern for lock creation

### 10. [Builder Pattern and Configuration Flow](10_builder_pattern_configuration_flow.md)
Details the fluent builder API used for creating lock instances, showing the configuration flow from global defaults through bucket-level and instance-level overrides.

**Key Components Covered:**
- `LockBucketRegistry`, `LockBucketBuilder`, `LockConfigBuilder`
- `AbstractLockTypeConfigBuilder` and specific lock builders
- Configuration override precedence (Instance > Bucket > Global)

## Architectural Principles Reflected

These diagrams consistently reflect the key architectural principles of the `locking-redis-lock` module:

1. **Async-First Design**: All core operations are fundamentally asynchronous using `CompletableFuture`s
2. **Mandatory `redis-core` Usage**: All Redis interactions use `ClusterCommandExecutor` from the `redis-core` module
3. **Spring-Managed Shared Components**: Critical components are Spring-managed singleton beans
4. **Efficient Non-Polling Lock Acquisition**: Redis Pub/Sub for unlock notifications minimizes polling
5. **Atomicity**: Critical operations use Redis Lua scripts for atomic execution
6. **Strict Framework Guideline Adherence**: No `@ComponentScan`, explicit `@Bean` definitions, proper auto-configuration

## Consistency Notes

All diagrams maintain consistency in:
- Component naming and relationships
- Interface implementations and class hierarchies
- Dependency injection patterns
- Configuration flow and property precedence
- Async operation patterns
- Integration points with external modules (`redis-core`)

These diagrams serve as both documentation and reference for understanding the complete architecture of the Redis locking module within the Destilink Framework.
