# Redis Locking Module: Final Redis Key Schema

## 1. Introduction

This document outlines the consolidated Redis key schema used by the `locking-redis-lock` module. A well-defined and consistent key schema is crucial for managing lock data effectively in Redis, avoiding conflicts, and ensuring compatibility with Redis Cluster. This schema is based on the principles and details from previous planning phases and is implemented by `LockBucket.java`.

**Crucially, all Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. It is important to note that these specific key construction classes are, as of the current implementation, primarily utilized by the `locking-redis-lock` module. Should specific needs arise, or if other modules begin to leverage these classes extensively, they can be further reviewed and optimized for broader framework applicability or specialized performance characteristics.**

## 2. Key Structure Principles

*   **Hierarchical Naming**: Keys follow a hierarchical structure using colons (`:`) as separators for clarity and organization.
*   **Configurable Prefixing via `redis-core`**: The base prefix for all lock-related keys is derived from the `redis-core` module's configuration (`com.tui.destilink.framework.redis.core.config.RedisCoreProperties.KeyspacePrefixes`). Specifically, the `locking-redis-lock` module uses either the `application` or `distributed` keyspace prefix from `redis-core` and appends a static segment (e.g., `:__lock_buckets__`) to form the initial part of its keys. This ensures integration with the framework's global Redis key management strategy and avoids collisions in shared Redis environments. For example, if `redis-core`'s `keyspace-prefixes.application` is configured as `${spring.application.name}` (evaluating to `my-app`), the effective prefix for locking keys would start with `my-app:__lock_buckets__`.
*   **Standardized Key Construction via `redis-core`**: As stated above, all Redis keys, including those with hashtags, MUST be constructed using `com.tui.destilink.framework.redis.core.key.AbstractRedisKey`, `com.tui.destilink.framework.redis.core.key.RedisKey`, and `com.tui.destilink.framework.redis.core.key.RedisKeyPrefix` from the `redis-core` module. As noted in the introduction, these key construction classes from `redis-core` are currently primarily utilized by the locking module and can be further optimized if specific needs arise or their usage expands significantly across the framework.
*   **Namespacing**: Specific namespaces (e.g., `__locks__`, `__unlock_channels__`, `__resp_cache__`) are used to group related keys.
*   **Bucket-Based Grouping**: Locks are grouped by a `bucket_name` for logical organization and application of common configurations.
*   **Hash Tags for Cluster Compatibility**: The specific lock identifier (`<lockName>`) is enclosed in curly braces `{<lockName>}` to act as a hash tag. This tag is appended to a base key structure that includes the prefix and bucket name (e.g., `<prefix>:<bucketName>:<namespace>:{<lockName>}`).
*   **Type-Specific Storage**: Reentrant lock data (owner and count) is stored in a Redis Hash, while simpler locks might use Redis Strings.

## 3. General Key Format

The general format for most keys is:
`<prefix>:<bucketName>:<namespace>:{<lockName>}[:<suffix>]`

Unlock channels are not stored keys but are dynamically constructed Pub/Sub channel names.

### **Note on `<prefix>`:**

The `<prefix>` placeholder used throughout this schema refers to a composite prefix. It is formed by taking one of the configured keyspace prefixes from `com.tui.destilink.framework.redis.core.config.RedisCoreProperties.KeyspacePrefixes` (specifically, the value of the `application` or `distributed` property, e.g., 'my-app-name' or '\\_\\_distributed\\_\\_') and appending the static segment `:__lock_buckets__`.
For example, if `RedisCoreProperties.keyspacePrefixes.application` is 'myApp', then `<prefix>` effectively becomes `myApp:__lock_buckets__`. A full key for a lock named '{order123}' in the 'orders' bucket would then start as `myApp:__lock_buckets__:orders:__locks__:{order123}`.

## 4. Standardized Key Schema Table

The following table details the standardized formats for various key types used by the module:

| Key Type                         | Standardized Format                                                                                                                      | Hash Tag       | Example                                                                                                                                                | Notes                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| :------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Main Lock (Reentrant)            | `<prefix>:<bucketName>:__locks__:{<lockName>}`                                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:{order123}`                                                                                                   | Redis Hash. Stores `owner` and `count`. TTL set by leaseTime or watchdogMaxTtl.                                                                                                                                                                                                                                                                                                                                                                    |
| Main Lock (Non-Reentrant)        | `<prefix>:<bucketName>:__locks__:{<lockName>}`                                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:simple:__locks__:{taskXYZ}`                                                                                                    | Redis String. Value is `owner_id`. TTL set by leaseTime or watchdogMaxTtl. (Note: Current default `try_lock.lua` uses HASH for all main locks)                                                                                                                                                                                                                                                                                                     |
| State Key                        | `<prefix>:<bucketName>:__locks__:{<lockName>}:state`                                                                                     | `{<lockName>}` | `myApp:__lock_buckets__:orders:__locks__:{order123}:state`                                                                                             | Redis String. Value is the state. Used by `RedisStateLock`. TTL by `stateKeyExpiration`.                                                                                                                                                                                                                                                                                                                                                           |
| ReadWriteLock Main Hash          | `<prefix>:<bucketName>:__locks__:{<lockName>}`                                                                                           | `{<lockName>}` | `myApp:__lock_buckets__:resource:__locks__:{configA}`                                                                                                  | Redis Hash. Stores `mode` (\'read\'/\'write\'), `write_owner_id` (String), `write_reentrancy_count` (Integer). For each reader, it stores a field named after the reader\'s unique ID (e.g., `getLockName(threadId)` from client), with the value being the reader\'s reentrant count. Its TTL is cooperatively managed by its configured leaseTime (with watchdog) and the maximum remaining TTL of any active Individual Read Lock Timeout Keys. |
| Stamped Lock Data                | `<prefix>:<bucketName>:__locks__:{<lockName>}:stamped`                                                                                   | `{<lockName>}` | `myApp:__lock_buckets__:data:__locks__:{itemX}:stamped`                                                                                                | Redis Hash. Expected fields: \'version\' (Integer/String), \'write_owner_id\' (String), \'write_reentrancy_count\' (Integer), \'read_holders\' (Set of reader IDs or a reader count field). For `RedisStampedLock`.                                                                                                                                                                                                                                |
| Unlock Channel                   | Publish: `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}` <br/> Subscribe Pattern: `<prefix>:<bucketName>:__unlock_channels__:*` | N/A            | Publish: `myApp:__lock_buckets__:orders:__unlock_channels__:{order123}` <br/> Subscribe Pattern: `myApp:__lock_buckets__:orders:__unlock_channels__:*` | Locks publish to specific channels. `UnlockMessageListener` (per bucket) subscribes to a pattern. Message payload on the specific channel contains _only_ `UnlockType`. The specific lock identifier (e.g. `{order123}`) is derived from the channel name.                                                                                                                                                                                         |
| Individual Read Lock Timeout Key | `<prefix>:<bucketName>:__rwttl__:{<lockName>}:<readerId>:<reentrantCount>`                                                               | `{<lockName>}` | `myApp:__lock_buckets__:resource:__rwttl__:{configA}:readerThreadId_uuid:1`                                                                            | Redis String. Stores a placeholder (e.g., \'1\'). TTL set to the `leaseTime` of the specific reentrant read lock acquisition. Used by `RedisReadWriteLock`\'s Lua scripts (`try_read_lock.lua`, `unlock_read_lock.lua`) to manage individual read lock instance expirations and to correctly update the TTL of the main ReadWriteLock Hash. The `<readerId>` part corresponds to the field name in the main hash for that reader.                  |
| Response Cache                   | `<prefix>:<bucketName>:__resp_cache__:{<lockName>}:<requestUuid>`                                                                        | `{<lockName>}` | `myApp:__lock_buckets__:orders:__resp_cache__:{order123}:a1b2-c3d4`                                                                                    | Redis String. Caches Lua script responses for idempotency. TTL by `uuidCacheTtlSeconds`. Key is constructed by the client (Java lock implementation) and passed to Lua.                                                                                                                                                                                                                                                                            |

_Note: Specific key structures for `RedisReadWriteLock` and `RedisStampedLock` depend on their Lua script implementations. The table above provides a likely representation based on common patterns._

## 5. Key Design Considerations

*   **Clarity and Readability**: Keys are structured to be human-readable and understandable.
*   **Namespacing**: Segments like `__locks__`, `__unlock_channels__`, and `__resp_cache__` provide clear logical separation.
*   **Redis Cluster Compatibility**: The consistent use of hash tags `{<lockName>}` appended to a common base (e.g., `<prefix>:<bucketName>:<namespace>`) ensures that all keys directly pertaining to a single lock instance (main lock, state, read/write components, idempotency cache) are co-located on the same Redis Cluster node. This is essential for the atomicity of multi-key Lua scripts. Pub/Sub subscription patterns like `<prefix>:<bucketName>:__unlock_channels__:*` are handled by the client listener, while actual messages are published to specific channels like `<prefix>:<bucketName>:__unlock_channels__:{<lockName>}` which are typically broadcast across the cluster or handled by client routing.
*   **Implementation**: The `LockBucket.java` class (or a similar dedicated component) is responsible for the correct construction of these keys based on the provided configuration (prefix, bucket name) and lock details.

This consolidated key schema provides a robust foundation for the Redis locking module's data management.