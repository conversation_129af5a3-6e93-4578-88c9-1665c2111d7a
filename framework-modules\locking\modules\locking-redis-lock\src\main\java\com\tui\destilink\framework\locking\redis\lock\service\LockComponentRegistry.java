package com.tui.destilink.framework.locking.redis.lock.service;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;

/**
 * A central registry for shared services used by the Redis locking mechanism.
 * <p>
 * This class acts as a singleton bean that provides access to various
 * components
 * required for Redis-based distributed locking. It centralizes access to
 * critical
 * services like script loading, unlock message listening, lock watchdog, and
 * lock operations.
 * </p>
 * <p>
 * The registry is designed to be injected into other components that need
 * access
 * to these shared services, promoting a clean dependency structure and avoiding
 * circular dependencies.
 * </p>
 */
@RequiredArgsConstructor
@Getter
public class LockComponentRegistry {

    /**
     * Service responsible for loading and managing Redis Lua scripts.
     */
    private final ScriptLoader scriptLoader;

    /**
     * Manager for handling unlock message events in Redis pub/sub.
     */
    private final UnlockMessageListenerManager unlockMessageListenerManager;

    /**
     * Service that extends lock leases automatically to prevent premature
     * expiration.
     */
    private final LockWatchdog lockWatchdog;

    /**
     * Core service for Redis lock operations (acquire, release, extend).
     */
    private final RedisLockOperations redisLockOperations;

    /**
     * Default supplier for lock owner identifiers.
     */
    private final LockOwnerSupplier defaultLockOwnerSupplier; // Renamed for clarity

    /**
     * Global Redis lock properties.
     */
    private final com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties globalRedisLockProperties;

    /**
     * Handler for Redis lock-related errors.
     */
    private final RedisLockErrorHandler redisLockErrorHandler;

    /**
     * Optional provider for lock monitoring capabilities.
     * <p>
     * This is wrapped in an ObjectProvider to make it optional, allowing the
     * locking
     * system to function without monitoring if not configured.
     * </p>
     */
    private final ObjectProvider<LockMonitor> lockMonitorProvider;
}