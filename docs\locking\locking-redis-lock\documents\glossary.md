# Redis Locking Module: Glossary

This document defines key terms used within the `locking-redis-lock` module and its associated documentation.

*   **Application-Instance-Bound Lock**: A lock whose ownership is tied to a specific application instance (e.g., a specific pod in Kubernetes). The `LockOwnerSupplier` typically generates an owner ID that includes an instance identifier. These locks are eligible for watchdog lease renewal.
*   **Atomic Operation**: An operation that is guaranteed to execute fully without interruption or interference from other operations, typically ensured by executing commands as a single transaction or Lua script on the Redis server.
*   **Bucket (Lock Bucket)**: A logical grouping or namespace for locks. Buckets allow for applying common default configurations (e.g., `leaseTime`, `retryInterval`, `useWatchdog` policy) to a set of related locks. Configuration can be specified per bucket in YAML.
*   **Builder API**: A fluent API, starting with `LockBucketRegistry.builder()`, used to configure and create specific lock instances (e.g., `RedisReentrantLock`).
*   **Distributed Lock**: A synchronization primitive used to coordinate access to shared resources among multiple processes or services running in a distributed environment.
*   **Idempotency**: Ensuring that performing an operation multiple times has the same effect as performing it once. The module uses response caching for certain idempotent script executions.
*   **Lease Time (`leaseTime`)**: The total duration a developer *intends* for a lock to be held. This is a configuration parameter. If the watchdog is active for the lock, it will attempt to renew the lock's presence in Redis up to this total duration. If the watchdog is not active, `leaseTime` directly becomes the TTL of the lock key in Redis.
*   **Lock Key**: The unique string identifier for a specific lock in Redis (e.g., `locks:myBucket:myapp:resource123`).
*   **`LockBucketConfig`**: A Java class that holds the resolved, effective configuration settings for a specific lock bucket, after merging global defaults and bucket-specific YAML overrides.
*   **`LockComponentRegistry`**: A Spring-managed bean that acts as a central holder for shared, stateless locking services (like `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`), making them available to lock instances.
*   **`LockOwnerSupplier`**: An interface responsible for providing a unique identifier for the current lock owner (typically combining an application instance ID and a thread ID).
*   **`LockSemaphoreHolder`**: A helper class used by the `UnlockMessageListener` to manage waiting threads for a specific lock key. It encapsulates a `java.util.concurrent.Semaphore`.
*   **`LockWatchdog`**: A Spring-managed bean that periodically extends the lease (TTL) of active, application-instance-bound locks in Redis to prevent premature expiration.
*   **Lua Scripts**: Scripts written in the Lua programming language that are executed atomically on the Redis server to perform complex lock operations (e.g., acquire, release, extend).
*   **Non-Polling Wait**: A mechanism where threads waiting for a lock do not continuously check its status. Instead, they wait on a synchronization primitive (like a `Semaphore` in `LockSemaphoreHolder`) that is signaled when the lock is released (via Redis Pub/Sub).
*   **Override Precedence**: The order in which configuration settings are applied: Instance-specific settings (via builder methods) > Bucket-Specific Configuration (YAML) > Global Configuration (YAML/Java defaults).
*   **Pub/Sub (Publish/Subscribe)**: A Redis messaging paradigm used by the `UnlockMessageListener` to receive notifications when locks are released, enabling efficient, non-polling waits.
*   **Reentrant Lock**: A lock that can be acquired multiple times by the same owner (e.g., the same thread) without deadlocking. Each `lock()` call must be matched by an `unlock()` call.
*   **`RedisLockAutoConfiguration`**: The Spring Boot `@AutoConfiguration` class responsible for setting up all the necessary beans for the locking module.
*   **`RedisLockOperations`**: A Spring-managed bean that abstracts direct Redis communication for lock-specific commands, primarily executing Lua scripts.
*   **`RedisLockProperties`**: A Spring `@ConfigurationProperties` class that binds global and bucket-specific settings from YAML files.
*   **Retry Interval (`retryInterval`)**: The duration `AbstractRedisLock` waits after a semaphore wait (which might have timed out based on the current lock holder's TTL) before re-attempting to acquire the lock via Lua script.
*   **`ScriptLoader`**: A Spring-managed bean that loads and caches all Lua scripts from the classpath at application startup.
*   **StateLock (`RedisStateLock`)**: A type of lock that, in addition to standard locking, also manages and potentially gates access based on an associated "state" value stored in Redis.
*   **StampedLock (`RedisStampedLock`)**: A lock type that provides optimistic read locking and pessimistic write locking, using a "stamp" or version number to detect intervening writes.
*   **TTL (Time-To-Live)**: The actual expiration time set on a key in Redis. For locks managed by the watchdog, this is typically the `watchdogMaxTtl`. For locks not managed by the watchdog, this is the `leaseTime`.
*   **`UnlockMessageListener`**: A component (managed per bucket by `UnlockMessageListenerManager`) that subscribes to Redis Pub/Sub channels and signals waiting threads (via `LockSemaphoreHolder`) when an unlock message for a specific lock key is received.
*   **`UnlockMessageListenerManager`**: A Spring-managed bean that creates and manages the lifecycle of `UnlockMessageListener` instances for different lock buckets.
*   **Watchdog Maximum TTL (`watchdogMaxTtl`)**: An internal global configuration property. It's the duration for which the `LockWatchdog` sets/extends the TTL of a Redis lock key during each renewal cycle.
*   **Watchdog Renewal Multiplier (`watchdogRenewalMultiplier`)**: An internal global configuration property used to determine how frequently the watchdog attempts to renew a lock. Renewal Interval = `watchdogMaxTtl / watchdogRenewalMultiplier`.