# Redis Locking Module: Notes for Users (v1.0)

## 1. Introduction

This document highlights key behavioral aspects and design choices of the `locking-redis-lock` module as of its **v1.0 (first official) release**. These notes serve to clarify its functionality, especially for users familiar with other locking libraries or anticipating certain common patterns from previous internal discussions or non-production drafts.

For detailed information on specific aspects, please refer to the respective core documentation:
*   [Architecture Overview](architecture_overview.md)
*   [Configuration](configuration.md)
*   [Lock Acquisition Mechanism](lock_acquisition.md)
*   [Redis Key Schema](redis_key_schema.md)
*   [Exception Handling](exception_handling.md)
*   [Implementation Details](implementation_details.md)
*   [<PERSON>a <PERSON>ripts](lua_scripts_2.md)
*   [Watchdog Mechanism](watchdog.md)
*   [Metrics](metrics.md)

## 2. Key Behavioral and Design Points to Note for v1.0

The `locking-redis-lock` module has been designed with the following key characteristics:

*   **Efficient Lock Acquisition (Non-Polling by De<PERSON>ult)**: The primary mechanism for waiting for a lock is notification-based, utilizing Redis Pub/Sub and client-side semaphores. This significantly reduces Redis load compared to continuous polling. Fallback polling is used if notifications are missed. (See [Lock Acquisition Mechanism](lock_acquisition.md)).
*   **`lock()` Method Blocks Indefinitely**: The standard `lock()` method (with no arguments) will block until the lock is acquired or the calling thread is interrupted. It does **not** use or respect any previously discussed internal default timeout property. For time-bounded acquisition attempts, applications **must** use `tryLock(long timeout, TimeUnit unit)`. (See [Lock Acquisition Mechanism](lock_acquisition.md)).
*   **Distributed Reentrancy via Redis Hash**: Reentrant locks (e.g., `RedisReentrantLock`) manage their reentrancy count and owner information directly within a Redis Hash associated with the lock key. This ensures correct reentrant behavior across distributed application instances. (See [Architecture Overview](architecture_overview.md) and [Implementation Details](implementation_details.md)).
*   **Atomic Operations via Lua Scripts**: All critical lock operations (acquisition, release, lease extension, state manipulation) are performed using Lua scripts executed atomically on the Redis server. This prevents race conditions and reduces network round trips. (See [Lua Scripts](lua_scripts_2.md)).
*   **Programmatic Bucket Configuration**: While global default settings are loaded from YAML into `RedisLockProperties`, the configuration of logical "lock buckets" (for grouping locks and applying common default settings like lease times or watchdog policies) is done **programmatically** through a fluent builder API, starting with `LockBucketRegistry`. Direct YAML-based definition of multiple, named buckets within `RedisLockProperties` is not supported in this version. (See [Configuration](configuration.md)).
*   **Structured Exception Hierarchy**: The module employs a specific exception hierarchy rooted in `AbstractRedisLockException`. These exceptions implement `ExceptionMarkerProvider` for integration with the Destilink Framework's structured logging. (See [Exception Handling](exception_handling.md)).
*   **Comprehensive Metrics**: Detailed operational metrics are exposed via Micrometer for monitoring and observability. (See [Metrics](metrics.md)).

## 3. Notes for Users Familiar with Earlier Drafts or Other Libraries

*   **No Implicit Timeout on `lock()`**: Re-emphasizing, the parameterless `lock()` method blocks indefinitely. Any prior internal drafts or expectations of a `defaultTimeout` property applying to this method are superseded; use `tryLock(long timeout, TimeUnit unit)` for timed attempts.
*   **Programmatic Configuration for Buckets**: Bucket-specific configurations are defined in code using the builder API, not via a map structure in the global YAML properties.
*   **`RedisStandardLock` Concept Deprecated**: Any previous discussions, internal drafts, or concepts referring to a lock type named `RedisStandardLock` should be considered deprecated. **This lock type IS NOT TO BE IMPLEMENTED.** The designated successor for general-purpose, reentrant distributed locking is `RedisReentrantLock`, as detailed in the [Class Hierarchy](class_hierarchy.md).

## 4. Compatibility & Future Migrations

*   **Source Compatibility**: This module aims to maintain source compatibility for the core `java.util.concurrent.Lock` interface methods.
*   **Behavioral Differences**: The non-polling nature and explicit timeout requirements for `lock()` are key behavioral characteristics of this module.

As this is the first release, there are no direct migration steps from a previous production version of *this specific module*. These notes aim to guide users in understanding its current design and behavior.