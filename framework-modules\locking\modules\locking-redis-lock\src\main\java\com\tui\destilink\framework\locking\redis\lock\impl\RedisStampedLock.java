package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.config.LockBucketConfig;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.LockComponentRegistry;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.exception.AbstractRedisLockException;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Redis-based implementation of a stamped lock providing optimistic reading,
 * read locking, and write locking capabilities.
 * <p>
 * This implementation provides a lightweight, stateless lock object with all
 * state
 * managed in Redis. It supports three types of access:
 * </p>
 * <ul>
 * <li>Optimistic read - fast, non-blocking validation</li>
 * <li>Pessimistic read - shared access with multiple readers</li>
 * <li>Write access - exclusive access</li>
 * </ul>
 * <p>
 * All reentrancy and state tracking is handled entirely by Redis using Lua
 * scripts.
 * No ThreadLocal or JVM-local state is maintained for lock tracking.
 * </p>
 * <p>
 * The stamped lock uses a sequence number (stamp) system to validate optimistic
 * reads
 * and coordinate between different lock modes, all managed in Redis.
 * </p>
 */
@Slf4j
public class RedisStampedLock extends AbstractRedisLock {
    private static final String LOCK_TYPE = "RedisStampedLock";
    private static final String STAMPED_DATA_SUFFIX = ":data";
    private static final String WRITE_MODE = "write";

    private final String stampedDataKey;

    /**
     * Creates a new Redis-based stamped lock with the specified key and
     * configuration.
     *
     * @param componentRegistry Central registry for shared lock components
     * @param config            Configuration for the lock bucket
     * @param lockName          The unique name identifying this lock
     * @param namespace         The namespace for the lock (e.g., bucket name)
     * @param properties        Redis lock properties
     */
    public RedisStampedLock(
            LockComponentRegistry componentRegistry,
            LockBucketConfig config,
            String lockName,
            String namespace,
            RedisLockProperties properties) {
        super(
                componentRegistry.getRedisLockOperations(),
                componentRegistry.getDefaultLockOwnerSupplier(),
                properties,
                lockName,
                config.leaseTime().toMillis(),
                config.retryInterval().toMillis(),
                Integer.MAX_VALUE);
        this.stampedDataKey = lockKey + STAMPED_DATA_SUFFIX;
    }

    /**
     * Gets the response cache key for this lock with a timestamp appended.
     * This is used for caching responses to idempotent operations.
     *
     * @return the response cache key with timestamp
     */
    /**
     * Creates a timestamped response cache key for this lock.
     * This is used for caching responses to idempotent operations.
     *
     * @return the response cache key with timestamp
     */
    private String createTimestampedResponseCacheKey() {
        return getResponseCacheKey() + ":" + System.nanoTime();
    }

    @Override
    protected String getLockType() {
        return LOCK_TYPE;
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        // For standard Lock interface, we'll use write lock semantics
        return tryWriteLockInternalAsync(ownerId)
                .thenApply(stamp -> stamp != null);
    }

    @Override
    protected CompletableFuture<Void> doLock(String ownerId, Duration effectiveTimeout) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Long stamp = tryWriteLockInternalAsync(ownerId).get(effectiveTimeout.toMillis(), TimeUnit.MILLISECONDS);
                if (stamp != null) {
                    log.debug("Stamped write lock acquired: lockKey={}, ownerId={}, stamp={}", lockKey, ownerId, stamp);
                    return null;
                }
                throw new LockAcquisitionException(lockKey, getLockType(), lockOwnerSupplier.get(),
                        "Failed to acquire stamped write lock");
            } catch (Exception e) {
                throw new LockAcquisitionException(lockKey, getLockType(), lockOwnerSupplier.get(),
                        "Error acquiring stamped write lock", e);
            }
        });
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        // For standard Lock interface, we'll use write lock unlock semantics
        return unlockWriteInternalAsync(ownerId)
                .thenApply(result -> {
                    log.debug("Stamped write lock released: lockKey={}, ownerId={}, result={}",
                            lockKey, ownerId, result);
                    return null;
                });
    }

    // Using default implementation from AbstractRedisLock for
    // lockInterruptiblyAsync and isLockedAsync

    // StampedLock-specific methods

    /**
     * Attempts to acquire a write lock, returning a stamp if successful.
     * 
     * @return CompletableFuture with stamp if successful, null otherwise
     */
    public CompletableFuture<Long> tryWriteLockAsync() {
        String ownerId = lockOwnerSupplier.get();
        return tryWriteLockInternalAsync(ownerId)
                .exceptionally(ex -> {
                    log.error("Error trying to acquire write lock: lockKey={}, ownerId={}", lockKey, ownerId, ex);
                    return null;
                });
    }

    /**
     * Attempts to acquire a read lock, returning a stamp if successful.
     * 
     * @return CompletableFuture with stamp if successful, null otherwise
     */
    public CompletableFuture<Long> tryReadLockAsync() {
        String ownerId = lockOwnerSupplier.get();
        return tryReadLockInternalAsync(ownerId)
                .exceptionally(ex -> {
                    log.error("Error trying to acquire read lock: lockKey={}, ownerId={}", lockKey, ownerId, ex);
                    return null;
                });
    }

    /**
     * Returns an optimistic read stamp for validation.
     * 
     * @return CompletableFuture with optimistic read stamp
     */
    public CompletableFuture<Long> tryOptimisticReadAsync() {
        // Get current sequence number from Redis for optimistic read validation
        return redisLockOperations.getStateLockState(stampedDataKey)
                .thenApply(data -> {
                    if (data != null) {
                        try {
                            return Long.parseLong(data);
                        } catch (NumberFormatException e) {
                            log.error("Invalid stamped data format: {}", data, e);
                            throw new LockAcquisitionException(lockKey, getLockType(), lockOwnerSupplier.get(),
                                    "Invalid stamped data format", e);
                        }
                    }
                    return 0L;
                });
    }

    /**
     * Custom exception for RedisStampedLock-specific errors.
     */
    public static class RedisStampedLockException extends AbstractRedisLockException {
        public RedisStampedLockException(String lockName, String lockType, String lockOwnerId, String message,
                Throwable cause) {
            super(lockName, lockType, lockOwnerId, null, message, cause);
        }
    }

    /**
     * Validates an optimistic read stamp.
     * 
     * @param stamp The stamp to validate
     * @return CompletableFuture with true if stamp is still valid
     */
    public CompletableFuture<Boolean> validateAsync(long stamp) {
        return tryOptimisticReadAsync()
                .thenApply(currentStamp -> currentStamp == stamp);
    }

    /**
     * Unlocks a write lock using the provided stamp.
     * 
     * @param stamp The stamp from write lock acquisition
     * @return CompletableFuture with operation result
     */
    public CompletableFuture<String> unlockWriteAsync(long stamp) {
        String ownerId = lockOwnerSupplier.get();
        return unlockWriteInternalAsync(ownerId)
                .exceptionally(ex -> {
                    log.error("Error unlocking write lock: lockKey={}, ownerId={}, stamp={}", lockKey, ownerId, stamp,
                            ex);
                    return null;
                });
    }

    /**
     * Unlocks a read lock using the provided stamp.
     * 
     * @param stamp The stamp from read lock acquisition
     * @return CompletableFuture with operation result
     */
    public CompletableFuture<String> unlockReadAsync(long stamp) {
        String ownerId = lockOwnerSupplier.get();
        return unlockReadInternalAsync(ownerId)
                .exceptionally(ex -> {
                    log.error("Error unlocking read lock: lockKey={}, ownerId={}, stamp={}", lockKey, ownerId, stamp,
                            ex);
                    return null;
                });
    }

    // Internal implementation methods

    private CompletableFuture<Long> tryWriteLockInternalAsync(String ownerId) {
        String requestUuid = String.valueOf(System.nanoTime());

        return redisLockOperations.tryWriteLock(
                lockKey,
                createTimestampedResponseCacheKey(),
                requestUuid,
                String.valueOf(lockTtlMillis),
                ownerId,
                String.valueOf(getResponseCacheTtlSeconds().getSeconds()),
                WRITE_MODE);
    }

    private CompletableFuture<Long> tryReadLockInternalAsync(String ownerId) {
        String requestUuid = String.valueOf(System.nanoTime());

        return redisLockOperations.tryReadLock(
                lockKey,
                createTimestampedResponseCacheKey(),
                requestUuid,
                String.valueOf(lockTtlMillis),
                ownerId,
                String.valueOf(getResponseCacheTtlSeconds().getSeconds()));
    }

    private CompletableFuture<String> unlockWriteInternalAsync(String ownerId) {
        String requestUuid = String.valueOf(System.nanoTime());

        return redisLockOperations.unlockWriteLock(
                lockKey,
                ownerId,
                requestUuid,
                "UNLOCK");
    }

    private CompletableFuture<String> unlockReadInternalAsync(String ownerId) {
        String requestUuid = String.valueOf(System.nanoTime());

        return redisLockOperations.unlockReadLock(
                lockKey,
                ownerId,
                requestUuid);
    }

    @Override
    protected CompletableFuture<Void> updateLockState(String ownerId) {
        return CompletableFuture.allOf(
                redisLockOperations.hset(getLockKey(), "mode", "WRITE").thenApply(result -> null),
                redisLockOperations.hset(getLockKey(), "owner", ownerId).thenApply(result -> null));
    }

    @Override
    public boolean isReadLock() {
        return false; // RedisStampedLock is neither purely read nor write
    }

    @Override
    public boolean isWriteLock() {
        return false; // RedisStampedLock is neither purely read nor write
    }
}
