/**
 * Updates the project documentation with the latest module dependency graph
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  mermaidFile: path.resolve(__dirname, 'output/module-dependencies.mmd'),
  docsDir: path.resolve(__dirname, '../../docs'),
  targetFile: path.resolve(__dirname, '../../docs/overview.md')
};

function updateDocs() {
  console.log('Updating documentation with module dependency graph...');
  
  // Check if the mermaid file exists
  if (!fs.existsSync(config.mermaidFile)) {
    console.error('Mermaid file not found. Run module_visualizer.js first.');
    process.exit(1);
  }
  
  // Read the mermaid content
  const mermaidContent = fs.readFileSync(config.mermaidFile, 'utf8');
  
  // Read the current docs file
  let docsContent = '';
  if (fs.existsSync(config.targetFile)) {
    docsContent = fs.readFileSync(config.targetFile, 'utf8');
  }
  
  // Check if the docs already contain a module dependency section
  const moduleDepSection = '## Module Dependencies';
  const mermaidStart = '```mermaid';
  const mermaidEnd = '```';
  
  if (docsContent.includes(moduleDepSection)) {
    // Update existing section
    const sectionStart = docsContent.indexOf(moduleDepSection);
    const diagramStart = docsContent.indexOf(mermaidStart, sectionStart);
    const diagramEnd = docsContent.indexOf(mermaidEnd, diagramStart) + mermaidEnd.length;
    
    const newDocsContent = 
      docsContent.substring(0, diagramStart + mermaidStart.length) + 
      '\n' + mermaidContent + '\n' + 
      docsContent.substring(diagramEnd);
    
    fs.writeFileSync(config.targetFile, newDocsContent);
    console.log('Updated existing module dependency section in docs.');
  } else {
    // Add new section
    const newSection = `
${moduleDepSection}

This diagram shows the dependencies between framework modules:

${mermaidStart}
${mermaidContent}
${mermaidEnd}

_This diagram is automatically generated by the module-visualizer tool._
`;
    
    // Append to the end of the file or create a new file
    if (docsContent) {
      fs.writeFileSync(config.targetFile, docsContent + '\n\n' + newSection);
    } else {
      fs.writeFileSync(config.targetFile, `# Framework Overview\n\n${newSection}`);
    }
    
    console.log('Added new module dependency section to docs.');
  }
  
  console.log(`Documentation updated at: ${config.targetFile}`);
}

// Run the update
updateDocs();