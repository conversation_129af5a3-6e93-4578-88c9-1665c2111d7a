/**
 * Destilink Framework Configuration Validator
 * Validates YAML configuration files against framework standards
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  rootDir: path.resolve(__dirname, '../..'),
  outputDir: path.resolve(__dirname, 'output'),
  ignorePatterns: ['node_modules', 'target', '.git', 'bak', 'temp'],
  propertyPrefix: 'destilink.fw'
};

// Find all YAML files
function findYamlFiles(dir) {
  const files = [];
  
  function scan(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        if (config.ignorePatterns.some(pattern => item.includes(pattern))) {
          continue;
        }
        
        const itemPath = path.join(currentDir, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
          scan(itemPath);
        } else if (stats.isFile() && (itemPath.endsWith('.yml') || itemPath.endsWith('.yaml'))) {
          files.push(itemPath);
        }
      }
    } catch (error) {
      console.error(`Error scanning ${currentDir}: ${error.message}`);
    }
  }
  
  scan(dir);
  return files;
}

// Validate a YAML file
function validateYamlFile(filePath) {
  const issues = [];
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Simple validation without full YAML parsing
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments and empty lines
      if (line.trim().startsWith('#') || !line.trim()) {
        continue;
      }
      
      // Check for framework properties with wrong prefix
      if (line.includes('destilink.') && !line.includes(config.propertyPrefix)) {
        issues.push({
          file: filePath.replace(config.rootDir, ''),
          line: i + 1,
          type: 'warning',
          message: `Property should use the standard prefix '${config.propertyPrefix}'`
        });
      }
      
      // Check for snake_case instead of kebab-case
      if (line.includes(config.propertyPrefix) && line.includes('_')) {
        issues.push({
          file: filePath.replace(config.rootDir, ''),
          line: i + 1,
          type: 'warning',
          message: 'Property should use kebab-case instead of snake_case'
        });
      }
      
      // Check for boolean values as strings
      if ((line.includes(': "true"') || line.includes(': "false"')) && 
          line.includes(config.propertyPrefix)) {
        issues.push({
          file: filePath.replace(config.rootDir, ''),
          line: i + 1,
          type: 'warning',
          message: 'Boolean value should not be quoted'
        });
      }
    }
    
  } catch (error) {
    issues.push({
      file: filePath.replace(config.rootDir, ''),
      line: 0,
      type: 'error',
      message: `Failed to read file: ${error.message}`
    });
  }
  
  return issues;
}

// Generate report
function generateReport(issues) {
  const errorCount = issues.filter(i => i.type === 'error').length;
  const warningCount = issues.filter(i => i.type === 'warning').length;
  
  const report = {
    summary: {
      totalIssues: issues.length,
      errorCount,
      warningCount,
      timestamp: new Date().toISOString()
    },
    issues: issues.sort((a, b) => {
      // Sort by file first
      if (a.file !== b.file) {
        return a.file.localeCompare(b.file);
      }
      // Then by line number
      return a.line - b.line;
    })
  };
  
  return report;
}

// Format report as text
function formatReportText(report) {
  let text = `
DESTILINK FRAMEWORK CONFIGURATION VALIDATION REPORT
==================================================
Generated: ${report.summary.timestamp}

SUMMARY:
  Total Issues: ${report.summary.totalIssues}
  Errors:       ${report.summary.errorCount}
  Warnings:     ${report.summary.warningCount}

`;

  if (report.issues.length === 0) {
    text += 'No issues found. All configuration files meet framework standards.\n';
    return text;
  }

  text += 'ISSUES:\n';
  
  let currentFile = null;
  for (const issue of report.issues) {
    if (currentFile !== issue.file) {
      currentFile = issue.file;
      text += `\nFile: ${currentFile}\n`;
      text += '----------------------------------------\n';
    }
    
    const type = issue.type.toUpperCase();
    const line = issue.line > 0 ? `Line ${issue.line}: ` : '';
    text += `  [${type}] ${line}${issue.message}\n`;
  }
  
  return text;
}

// Main function
function main() {
  console.log('Destilink Framework Configuration Validator');
  console.log('==========================================');
  
  // Find YAML files
  console.log('Scanning for YAML files...');
  const yamlFiles = findYamlFiles(config.rootDir);
  console.log(`Found ${yamlFiles.length} YAML files`);
  
  // Validate files
  console.log('Validating configuration files...');
  let allIssues = [];
  for (const file of yamlFiles) {
    const issues = validateYamlFile(file);
    allIssues = allIssues.concat(issues);
  }
  
  // Generate report
  const report = generateReport(allIssues);
  const reportText = formatReportText(report);
  
  // Output report
  const reportPath = path.join(config.outputDir, `config-validation-${new Date().toISOString().replace(/:/g, '-')}.txt`);
  fs.writeFileSync(reportPath, reportText);
  
  console.log(reportText);
  console.log(`Report saved to: ${reportPath}`);
  
  // Exit with error code if errors found
  if (report.summary.errorCount > 0) {
    console.error('ERRORS FOUND! Fix them before proceeding.');
    process.exit(1);
  }
}

// Run the program
if (require.main === module) {
  main();
}

module.exports = {
  validateYamlFile,
  findYamlFiles,
  generateReport
};