# Redis Locking Module: Modernization Plan

This diagram illustrates the Modernization Plan for the `locking-redis-lock` module.

```mermaid
flowchart TB
    %% Current State
    subgraph "Current State"
        A1["Legacy Component Loading"]
        A2["Mixed Dependency Injection"]
        A3["Inconsistent Error Handling"]
        A4["Limited Metrics"]
        A5["Manual Configuration"]
        A6["Unstructured Logging"]
        A7["Incomplete Testing"]
    end
    
    %% Target State
    subgraph "Target State"
        B1["Explicit Component Loading\n@Import and @Bean"]
        B2["Constructor Injection Only"]
        B3["Standardized Error Handling\nMarkerNestedRuntimeException"]
        B4["Comprehensive Metrics\nMicrometer Integration"]
        B5["Auto-Configuration\nConditional Activation"]
        B6["Structured Logging\nMDC Context"]
        B7["Comprehensive Testing\nTest Support Modules"]
    end
    
    %% Phases
    subgraph "Phase 1: Foundation"
        P1_1["Migrate to @AutoConfiguration"]
        P1_2["Implement Explicit @Import"]
        P1_3["Refactor to Constructor Injection"]
        P1_4["Add Conditional Activation"]
    end
    
    subgraph "Phase 2: Operational Excellence"
        P2_1["Enhance Metrics Collection"]
        P2_2["Implement Structured Logging"]
        P2_3["Standardize Error Handling"]
        P2_4["Add Health Indicators"]
    end
    
    subgraph "Phase 3: Testing & Documentation"
        P3_1["Create Test Support Module"]
        P3_2["Migrate Tests to Test Support"]
        P3_3["Enhance Documentation"]
        P3_4["Create Architecture Diagrams"]
    end
    
    %% Relationships
    A1 --> P1_1
    A1 --> P1_2
    A2 --> P1_3
    A5 --> P1_4
    
    A4 --> P2_1
    A6 --> P2_2
    A3 --> P2_3
    A4 --> P2_4
    
    A7 --> P3_1
    A7 --> P3_2
    A7 --> P3_3
    A7 --> P3_4
    
    P1_1 --> B1
    P1_2 --> B1
    P1_3 --> B2
    P1_4 --> B5
    
    P2_1 --> B4
    P2_2 --> B6
    P2_3 --> B3
    P2_4 --> B4
    
    P3_1 --> B7
    P3_2 --> B7
    P3_3 --> B5
    P3_4 --> B5
    
    %% Timeline
    classDef phase1 fill:#d4f1f9,stroke:#05386b,stroke-width:1px
    classDef phase2 fill:#c8e6c9,stroke:#05386b,stroke-width:1px
    classDef phase3 fill:#ffe0b2,stroke:#05386b,stroke-width:1px
    
    class P1_1,P1_2,P1_3,P1_4 phase1
    class P2_1,P2_2,P2_3,P2_4 phase2
    class P3_1,P3_2,P3_3,P3_4 phase3
```

## Modernization Overview

The Redis Locking module requires modernization to align with the Destilink Framework guidelines and best practices. This plan outlines the current state, target state, and phased approach to modernization.

### Current State Assessment

The current implementation of the Redis Locking module has several areas that need improvement:

1. **Legacy Component Loading**: The module uses a mix of component scanning and explicit bean definitions, which violates the Destilink Framework guidelines.

2. **Mixed Dependency Injection**: The module uses a combination of constructor injection, field injection, and setter injection, which makes the code harder to understand and test.

3. **Inconsistent Error Handling**: The module has inconsistent error handling patterns, with some exceptions extending RuntimeException directly and others using custom base classes.

4. **Limited Metrics**: The module has basic metrics collection but lacks comprehensive monitoring capabilities.

5. **Manual Configuration**: The module requires manual configuration of many components, with limited use of auto-configuration.

6. **Unstructured Logging**: The module uses basic logging without structured context or standardized markers.

7. **Incomplete Testing**: The module has some tests but lacks comprehensive test coverage and doesn't use test support modules.

### Target State Vision

The target state for the Redis Locking module aligns with the Destilink Framework guidelines:

1. **Explicit Component Loading**: All components will be explicitly loaded using `@Import` and `@Bean` definitions, with no component scanning.

2. **Constructor Injection Only**: All dependencies will be injected via constructors, with no field or setter injection.

3. **Standardized Error Handling**: All exceptions will extend `MarkerNestedRuntimeException` or `MarkerNestedException` for consistent error handling and logging.

4. **Comprehensive Metrics**: The module will have comprehensive metrics collection using Micrometer, with health indicators and dashboards.

5. **Auto-Configuration**: The module will use Spring Boot auto-configuration with conditional activation for all components.

6. **Structured Logging**: The module will use structured logging with MDC context and standardized markers.

7. **Comprehensive Testing**: The module will have comprehensive test coverage using test support modules.

## Modernization Phases

### Phase 1: Foundation

The first phase focuses on the foundational changes required to align with the Destilink Framework guidelines:

#### 1.1 Migrate to @AutoConfiguration

Replace the current configuration classes with proper `@AutoConfiguration` classes:

```java
/**
 * Auto-configuration for Redis Lock functionality.
 * <p>
 * Provides distributed locking capabilities using Redis as the backing store.
 * Supports various lock types including reentrant locks, read-write locks,
 * stamped locks, and state locks.
 * </p>
 */
@AutoConfiguration
@EnableConfigurationProperties(RedisLockProperties.class)
@ConditionalOnProperty(
    prefix = "destilink.fw.locking.redis",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true)
public class RedisLockAutoConfiguration {
    // Configuration content
}
```

#### 1.2 Implement Explicit @Import

Replace component scanning with explicit `@Import` statements:

```java
@Configuration
@Import({
    // Core services
    RedisLockOperations.class,
    ScriptLoader.class,
    
    // Lock implementations
    RedisReentrantLock.class,
    RedisReadWriteLock.class,
    RedisStampedLock.class,
    RedisStateLock.class,
    
    // Supporting services
    LockWatchdog.class,
    UnlockMessageListenerManager.class,
    LockComponentRegistry.class
})
public class RedisLockComponentConfiguration {
    // Additional @Bean definitions
}
```

#### 1.3 Refactor to Constructor Injection

Replace field and setter injection with constructor injection:

```java
@Slf4j
public class RedisLockOperations {
    private final RedisTemplate<String, String> redisTemplate;
    private final ScriptLoader scriptLoader;
    private final ObjectMapper objectMapper;
    private final RedisLockProperties properties;
    private final LockMonitor lockMonitor;
    
    public RedisLockOperations(
            RedisTemplate<String, String> redisTemplate,
            ScriptLoader scriptLoader,
            ObjectMapper objectMapper,
            RedisLockProperties properties,
            LockMonitor lockMonitor) {
        this.redisTemplate = redisTemplate;
        this.scriptLoader = scriptLoader;
        this.objectMapper = objectMapper;
        this.properties = properties;
        this.lockMonitor = lockMonitor;
    }
    
    // Implementation
}
```

#### 1.4 Add Conditional Activation

Add conditional activation for all beans:

```java
@Bean
@ConditionalOnMissingBean
@ConditionalOnProperty(
    prefix = "destilink.fw.locking.redis",
    name = "watchdog-enabled",
    havingValue = "true",
    matchIfMissing = true)
public LockWatchdog lockWatchdog(
        RedisLockOperations lockOperations,
        RedisLockProperties properties,
        LockMonitor lockMonitor,
        RedisLockErrorHandler errorHandler) {
    return new LockWatchdog(lockOperations, properties, lockMonitor, errorHandler);
}
```

### Phase 2: Operational Excellence

The second phase focuses on operational excellence improvements:

#### 2.1 Enhance Metrics Collection

Implement comprehensive metrics collection using Micrometer:

```java
@Slf4j
public class LockMonitor {
    private final MeterRegistry meterRegistry;
    
    public LockMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    public void recordLockAcquisitionAttempt(String lockKey) {
        meterRegistry.counter("redis.lock.acquisition.attempt", createTag(lockKey))
            .increment();
    }
    
    public void recordLockAcquisitionSuccess(String lockKey, long durationMs) {
        meterRegistry.counter("redis.lock.acquisition.success", createTag(lockKey))
            .increment();
        
        meterRegistry.timer("redis.lock.acquisition.time", createTag(lockKey))
            .record(durationMs, TimeUnit.MILLISECONDS);
    }
    
    // Additional metrics methods
}
```

#### 2.2 Implement Structured Logging

Implement structured logging with MDC context:

```java
public class LockContextDecorator extends AbstractContextDecorator<LockContextDecorator> {
    private static final LockContextDecorator INSTANCE = new LockContextDecorator();
    
    public static LockContextDecorator getInstance() {
        return INSTANCE;
    }
    
    public LockContextDecorator withLockKey(String lockKey) {
        return put("lockcontext.key", lockKey);
    }
    
    public LockContextDecorator withLockType(String lockType) {
        return put("lockcontext.type", lockType);
    }
    
    public LockContextDecorator withOwnerId(String ownerId) {
        return put("lockcontext.owner", ownerId);
    }
    
    // Additional context methods
}
```

Usage example:

```java
try (var scope = LockContextDecorator.getInstance()
        .withLockKey(lockKey)
        .withLockType("reentrant")
        .withOwnerId(ownerId)
        .createClosableScope()) {
    
    log.info("Attempting to acquire lock");
    // Lock acquisition logic
    log.info("Lock acquired successfully");
}
```

#### 2.3 Standardize Error Handling

Standardize error handling with `MarkerNestedRuntimeException`:

```java
public class LockAcquisitionException extends MarkerNestedRuntimeException {
    private final String lockKey;
    private final String ownerId;
    
    public LockAcquisitionException(String message, String lockKey, String ownerId) {
        super(message);
        this.lockKey = lockKey;
        this.ownerId = ownerId;
    }
    
    public LockAcquisitionException(String message, Throwable cause, String lockKey, String ownerId) {
        super(message, cause);
        this.lockKey = lockKey;
        this.ownerId = ownerId;
    }
    
    @Override
    protected Marker getMarker() {
        return Markers.append("lockKey", lockKey)
            .and(Markers.append("ownerId", ownerId));
    }
}
```

#### 2.4 Add Health Indicators

Implement health indicators for monitoring:

```java
@Slf4j
public class RedisLockHealthIndicator implements HealthIndicator {
    private final RedisConnectionFactory connectionFactory;
    private final RedisLockProperties properties;
    
    public RedisLockHealthIndicator(RedisConnectionFactory connectionFactory,
                                   RedisLockProperties properties) {
        this.connectionFactory = connectionFactory;
        this.properties = properties;
    }
    
    @Override
    public Health health() {
        try {
            // Check Redis connection
            RedisConnection connection = connectionFactory.getConnection();
            try {
                String pong = connection.ping();
                if (!"PONG".equals(pong)) {
                    return Health.down()
                        .withDetail("ping", "Failed to get PONG response")
                        .build();
                }
            } finally {
                connection.close();
            }
            
            // Additional health checks
            
            return Health.up()
                .withDetail("ping", "PONG")
                .build();
        } catch (Exception e) {
            log.error("Redis connection check failed", e);
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

### Phase 3: Testing & Documentation

The third phase focuses on testing and documentation improvements:

#### 3.1 Create Test Support Module

Create a dedicated test support module for Redis locking:

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@TestSupport
@Import(RedisLockTestConfiguration.class)
public @interface RedisLockTestSupport {
    String host() default "localhost";
    int port() default 6379;
    boolean cleanupAfterTest() default true;
}
```

#### 3.2 Migrate Tests to Test Support

Migrate existing tests to use the test support module:

```java
@RedisLockTestSupport
@SpringBootTest
public class RedisReentrantLockIT {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RedisLockFactory lockFactory;
    
    @Test
    public void testLockUnlock() {
        // Test implementation using test support
    }
}
```

#### 3.3 Enhance Documentation

Enhance documentation with comprehensive JavaDoc:

```java
/**
 * Factory for creating Redis-based distributed locks.
 * <p>
 * This factory provides methods to create various types of distributed locks
 * backed by Redis:
 * <ul>
 *   <li>Reentrant locks - Standard exclusive locks that can be acquired multiple times by the same thread</li>
 *   <li>Read-write locks - Locks that allow multiple readers but only one writer</li>
 *   <li>Stamped locks - Locks that support optimistic read locking</li>
 *   <li>State locks - Locks that maintain state information</li>
 * </ul>
 * </p>
 * <p>
 * All locks created by this factory are registered with the lock component registry
 * for proper lifecycle management, including watchdog lease extension and unlock
 * notification.
 * </p>
 *
 * @see RedisReentrantLock
 * @see RedisReadWriteLock
 * @see RedisStampedLock
 * @see RedisStateLock
 */
@Slf4j
public class RedisLockFactory {
    // Implementation
}
```

#### 3.4 Create Architecture Diagrams

Create comprehensive architecture diagrams for the module:

- Architecture overview diagram
- Lock implementations diagram
- Redis operations diagram
- Supporting services diagram
- Exception handling diagram
- Configuration diagram
- Lock watchdog diagram
- Unlock notification diagram
- Metrics and monitoring diagram
- Modernization plan diagram

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-2)

| Task | Description                       | Effort | Dependencies |
| ---- | --------------------------------- | ------ | ------------ |
| 1.1  | Migrate to @AutoConfiguration     | 3 days | None         |
| 1.2  | Implement Explicit @Import        | 2 days | 1.1          |
| 1.3  | Refactor to Constructor Injection | 3 days | None         |
| 1.4  | Add Conditional Activation        | 2 days | 1.1, 1.2     |

### Phase 2: Operational Excellence (Weeks 3-4)

| Task | Description                  | Effort | Dependencies |
| ---- | ---------------------------- | ------ | ------------ |
| 2.1  | Enhance Metrics Collection   | 3 days | Phase 1      |
| 2.2  | Implement Structured Logging | 3 days | Phase 1      |
| 2.3  | Standardize Error Handling   | 2 days | Phase 1      |
| 2.4  | Add Health Indicators        | 2 days | 2.1          |

### Phase 3: Testing & Documentation (Weeks 5-6)

| Task | Description                   | Effort | Dependencies |
| ---- | ----------------------------- | ------ | ------------ |
| 3.1  | Create Test Support Module    | 3 days | Phase 2      |
| 3.2  | Migrate Tests to Test Support | 3 days | 3.1          |
| 3.3  | Enhance Documentation         | 2 days | Phase 2      |
| 3.4  | Create Architecture Diagrams  | 2 days | 3.3          |

## Risk Assessment

### High-Risk Areas

1. **Component Loading Migration**: Migrating from component scanning to explicit imports may cause runtime issues if components are missed.
   - **Mitigation**: Comprehensive inventory of all components and thorough testing.

2. **Dependency Injection Refactoring**: Changing injection patterns may introduce subtle bugs.
   - **Mitigation**: Incremental changes with thorough testing after each change.

3. **Error Handling Standardization**: Changing exception hierarchy may affect error handling in client code.
   - **Mitigation**: Maintain backward compatibility where possible and document breaking changes.

### Medium-Risk Areas

1. **Metrics Integration**: Adding comprehensive metrics may impact performance.
   - **Mitigation**: Performance testing before and after changes.

2. **Auto-Configuration**: Conditional activation may cause unexpected behavior in certain environments.
   - **Mitigation**: Test in multiple environments with different configurations.

### Low-Risk Areas

1. **Documentation Enhancement**: Low risk but time-consuming.
   - **Mitigation**: Prioritize critical documentation first.

2. **Architecture Diagrams**: Low risk but requires accurate understanding of the system.
   - **Mitigation**: Review diagrams with team members.

## Expected Benefits

### Immediate Benefits

1. **Improved Maintainability**: Explicit component loading and constructor injection make the code easier to understand and maintain.

2. **Better Operational Visibility**: Enhanced metrics and health indicators provide better visibility into the system's operation.

3. **Consistent Error Handling**: Standardized error handling makes it easier to diagnose and resolve issues.

### Long-Term Benefits

1. **Reduced Technical Debt**: Alignment with Destilink Framework guidelines reduces technical debt.

2. **Easier Upgrades**: Proper auto-configuration and conditional activation make it easier to upgrade dependencies.

3. **Better Testing**: Comprehensive test support makes it easier to write and maintain tests.

4. **Improved Documentation**: Comprehensive documentation makes it easier for new team members to understand the system.

## Success Criteria

The modernization will be considered successful when:

1. All components are explicitly loaded with `@Import` or `@Bean` definitions.
2. All dependencies are injected via constructors.
3. All exceptions extend `MarkerNestedRuntimeException` or `MarkerNestedException`.
4. Comprehensive metrics are collected and exposed via Micrometer.
5. All components are conditionally activated with appropriate annotations.
6. Structured logging is used throughout the module.
7. Comprehensive tests are in place using test support modules.
8. Documentation is comprehensive and up-to-date.
9. Architecture diagrams accurately represent the system.
10. All tests pass and the module functions correctly in all supported environments.

## Conclusion

The modernization plan for the Redis Locking module provides a clear path to align with the Destilink Framework guidelines and best practices. By following this plan, the module will become more maintainable, testable, and operationally excellent.