# Redis Locking Module: Supporting Services

This diagram illustrates the supporting services in the `locking-redis-lock` module and their relationships.

```mermaid
classDiagram
    %% Core Services
    class LockComponentRegistry {
        -components: Map~Class, Object~
        +register~T~(component: T): void
        +getComponent~T~(componentClass: Class~T~): T
        +hasComponent(componentClass: Class): boolean
    }
    
    class ScriptLoader {
        -redisTemplate: RedisTemplate
        -scriptCache: Map~String, String~
        +loadScript(scriptName: String): String
        +getScriptSha(scriptName: String): String
        +executeScript~T~(scriptName: String, keys: List~String~, args: List~Object~): CompletableFuture~T~
    }
    
    class ClusterCommandExecutor {
        -redisTemplate: RedisTemplate
        -redisConnectionFactory: RedisConnectionFactory
        +executeCommand~T~(key: String, command: Function): CompletableFuture~T~
        +executeScriptCommand~T~(keys: List~String~, scriptSha: String, args: List~Object~): CompletableFuture~T~
        +executeMultiKeyCommand~T~(keys: List~String~, command: Function): CompletableFuture~T~
    }
    
    class RedisLockOperations {
        -scriptLoader: ScriptLoader
        -clusterCommandExecutor: ClusterCommandExecutor
        -lockErrorHandler: RedisLockErrorHandler
        -unlockMessageListenerManager: UnlockMessageListenerManager
        +tryLock(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlock(lockKey: String, ownerId: String): CompletableFuture~Boolean~
        +isLocked(lockKey: String): CompletableFuture~Boolean~
        +getLockOwner(lockKey: String): CompletableFuture~String~
        +tryReadLock(lockKey: String, readerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlockReadLock(lockKey: String, readerId: String): CompletableFuture~Boolean~
        +tryWriteLock(lockKey: String, writerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +unlockWriteLock(lockKey: String, writerId: String): CompletableFuture~Boolean~
        +tryStampedLock(lockKey: String, ownerId: String, stampType: String, leaseTimeMs: long): CompletableFuture~String~
        +validateStamp(lockKey: String, stamp: String): CompletableFuture~Boolean~
        +unlockStampedLock(lockKey: String, stamp: String): CompletableFuture~Boolean~
        +tryStateLock(lockKey: String, ownerId: String, stateKey: String, expectedState: String, newState: String, leaseTimeMs: long): CompletableFuture~Boolean~
        +getState(lockKey: String, stateKey: String): CompletableFuture~String~
    }
    
    %% Non-Blocking Wait Services
    class LockSemaphoreHolder {
        -semaphores: Map~String, Semaphore~
        +getSemaphore(lockKey: String): Semaphore
        +releaseSemaphore(lockKey: String): void
        +notifyUnlock(lockKey: String): void
    }
    
    class UnlockMessageListenerManager {
        -redisTemplate: RedisTemplate
        -listenerContainers: Map~String, RedisMessageListenerContainer~
        -messageListeners: Map~String, UnlockMessageListener~
        +registerListener(channelName: String, bucket: String): UnlockMessageListener
        +unregisterListener(channelName: String, bucket: String): void
        +publishUnlockMessage(channelName: String, lockKey: String, unlockType: UnlockType): void
    }
    
    class UnlockMessageListener {
        -lockSemaphoreHolder: LockSemaphoreHolder
        +onMessage(message: Message, pattern: byte[]): void
        +getChannelName(): String
    }
    
    %% Lease Extension
    class LockWatchdog {
        -redisLockOperations: RedisLockOperations
        -lockMonitor: LockMonitor
        -locks: Map~String, LockEntry~
        -scheduledExecutor: ScheduledExecutorService
        +registerLock(lockKey: String, ownerId: String, leaseTimeMs: long): void
        +unregisterLock(lockKey: String, ownerId: String): void
        +refreshLease(lockKey: String, ownerId: String, leaseTimeMs: long): CompletableFuture~Boolean~
        -scheduleRefresh(): void
    }
    
    class LockEntry {
        -lockKey: String
        -ownerId: String
        -leaseTimeMs: long
        -lastRefreshTime: long
    }
    
    %% Error Handling
    class RedisLockErrorHandler {
        +translateException(ex: Throwable, lockKey: String, ownerId: String): AbstractRedisLockException
        +translateScriptException(ex: Throwable, scriptName: String, keys: List, args: List): AbstractRedisLockException
        +enrichException(ex: AbstractRedisLockException, details: Map): AbstractRedisLockException
    }
    
    %% Monitoring
    class LockMonitor {
        -meterRegistry: MeterRegistry
        +recordLockAcquisitionAttempt(lockKey: String, success: boolean, durationMs: long): void
        +recordLockRelease(lockKey: String, durationMs: long): void
        +recordLeaseRefresh(lockKey: String, success: boolean): void
        +recordLockTimeout(lockKey: String): void
        +recordLockError(lockKey: String, errorType: String): void
    }
    
    %% Owner Identification
    class DefaultLockOwnerSupplier {
        +get(): String
        -generateClientId(): String
    }
    
    interface LockOwnerSupplier {
        <<interface>>
        +get(): String
    }
    
    %% Factory
    class RedisLockFactory {
        -redisLockOperations: RedisLockOperations
        -lockComponentRegistry: LockComponentRegistry
        -lockOwnerSupplier: LockOwnerSupplier
        -redisLockProperties: RedisLockProperties
        +createReentrantLock(name: String): RedisReentrantLock
        +createReentrantLock(name: String, bucket: String): RedisReentrantLock
        +createReentrantLock(name: String, bucket: String, config: LockBucketConfig): RedisReentrantLock
        +createReadWriteLock(name: String): RedisReadWriteLock
        +createReadWriteLock(name: String, bucket: String): RedisReadWriteLock
        +createReadWriteLock(name: String, bucket: String, config: LockBucketConfig): RedisReadWriteLock
        +createStampedLock(name: String): RedisStampedLock
        +createStampedLock(name: String, bucket: String): RedisStampedLock
        +createStampedLock(name: String, bucket: String, config: LockBucketConfig): RedisStampedLock
        +createStateLock~T~(name: String, stateKey: String, stateType: Class~T~, serializer: Function, deserializer: Function): RedisStateLock~T~
        +createStateLock~T~(name: String, bucket: String, stateKey: String, stateType: Class~T~, serializer: Function, deserializer: Function): RedisStateLock~T~
        +createStateLock~T~(name: String, bucket: String, config: LockBucketConfig, stateKey: String, stateType: Class~T~, serializer: Function, deserializer: Function): RedisStateLock~T~
    }
    
    %% Configuration
    class RedisLockProperties {
        +enabled: boolean = true
        +prefix: String = "lock"
        +defaultBucket: LockBucketConfig
        +buckets: Map~String, LockBucketConfig~
        +getEffectiveBucketConfig(bucketName: String): LockBucketConfig
        +getDefaultBucketConfig(): LockBucketConfig
    }
    
    class LockBucketConfig {
        +lockTimeoutMs: long = 10000
        +lockLeaseTimeMs: long = 30000
        +lockWatchdogEnabled: boolean = true
        +lockWatchdogIntervalMs: long = 10000
        +retryCount: int = 3
        +retryDelayMs: long = 200
        +nonBlockingTimeoutMs: long = 0
        +pubSubEnabled: boolean = true
        +pubSubChannelNamePrefix: String = "unlock_channel"
        +metricsEnabled: boolean = true
        +hashTagsEnabled: boolean = true
        +mergeWith(other: LockBucketConfig): LockBucketConfig
    }
    
    %% Auto-Configuration
    class RedisLockAutoConfiguration {
        <<@AutoConfiguration>>
        +redisLockProperties(): RedisLockProperties
        +redisLockErrorHandler(): RedisLockErrorHandler
        +defaultLockOwnerSupplier(): DefaultLockOwnerSupplier
        +lockMonitor(): LockMonitor
    }
    
    %% Relationships
    LockComponentRegistry --> ScriptLoader : registers
    LockComponentRegistry --> ClusterCommandExecutor : registers
    LockComponentRegistry --> RedisLockOperations : registers
    LockComponentRegistry --> LockSemaphoreHolder : registers
    LockComponentRegistry --> UnlockMessageListenerManager : registers
    LockComponentRegistry --> LockWatchdog : registers
    LockComponentRegistry --> RedisLockErrorHandler : registers
    LockComponentRegistry --> LockMonitor : registers
    
    RedisLockOperations --> ScriptLoader : uses
    RedisLockOperations --> ClusterCommandExecutor : uses
    RedisLockOperations --> RedisLockErrorHandler : uses
    RedisLockOperations --> UnlockMessageListenerManager : uses
    
    UnlockMessageListenerManager --> UnlockMessageListener : manages
    UnlockMessageListener --> LockSemaphoreHolder : notifies
    
    LockWatchdog --> RedisLockOperations : refreshes locks via
    LockWatchdog --> LockMonitor : reports metrics to
    LockWatchdog *-- LockEntry : contains
    
    RedisLockFactory --> RedisLockOperations : uses
    RedisLockFactory --> LockComponentRegistry : uses
    RedisLockFactory --> LockOwnerSupplier : uses
    RedisLockFactory --> RedisLockProperties : uses
    
    LockOwnerSupplier <|.. DefaultLockOwnerSupplier : implements
    
    RedisLockAutoConfiguration --> RedisLockProperties : configures
    RedisLockAutoConfiguration --> RedisLockErrorHandler : creates
    RedisLockAutoConfiguration --> DefaultLockOwnerSupplier : creates
    RedisLockAutoConfiguration --> LockMonitor : creates
    
    %% Notes
    note for LockComponentRegistry "Central registry for all lock-related components"
    note for RedisLockOperations "Core service for Redis lock operations"
    note for LockWatchdog "Automatically extends lock leases"
    note for UnlockMessageListenerManager "Manages Redis Pub/Sub for non-blocking wait"
    note for RedisLockFactory "Factory for creating lock instances"
```

## Service Responsibilities

### 1. Component Management

#### LockComponentRegistry

The `LockComponentRegistry` serves as a central registry for all lock-related components, providing:

- **Component Registration**: Registers all lock-related services in a central location
- **Component Retrieval**: Provides type-safe access to registered components
- **Dependency Management**: Simplifies dependency management between components
- **Lifecycle Management**: Ensures components are properly initialized and available

```java
// Example usage
lockComponentRegistry.register(scriptLoader);
lockComponentRegistry.register(redisLockOperations);

ScriptLoader scriptLoader = lockComponentRegistry.getComponent(ScriptLoader.class);
```

### 2. Redis Command Execution

#### ScriptLoader

The `ScriptLoader` manages Lua scripts for atomic Redis operations:

- **Script Loading**: Loads Lua scripts from classpath resources
- **Script Caching**: Caches script SHA1 hashes for efficient execution
- **Script Execution**: Executes scripts with proper error handling
- **Script Reloading**: Handles script reloading if Redis cluster is restarted

#### ClusterCommandExecutor

The `ClusterCommandExecutor` handles Redis Cluster command routing:

- **Command Routing**: Routes commands to the correct Redis Cluster node
- **Script Execution**: Executes Lua scripts across the cluster
- **Multi-Key Operations**: Handles operations that span multiple keys
- **Error Handling**: Translates Redis errors to appropriate exceptions
- **Retry Logic**: Handles cluster topology changes and retries

### 3. Non-Blocking Wait

#### LockSemaphoreHolder

The `LockSemaphoreHolder` manages semaphores for non-blocking wait:

- **Semaphore Management**: Creates and manages semaphores for each lock key
- **Wait Coordination**: Coordinates waiting threads for lock availability
- **Cleanup**: Removes semaphores when no longer needed
- **Notification**: Receives unlock notifications and releases waiting threads

#### UnlockMessageListenerManager

The `UnlockMessageListenerManager` manages Redis Pub/Sub for unlock notifications:

- **Listener Registration**: Creates and registers message listeners for each bucket
- **Channel Management**: Manages Redis Pub/Sub channels
- **Message Publishing**: Publishes unlock messages when locks are released
- **Listener Cleanup**: Unregisters listeners when no longer needed

#### UnlockMessageListener

The `UnlockMessageListener` processes Redis Pub/Sub messages:

- **Message Processing**: Parses unlock messages
- **Notification**: Notifies waiting threads via `LockSemaphoreHolder`
- **Channel Subscription**: Subscribes to specific unlock channels

### 4. Lease Extension

#### LockWatchdog

The `LockWatchdog` automatically extends lock leases to prevent expiration:

- **Lock Registration**: Registers locks for automatic lease extension
- **Lease Refresh**: Periodically refreshes lock leases
- **Scheduling**: Manages refresh schedule based on lease times
- **Cleanup**: Unregisters locks when explicitly released
- **Metrics**: Reports lease extension metrics

### 5. Error Handling

#### RedisLockErrorHandler

The `RedisLockErrorHandler` translates Redis errors to specific exceptions:

- **Error Translation**: Converts Redis errors to typed exceptions
- **Context Enrichment**: Adds contextual information to exceptions
- **Script Error Handling**: Handles Lua script execution errors
- **Structured Logging**: Provides markers for structured logging

### 6. Monitoring

#### LockMonitor

The `LockMonitor` collects metrics for lock operations:

- **Acquisition Metrics**: Records lock acquisition attempts, success rates, and durations
- **Release Metrics**: Records lock release operations and durations
- **Lease Metrics**: Records lease extension attempts and success rates
- **Error Metrics**: Records lock-related errors by type
- **Timeout Metrics**: Records lock acquisition timeouts

### 7. Owner Identification

#### LockOwnerSupplier

The `LockOwnerSupplier` interface provides lock ownership identification:

- **Owner ID Generation**: Generates unique IDs for lock ownership
- **Client Identification**: Identifies the client application
- **Thread Identification**: Includes thread information for debugging

#### DefaultLockOwnerSupplier

The `DefaultLockOwnerSupplier` implements `LockOwnerSupplier` with:

- **Client ID**: Includes hostname and process ID
- **Thread ID**: Includes thread name and ID
- **Request ID**: Optionally includes request ID from MDC
- **Format**: `client:{clientId}:thread:{threadId}[:request:{requestId}]`

### 8. Lock Factory

#### RedisLockFactory

The `RedisLockFactory` creates lock instances with appropriate configuration:

- **Lock Creation**: Creates different lock types with consistent configuration
- **Configuration Resolution**: Resolves configuration from properties
- **Component Injection**: Injects required components into lock instances
- **Bucket Management**: Handles lock bucket configuration

## Service Interactions

### Lock Acquisition Flow

```
1. Client → RedisLockFactory.createReentrantLock() → RedisReentrantLock
2. Client → RedisReentrantLock.lock()
   → AbstractRedisLock.acquireLock()
     → RedisLockOperations.tryLock()
       → ScriptLoader.executeScript("try_lock.lua")
         → ClusterCommandExecutor.executeScriptCommand()
           → Redis EVALSHA command
   → (If successful) LockWatchdog.registerLock()
   → (If unsuccessful) LockSemaphoreHolder.getSemaphore().tryAcquire()
     → Wait for Pub/Sub notification
     → UnlockMessageListener.onMessage()
       → LockSemaphoreHolder.notifyUnlock()
         → Semaphore.release()
```

### Lock Release Flow

```
1. Client → RedisReentrantLock.unlock()
   → AbstractRedisLock.releaseLock()
     → LockWatchdog.unregisterLock()
     → RedisLockOperations.unlock()
       → ScriptLoader.executeScript("unlock.lua")
         → ClusterCommandExecutor.executeScriptCommand()
           → Redis EVALSHA command
           → Redis PUBLISH to unlock channel
```

### Lease Extension Flow

```
1. LockWatchdog.scheduleRefresh() (periodic task)
   → For each registered lock:
     → RedisLockOperations.tryLock() (extends lease)
       → ScriptLoader.executeScript("try_lock.lua")
         → ClusterCommandExecutor.executeScriptCommand()
           → Redis EVALSHA command
```

## Extension Points

The module provides several extension points for customization:

### 1. Custom Lock Owner Supplier

You can provide a custom implementation of `LockOwnerSupplier` to customize lock ownership identification:

```java
@Bean
@ConditionalOnMissingBean
public LockOwnerSupplier customLockOwnerSupplier() {
    return new CustomLockOwnerSupplier();
}
```

### 2. Custom Error Handler

You can provide a custom implementation of `RedisLockErrorHandler` to customize exception translation:

```java
@Bean
@ConditionalOnMissingBean
public RedisLockErrorHandler customLockErrorHandler() {
    return new CustomLockErrorHandler();
}
```

### 3. Custom Lock Monitor

You can provide a custom implementation of `LockMonitor` to customize metrics collection:

```java
@Bean
@ConditionalOnMissingBean
public LockMonitor customLockMonitor() {
    return new CustomLockMonitor();
}
```

### 4. Custom Lock Implementations

You can provide custom lock implementations by extending the abstract base classes:

```java
@Bean
@ConditionalOnMissingBean
public RedisReentrantLock customReentrantLock(RedisLockOperations operations, /* other dependencies */) {
    return new CustomRedisReentrantLock(operations, /* other dependencies */);
}
```

## Operational Aspects

### 1. Metrics

The module provides the following metrics via Micrometer:

- **Lock Acquisition**: `destilink.lock.acquisition` (count, success rate, duration)
- **Lock Release**: `destilink.lock.release` (count, duration)
- **Lease Extension**: `destilink.lock.lease.refresh` (count, success rate)
- **Lock Errors**: `destilink.lock.error` (count by error type)
- **Lock Timeouts**: `destilink.lock.timeout` (count)

### 2. Logging

The module uses structured logging with the following context:

- **Lock Key**: The key of the lock being operated on
- **Lock Owner**: The ID of the lock owner
- **Lock Bucket**: The bucket the lock belongs to
- **Operation**: The operation being performed (acquire, release, refresh)
- **Duration**: The duration of the operation
- **Result**: The result of the operation (success, failure)

### 3. Health Checks

The module provides health checks for:

- **Redis Connectivity**: Checks if Redis is reachable
- **Script Loading**: Checks if Lua scripts can be loaded
- **Lock Operations**: Checks if basic lock operations work

### 4. Resilience

The module implements several resilience patterns:

- **Retry**: Retries lock acquisition with configurable count and delay
- **Timeout**: Configurable timeouts for lock acquisition
- **Circuit Breaking**: Fails fast when Redis is unavailable
- **Fallback**: Provides fallback behavior for lock operations
- **Graceful Degradation**: Continues operation when non-critical components fail