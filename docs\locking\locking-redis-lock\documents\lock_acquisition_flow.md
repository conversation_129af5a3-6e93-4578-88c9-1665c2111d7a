# Redis Locking Module: Lock Acquisition Flow

## 1. Introduction

This document describes the process by which locks are acquired in the `locking-redis-lock` module. The mechanism is designed to be efficient, relying on Redis Pub/Sub for unlock notifications to avoid busy-waiting (polling), while also handling potential missed notifications through TTL-based retries.

The core logic is implemented in [`AbstractRedisLock.java`](../src/main/java/com/tui/destilink/framework/locking/redis/lock/AbstractRedisLock.java:1) and utilizes [`UnlockMessageListener.java`](../src/main/java/com/tui/destilink/framework/locking/redis/messaging/UnlockMessageListener.java:1) and [`LockSemaphoreHolder.java`](../src/main/java/com/tui/destilink/framework/locking/redis/lock/LockSemaphoreHolder.java:1).

## 2. Lock Acquisition Scenarios

There are two primary scenarios for blocking lock acquisition:

*   **Infinite Wait**: The thread waits indefinitely until the lock is acquired (e.g., via `lock.lock()`).
*   **Timeout**: The thread waits up to a specified timeout for the lock (e.g., via `lock.tryLock(long time, TimeUnit unit)`).

### 2.1. Infinite Wait Scenario

This scenario applies when a method like `RedisLock.lock()` is called.

```mermaid
flowchart TD
    A["Start: Acquire Lock (e.g., lock.lock())"] --> B["Generate Unique Request ID (UUID)"]
    B --> C["Loop (Until Lock Acquired)"]
    C --> D["Call AbstractRedisLock.tryAcquireLock(requestUuid, ownerId)<br/>(Executes Lua script for specific lock type)"]
    D --> E{"Lock Acquired by Script?"}
    E -- "Yes (Script returns null/success)" --> F["Register with LockWatchdog (if leaseTime > 0)"]
    F --> G["Return (Lock Acquired)"]
    E -- "No (Script returns TTL of current holder)" --> H["Get/Create LockSemaphoreHolder for lockKey<br/>(via UnlockMessageListener.registerForUnlockNotification)"]
    H --> H_INC["Increment Waiters on LockSemaphoreHolder"]
    H_INC --> I["Calculate Wait Duration:<br/>min(Returned TTL, Retry Interval, Remaining Overall Timeout (if applicable))"]
    I --> J["Wait on LockSemaphoreHolder's Semaphore<br/>(holder.waitForUnlock(calculatedWaitTime))"]
    J --> K{"Notified by UnlockMessageListener OR Wait Timed Out?"}
    K -- "Yes/No" --> L["Decrement Waiters & Unregister from UnlockMessageListener<br/>(if holder.getWaiters() <=0, remove holder)"]
    L --> C
```

**Process Description (Infinite Wait):**

1.  **Initiate Acquisition**: The `lock()` method in a concrete lock implementation (e.g., `RedisReentrantLock`) typically delegates to `AbstractRedisLock.lock()`.
2.  **Loop Indefinitely**: `AbstractRedisLock.lock()` enters a loop that continues until the lock is successfully acquired.
3.  **Attempt Atomic Acquisition**:
    *   Inside the loop, a unique `requestUuid` is generated for the current attempt.
    *   The abstract method `tryAcquireLock(requestUuid, lockOwnerId)` is called. Concrete subclasses implement this to execute a specific Lua script (e.g., "try\_lock" for `RedisReentrantLock`) via `RedisLockOperations`. This script attempts to acquire the lock atomically.
4.  **Check Result**:
    *   **Success**: If `tryAcquireLock` returns `null` (or a success indicator), the lock is acquired.
        *   The lock is registered with the `LockWatchdog` (if its `leaseTime` is positive, indicating it's not an infinitely held lock without renewal).
        *   The method returns, and the application can proceed with the critical section.
    *   **Failure (Lock Held by Another)**: If `tryAcquireLock` returns a positive value (typically the TTL of the current lock holder), the lock is not acquired.
5.  **Wait for Notification/Retry**:
    *   A `LockSemaphoreHolder` is obtained (or created if it's the first waiter for this `lockKey`) from the `UnlockMessageListener` for the lock's bucket and specific `lockKey`.
    *   The waiter count on the `LockSemaphoreHolder` is incremented.
    *   The current thread calls `holder.waitForUnlock(waitTimeMs)`. The `waitTimeMs` is calculated based on the TTL returned by the script or the configured `retryInterval`. The `waitForUnlock` method in `LockSemaphoreHolder` first drains any stale permits from its internal `Semaphore` and then attempts to acquire a permit within `waitTimeMs`.
6.  **Post-Wait**:
    *   Regardless of whether the wait was ended by a notification (semaphore signaled by `UnlockMessageListener`) or by timeout, the waiter count on the `LockSemaphoreHolder` is decremented. If it becomes zero, the holder might be removed from the `UnlockMessageListener`'s map.
    *   The loop continues, and another attempt to acquire the lock (Step 3) is made. A new `requestUuid` is generated for this new attempt.
7.  **Interruption**: If the thread is interrupted during the wait, `AbstractRedisLock.lock()` will preserve the interrupt status and re-throw an appropriate exception (e.g., `LockAcquisitionException` wrapping an `InterruptedException`).

### 2.2. Timeout Scenario

This scenario applies when a method like `RedisLock.tryLock(long time, TimeUnit unit)` is called.

```mermaid
flowchart TD
    A["Start: Acquire Lock (e.g., lock.tryLock(timeout))"] --> B["Calculate Deadline (startTime + timeout)"]
    B --> C["Register Semaphore/CompletableFuture with UnlockMessageListener"]
    C --> D["Loop (While currentTime < Deadline)"]
    D --> E["Issue Direct Polling Lock Request (Lua Script)"]
    E --> F{"Lock Acquired?"}
    F -- "Yes" --> G["Lock Acquired"]
    G --> H["Unregister Semaphore/CompletableFuture"]
    H --> Z["End (Return True)"]
    F -- "No" --> I["Lock Locked, Return TTL"]
    I --> J["Calculate Wait Time (min of TTL, RetryInterval, RemainingOverallTimeout)"]
    J --> L{"Wait Time <= 0?"}
    L -- "Yes (Overall Timeout Reached)" --> M["Stop Trying"]
    M --> H
    L -- "No" --> N["Wait on Semaphore/CompletableFuture up to Calculated Wait Time"]
    N --> O{"Unlock Message Received OR Wait Timed Out?"}
    O --> D  // Loop back to try acquiring again
    D -- "Loop Condition: currentTime >= Deadline" --> M // Explicit check for overall timeout
```

**Process Description (Timeout Scenario):**

The flow is very similar to the infinite wait scenario, with the primary difference being an overall `deadline` for the acquisition attempt.

1.  **Initiate Acquisition & Deadline**: The `tryLock(long time, TimeUnit unit)` method in `AbstractRedisLock` calculates a `deadline` based on the current time and the provided `time` and `unit`.
2.  **Loop with Deadline**: The acquisition loop continues as long as `System.currentTimeMillis() < deadline`.
3.  **Attempt Atomic Acquisition**: Same as in the infinite wait scenario (Step 3 above).
4.  **Check Result**:
    *   **Success**: If `tryAcquireLock` indicates success:
        *   The lock is registered with the `LockWatchdog` (if applicable).
        *   The method returns `true`.
    *   **Failure (Lock Held by Another)**: If `tryAcquireLock` indicates failure and returns the current holder's TTL.
5.  **Wait for Notification/Retry (with Timeout Consideration)**:
    *   A `LockSemaphoreHolder` is obtained and waiter count incremented.
    *   The `waitTimeMs` for `holder.waitForUnlock()` is calculated. It's the minimum of:
        *   The TTL returned by the script.
        *   The configured `retryInterval`.
        *   The **remaining time until the overall `deadline`**.
    *   If `waitTimeMs` is less than or equal to 0 (meaning the overall deadline has been reached or is imminent), the loop breaks, and the method will return `false`.
    *   The thread waits on the semaphore.
6.  **Post-Wait**:
    *   Waiter count is decremented, and the holder potentially removed.
    *   The loop continues (back to Step 3) if the `deadline` has not been reached.
7.  **Timeout**: If the loop condition (`System.currentTimeMillis() < deadline`) fails, the method returns `false`, indicating the lock was not acquired within the specified timeout.
8.  **Interruption**: If the thread is interrupted, `InterruptedException` is thrown.

## 3. Key Components in the Flow

*   **[`AbstractRedisLock.java`](../src/main/java/com/tui/destilink/framework/locking/redis/lock/AbstractRedisLock.java:1)**: Orchestrates the acquisition loop, calls `tryAcquireLock` (implemented by subclasses), and manages waiting using `UnlockMessageListener` and `LockSemaphoreHolder`.
*   **Concrete Lock Implementations (e.g., [`RedisReentrantLock.java`](../src/main/java/com/tui/destilink/framework/locking/redis/lock/RedisReentrantLock.java:1))**: Provide the specific Lua scripts for `tryAcquireLock` and `releaseLock` tailored to their locking semantics (e.g., reentrancy).
*   **[`RedisLockOperations.java`](../src/main/java/com/tui/destilink/framework/locking/redis/core/RedisLockOperations.java:1)**: Executes the Lua scripts on Redis.
*   **[`UnlockMessageListener.java`](../src/main/java/com/tui/destilink/framework/locking/redis/messaging/UnlockMessageListener.java:1)**: Subscribes to Redis Pub/Sub channels for unlock messages. When a message is received, it signals the appropriate `LockSemaphoreHolder`.
*   **[`LockSemaphoreHolder.java`](../src/main/java/com/tui/destilink/framework/locking/redis/lock/LockSemaphoreHolder.java:1)**: Contains a `java.util.concurrent.Semaphore`. Threads waiting for a specific lock will attempt to acquire a permit from this semaphore. The `waitForUnlock` method crucially drains stale permits before waiting.
*   **Lua Scripts**: Atomically attempt to acquire or release locks on the Redis server. The "try acquire" scripts typically return `null` or `1` on success, or the TTL of the existing lock if held by another. The "unlock" script publishes a message to the Pub/Sub channel.

This non-polling, notification-driven approach with a TTL-based retry fallback ensures efficient and robust distributed lock acquisition.