package com.tui.destilink.framework.locking.redis.lock.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import static org.assertj.core.api.Assertions.*;

/**
 * Unit tests for RedisLockContextDecorator to verify MDC context management
 * for Redis lock operations.
 */
class RedisLockContextDecoratorTest {

    @BeforeEach
    void setUp() {
        // Clear any existing MDC context
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        // Clean up
        MDC.clear();
    }

    @Test
    void create_shouldReturnNewInstance() {
        // When
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // Then
        assertThat(decorator).isNotNull();
    }

    @Test
    void lockKey_shouldSetAndGetLockKey() {
        // Given
        String testKey = "user:123:profile";
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockKey(testKey);
        
        // Then
        assertThat(decorator.getLockKey()).isEqualTo(testKey);
        assertThat(MDC.get("contextMap.lock.lock.key")).isEqualTo(testKey);
    }

    @Test
    void lockOperation_shouldSetAndGetLockOperation() {
        // Given
        String operation = "acquire";
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockOperation(operation);
        
        // Then
        assertThat(decorator.getLockOperation()).isEqualTo(operation);
        assertThat(MDC.get("contextMap.lock.lock.operation")).isEqualTo(operation);
    }

    @Test
    void lockOwner_shouldSetAndGetLockOwner() {
        // Given
        String owner = "service-instance-123";
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockOwner(owner);
        
        // Then
        assertThat(decorator.getLockOwner()).isEqualTo(owner);
        assertThat(MDC.get("contextMap.lock.lock.owner")).isEqualTo(owner);
    }

    @Test
    void lockBucket_shouldSetAndGetLockBucket() {
        // Given
        String bucket = "user-locks";
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockBucket(bucket);
        
        // Then
        assertThat(decorator.getLockBucket()).isEqualTo(bucket);
        assertThat(MDC.get("contextMap.lock.lock.bucket")).isEqualTo(bucket);
    }

    @Test
    void lockTtl_shouldSetAndGetLockTtl() {
        // Given
        Long ttl = 30000L;
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockTtl(ttl);
        
        // Then
        assertThat(decorator.getLockTtl()).isEqualTo(ttl);
        assertThat(MDC.get("contextMap.lock.lock.ttl")).isEqualTo(ttl.toString());
    }

    @Test
    void lockRetryCount_shouldSetAndGetLockRetryCount() {
        // Given
        Integer retryCount = 3;
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockRetryCount(retryCount);
        
        // Then
        assertThat(decorator.getLockRetryCount()).isEqualTo(retryCount);
        assertThat(MDC.get("contextMap.lock.lock.retryCount")).isEqualTo(retryCount.toString());
    }

    @Test
    void lockExecutionTime_shouldSetAndGetLockExecutionTime() {
        // Given
        Long executionTime = 150L;
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When
        decorator.lockExecutionTime(executionTime);
        
        // Then
        assertThat(decorator.getLockExecutionTime()).isEqualTo(executionTime);
        assertThat(MDC.get("contextMap.lock.lock.executionTime")).isEqualTo(executionTime.toString());
    }

    @Test
    void fluentApi_shouldAllowMethodChaining() {
        // Given
        String key = "user:123:profile";
        String operation = "acquire";
        String owner = "service-instance-123";
        String bucket = "user-locks";
        Long ttl = 30000L;
        Integer retryCount = 3;
        Long executionTime = 150L;
        
        // When
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create()
            .lockKey(key)
            .lockOperation(operation)
            .lockOwner(owner)
            .lockBucket(bucket)
            .lockTtl(ttl)
            .lockRetryCount(retryCount)
            .lockExecutionTime(executionTime);
        
        // Then
        assertThat(decorator.getLockKey()).isEqualTo(key);
        assertThat(decorator.getLockOperation()).isEqualTo(operation);
        assertThat(decorator.getLockOwner()).isEqualTo(owner);
        assertThat(decorator.getLockBucket()).isEqualTo(bucket);
        assertThat(decorator.getLockTtl()).isEqualTo(ttl);
        assertThat(decorator.getLockRetryCount()).isEqualTo(retryCount);
        assertThat(decorator.getLockExecutionTime()).isEqualTo(executionTime);
    }

    @Test
    void createClosableScope_shouldManageContextLifecycle() throws Exception {
        // Given
        String key = "user:123:profile";
        String operation = "acquire";
        String owner = "service-instance-123";
        
        // When
        try (var scope = RedisLockContextDecorator.create()
                .lockKey(key)
                .lockOperation(operation)
                .lockOwner(owner)
                .createClosableScope()) {
            
            // Then - context should be set
            assertThat(MDC.get("contextMap.lock.lock.key")).isEqualTo(key);
            assertThat(MDC.get("contextMap.lock.lock.operation")).isEqualTo(operation);
            assertThat(MDC.get("contextMap.lock.lock.owner")).isEqualTo(owner);
        }
        
        // After scope - context should be cleared
        assertThat(MDC.get("contextMap.lock.lock.key")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.operation")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.owner")).isNull();
    }

    @Test
    void clear_shouldRemoveAllLockContext() {
        // Given
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create()
            .lockKey("user:123:profile")
            .lockOperation("acquire")
            .lockOwner("service-instance-123")
            .lockBucket("user-locks");
        
        // Verify context is set
        assertThat(MDC.get("contextMap.lock.lock.key")).isNotNull();
        assertThat(MDC.get("contextMap.lock.lock.operation")).isNotNull();
        
        // When
        decorator.clear();
        
        // Then
        assertThat(MDC.get("contextMap.lock.lock.key")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.operation")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.owner")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.bucket")).isNull();
    }

    @Test
    void operations_forAcquire_shouldCreateConfiguredDecorator() {
        // Given
        String key = "user:123:profile";
        String owner = "service-instance-123";
        String bucket = "user-locks";
        
        // When
        RedisLockContextDecorator decorator = RedisLockContextDecorator.Operations.forAcquire(key, owner, bucket);
        
        // Then
        assertThat(decorator.getLockKey()).isEqualTo(key);
        assertThat(decorator.getLockOperation()).isEqualTo(RedisLockContextDecorator.Operations.ACQUIRE);
        assertThat(decorator.getLockOwner()).isEqualTo(owner);
        assertThat(decorator.getLockBucket()).isEqualTo(bucket);
    }

    @Test
    void operations_forRelease_shouldCreateConfiguredDecorator() {
        // Given
        String key = "user:123:profile";
        String owner = "service-instance-123";
        String bucket = "user-locks";
        
        // When
        RedisLockContextDecorator decorator = RedisLockContextDecorator.Operations.forRelease(key, owner, bucket);
        
        // Then
        assertThat(decorator.getLockKey()).isEqualTo(key);
        assertThat(decorator.getLockOperation()).isEqualTo(RedisLockContextDecorator.Operations.RELEASE);
        assertThat(decorator.getLockOwner()).isEqualTo(owner);
        assertThat(decorator.getLockBucket()).isEqualTo(bucket);
    }

    @Test
    void operations_forExtend_shouldCreateConfiguredDecorator() {
        // Given
        String key = "user:123:profile";
        String owner = "service-instance-123";
        String bucket = "user-locks";
        Long ttl = 30000L;
        
        // When
        RedisLockContextDecorator decorator = RedisLockContextDecorator.Operations.forExtend(key, owner, bucket, ttl);
        
        // Then
        assertThat(decorator.getLockKey()).isEqualTo(key);
        assertThat(decorator.getLockOperation()).isEqualTo(RedisLockContextDecorator.Operations.EXTEND);
        assertThat(decorator.getLockOwner()).isEqualTo(owner);
        assertThat(decorator.getLockBucket()).isEqualTo(bucket);
        assertThat(decorator.getLockTtl()).isEqualTo(ttl);
    }

    @Test
    void operations_constants_shouldHaveCorrectValues() {
        // Then
        assertThat(RedisLockContextDecorator.Operations.ACQUIRE).isEqualTo("acquire");
        assertThat(RedisLockContextDecorator.Operations.RELEASE).isEqualTo("release");
        assertThat(RedisLockContextDecorator.Operations.EXTEND).isEqualTo("extend");
        assertThat(RedisLockContextDecorator.Operations.REFRESH).isEqualTo("refresh");
        assertThat(RedisLockContextDecorator.Operations.CHECK).isEqualTo("check");
        assertThat(RedisLockContextDecorator.Operations.FORCE_RELEASE).isEqualTo("force_release");
    }

    @Test
    void contextIsolation_shouldMaintainSeparateContexts() throws Exception {
        // Given
        String key1 = "user:123:profile";
        String key2 = "user:456:settings";
        
        // When - create nested scopes
        try (var outerScope = RedisLockContextDecorator.create()
                .lockKey(key1)
                .lockOperation("acquire")
                .createClosableScope()) {
            
            // Verify outer context
            assertThat(MDC.get("contextMap.lock.lock.key")).isEqualTo(key1);
            assertThat(MDC.get("contextMap.lock.lock.operation")).isEqualTo("acquire");
            
            try (var innerScope = RedisLockContextDecorator.create()
                    .lockKey(key2)
                    .lockOperation("release")
                    .createClosableScope()) {
                
                // Verify inner context overrides outer
                assertThat(MDC.get("contextMap.lock.lock.key")).isEqualTo(key2);
                assertThat(MDC.get("contextMap.lock.lock.operation")).isEqualTo("release");
            }
            
            // Verify outer context is restored
            assertThat(MDC.get("contextMap.lock.lock.key")).isEqualTo(key1);
            assertThat(MDC.get("contextMap.lock.lock.operation")).isEqualTo("acquire");
        }
        
        // Verify all context is cleared
        assertThat(MDC.get("contextMap.lock.lock.key")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.operation")).isNull();
    }

    @Test
    void nullValues_shouldBeHandledGracefully() {
        // Given
        RedisLockContextDecorator decorator = RedisLockContextDecorator.create();
        
        // When - set null values
        decorator.lockKey(null)
                .lockOperation(null)
                .lockOwner(null)
                .lockBucket(null)
                .lockTtl(null)
                .lockRetryCount(null)
                .lockExecutionTime(null);
        
        // Then - getters should return null
        assertThat(decorator.getLockKey()).isNull();
        assertThat(decorator.getLockOperation()).isNull();
        assertThat(decorator.getLockOwner()).isNull();
        assertThat(decorator.getLockBucket()).isNull();
        assertThat(decorator.getLockTtl()).isNull();
        assertThat(decorator.getLockRetryCount()).isNull();
        assertThat(decorator.getLockExecutionTime()).isNull();
        
        // And MDC should not have the keys set
        assertThat(MDC.get("contextMap.lock.lock.key")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.operation")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.owner")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.bucket")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.ttl")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.retryCount")).isNull();
        assertThat(MDC.get("contextMap.lock.lock.executionTime")).isNull();
    }

    @Test
    void integrationWithVirtualThreadContextUtils_shouldPropagateContext() throws Exception {
        // Given
        String key = "user:123:profile";
        String operation = "acquire";
        String owner = "service-instance-123";
        
        // Set up context
        try (var scope = RedisLockContextDecorator.create()
                .lockKey(key)
                .lockOperation(operation)
                .lockOwner(owner)
                .createClosableScope()) {
            
            // When - capture context for Virtual Thread propagation
            var contextMap = VirtualThreadContextUtils.captureContext();
            
            // Then - context should be captured
            assertThat(contextMap).containsEntry("contextMap.lock.lock.key", key);
            assertThat(contextMap).containsEntry("contextMap.lock.lock.operation", operation);
            assertThat(contextMap).containsEntry("contextMap.lock.lock.owner", owner);
            
            // And restoration should work
            MDC.clear();
            try (var restoreScope = VirtualThreadContextUtils.restoreContext(contextMap)) {
                assertThat(MDC.get("contextMap.lock.lock.key")).isEqualTo(key);
                assertThat(MDC.get("contextMap.lock.lock.operation")).isEqualTo(operation);
                assertThat(MDC.get("contextMap.lock.lock.owner")).isEqualTo(owner);
            }
        }
    }
}